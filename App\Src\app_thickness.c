/**
  ********************************************************************************
  * Copyright (C), 2018-2023, 苏州博昇科技有限公司, www.phaserise.com  // 版权信息：苏州博昇科技有限公司
  * @file    : main.c                                                    // 文件名：main.c（实际应为app_thickness.c）
  * @project : 项目名称（主要文件必选，次要文件可选）                      // 项目名称字段
  * @brief   : 介绍                                                      // 文件简介字段
  * <AUTHOR> PR Team                                                   // 作者：PR团队
  * @date    : 2022/08/08                                                // 创建日期：2022年8月8日
  * @version : V1.0                                                      // 版本号：V1.0
  * @history :                                                           // 版本历史记录
  *	2022/08/08:                                                        // 2022年8月8日版本
  *	  1) 首次创建;                                                      // 首次创建文件
  *	2023/01/18:                                                        // 2023年1月18日版本
  *	  1）新增波形采集时修复的功能(实验用);                              // 新增实验性波形修复功能
  *
  ********************************************************************************
  * @note                                                                // 注意事项字段
  *
  ********************************************************************************
  * @attention                                                           // 特别注意字段
  *
  ********************************************************************************
  */
#include "app_thickness.h"     // 包含厚度测量应用头文件，定义了厚度测量相关的结构体和函数声明
#include "app_cali.h"          // 包含校准应用头文件，提供声速和零点校准功能
#include "app_gui.h"           // 包含GUI应用头文件，提供用户界面显示功能
#include "emat.h"              // 包含电磁超声换能器头文件，控制EMAT发射和接收参数
#include "thk.h"               // 包含测厚算法头文件，提供厚度计算的核心算法
//#include "single_peak.h"     // 单峰算法头文件（已注释，可能是旧版本算法）
#include "oled.h"              // 包含OLED显示屏头文件，控制液晶显示
#include "delay.h"             // 包含延时函数头文件，提供毫秒级延时功能
#include "app.h"               // 包含应用程序主头文件，定义全局设备状态和配置
#include "adc.h"               // 包含模数转换器头文件，控制ADC采样参数
#include "dac.h"               // 包含数模转换器头文件，控制DAC输出波形
#include "fpga.h"              // 包含FPGA控制头文件，管理FPGA硬件资源和配置
#include "wifi.h"              // 包含WiFi通信头文件，处理无线数据传输
#include "bsp_zpt.h"           // 包含零点时间板级支持包头文件，管理零点时间补偿
#include "ui_draw_wave.h"      // 包含波形绘制界面头文件，在OLED上显示超声波形

THICK_FUNCTION_T 	g_Thk;     // 全局厚度测量功能结构体变量，存储所有厚度测量相关的参数、状态和数据缓冲区

/**
  * @brief  参数初始化                                                   // 函数功能：初始化厚度测量模块的所有参数
			波形数组在 app.c 中统一管理                                  // 说明：波形数据缓冲区由app.c文件统一分配和管理
  * @param  None                                                        // 输入参数：无
  * @retval None                                                        // 返回值：无
  */
void App_Thk_Init(void)                                                  // 厚度测量应用初始化函数
{
	Emat_SetFs(50);                                                      // 设置EMAT采样频率为50MHz，这是超声波数字化的基础频率

	//波形数组在 app.c 中统一管理                                        // 注释：说明内存管理策略
	g_Thk.wave.waveValid	= 0;                                         // 设置普通波形数据有效标志为0（无效），表示当前没有有效的波形数据
	g_Thk.wave.fs			= 50;                                        // 设置普通波形采样频率为50MHz，与EMAT设置保持一致
	g_Thk.wave.len_pt 		= THK_WAVE_LEN;                              // 设置普通波形数据点长度为THK_WAVE_LEN（通常为4096点）

	g_Thk.longWave.waveValid	= 0;                                     // 设置长距离波形数据有效标志为0（无效）
	g_Thk.longWave.fs		= 50;                                        // 设置长距离波形采样频率为50MHz
	g_Thk.longWave.len_pt 	= THK_LONG_WAVE_LEN;                         // 设置长距离波形数据点长度为THK_LONG_WAVE_LEN（通常为32768点，用于长距离测量）

	g_Thk.resValid = 0;                                                  // 设置测量结果有效标志为0（无效），表示当前没有有效的厚度测量结果
	g_Thk.thkResValUm = 0;                                               // 设置厚度测量结果值为0微米，初始化测量结果

	g_Thk.supplyEmitEnable	= 0;                                         // 设置补充发射使能标志为0（禁用），用于控制是否进行二次发射测量以提高精度
}

uint8_t Thk_isLongMode(void) {                                          // 查询是否为长距离测量模式的函数
	return g_Thk.longMode;                                               // 返回长距离模式标志，1表示长距离模式，0表示普通模式
}


static uint32_t CalcI16ArrayMaxValIdx(int16_t *buf, uint32_t len) {    // 静态函数：计算16位整数数组中最大值的索引位置
	int16_t max_tmp;                                                     // 临时变量：存储当前找到的最大值
	uint32_t max_idx;                                                    // 变量：存储最大值对应的数组索引

	max_tmp = buf[0];                                                    // 初始化：将数组第一个元素作为初始最大值
	max_idx = 0;                                                         // 初始化：将索引0作为初始最大值位置
	for(uint32_t i = 0; i < len; i++) {                                  // 遍历整个数组，从索引0到len-1
		if(buf[i] >= max_tmp) {                                          // 如果当前元素大于或等于当前最大值（使用>=确保找到最后一个最大值）
			max_idx = i;                                                 // 更新最大值索引为当前位置
			max_tmp = buf[i];                                            // 更新最大值为当前元素值
		}
	}
	return max_idx;                                                      // 返回最大值在数组中的索引位置，用于定位超声波形中的主峰位置
}

static int32_t CalcRealPeakIdx_forFirstPeakWeak(int16_t *buf, uint32_t len, uint32_t max_i) { // 静态函数：当首个回波峰值较弱时，计算真实峰值位置的智能算法
	int32_t i, i0;                                                       // 循环变量i和起始位置变量i0
	int16_t max_val = buf[max_i];                                        // 获取当前找到的最大值，作为参考基准
	uint32_t range = 512;                                                // 定义搜索范围为512个采样点，约10.24微秒的时间窗口（50MHz采样）

	uint32_t max_i_1p2, max_i_1p3;                                      // 分别存储1/2位置和1/3位置的最大值索引
	int16_t max_v_1p2, max_v_1p3;                                       // 分别存储1/2位置和1/3位置的最大值

	//计算 1/2 处最大值                                                  // 注释：在时间轴1/2位置附近搜索可能的真实峰值
	i0 = (max_i/2 > range/2) ? (max_i/2 - range/2) : 0;                 // 计算1/2位置搜索的起始点，确保不越界（不小于0）
	max_i_1p2 = CalcI16ArrayMaxValIdx(buf + i0, range) + i0;            // 在1/2位置附近的range范围内找最大值索引，并加上偏移量得到绝对位置
	max_v_1p2 = buf[max_i_1p2];                                         // 获取1/2位置处的最大值

	//计算 1/3 处最大值                                                  // 注释：在时间轴1/3位置附近搜索可能的真实峰值
	i0 = (max_i/3 > range/2) ? (max_i/3 - range/2) : 0;                 // 计算1/3位置搜索的起始点，确保不越界（不小于0）
	max_i_1p3 = CalcI16ArrayMaxValIdx(buf + i0, range) + i0;            // 在1/3位置附近的range范围内找最大值索引，并加上偏移量得到绝对位置
	max_v_1p3 = buf[max_i_1p3];                                         // 获取1/3位置处的最大值

	if(max_v_1p3 > max_val/2)                                           // 如果1/3位置的峰值大于原峰值的一半（说明可能是真实的首次回波）
		return max_i_1p3;                                               // 返回1/3位置的峰值索引，这可能是被噪声掩盖的真实首次回波
	else if(max_v_1p2 > max_val/2)                                      // 否则如果1/2位置的峰值大于原峰值的一半
		return max_i_1p2;                                               // 返回1/2位置的峰值索引，这可能是真实的首次回波
	else                                                                 // 如果前面的位置都没有足够强的峰值
		return max_i;                                                   // 返回原始找到的最大值位置，保持原有结果
}

/**
  * @brief  选波采集                                                     // 函数功能：长距离测厚模式的波形采集和处理
  * @param  wave: 波形数据结构体指针                                     // 输入参数：指向波形数据结构的指针，包含缓冲区和参数信息
  * @param  vel: 超声波在材料中的传播速度（m/s）                         // 输入参数：声速值，用于计算厚度
  * @retval 返回计算得到的厚度值（微米）                                 // 返回值：厚度测量结果，单位为微米
  */
uint32_t Thk_MeasureThk_Long(WAVE_T *wave, uint32_t vel) {              // 长距离测厚函数，适用于厚壁或长距离测量场景
	uint32_t		blind_us = 20;                                       // 盲区时间设置为20微秒，避免发射余震和近场干扰影响测量
	uint32_t		thickness;                                           // 变量：存储计算得到的厚度值
	uint16_t 		fs;                                                  // 变量：采样频率，从波形结构体中获取
	float			fe;                                                  // 变量：激励频率，EMAT的工作频率
	uint32_t 		i, j, max_v, max_i;                                  // 循环变量和临时变量：用于数据处理和峰值搜索

	fs = wave->fs;                                                       // 获取波形采样频率（MHz），通常为50MHz
	g_Daq.fs_MHz = fs;                                                   // 将采样频率同步到全局数据采集结构体中
	fe = g_Emat.fe_MHz;                                                  // 获取EMAT激励频率（MHz），通常为3.85MHz
//	Emat_SetAvg(128);                                                    // 设置平均次数为128（已注释，可能在其他地方设置）

	//拼接采集                                                           // 注释：执行拼接式波形采集，适用于长距离测量
	DAQ_AcqJointWave(wave->buf, wave->len_pt, 1);                       // 调用拼接采集函数，获取长距离波形数据，参数1表示采集模式
	wave->max_i	= CalcI16ArrayMaxValIdx(wave->buf + blind_us*fs, wave->len_pt - blind_us*fs) + blind_us*fs; // 在盲区之后的数据中找最大值位置，并加上盲区偏移得到绝对位置
	wave->max_val	= wave->buf[wave->max_i];                            // 获取最大值对应的幅度值，即主峰幅度

	//判断是否倍值                                                       // 注释：检查是否存在倍频现象，修正峰值位置
	wave->max_i 	= CalcRealPeakIdx_forFirstPeakWeak(wave->buf, wave->len_pt, wave->max_i); // 使用智能算法修正峰值位置，处理首次回波较弱的情况
	wave->max_val	= wave->buf[wave->max_i];                            // 更新修正后的峰值幅度

	wave->waveValid = 1;                                                 // 设置波形数据有效标志为1，表示波形数据可用于后续处理
	

	if(g_Dev.controlByApp == 1) {                                        // 如果设备被APP控制（WiFi连接状态）
		//抽数据显示                                                     // 注释：数据抽取用于显示，将长波形压缩为标准长度
		for(i = 0; i < 4096; i++) {                                      // 遍历4096个输出点，这是标准显示波形的长度
			for(j = i*8, max_i = j, max_v = abs(wave->buf[max_i]); j < (i+1)*8; j++) { // 在每8个原始采样点中寻找绝对值最大的点（8:1抽取比例）
				if(abs(wave->buf[j]) > max_v) {                          // 如果当前点的绝对值大于当前最大值
					max_v = abs(wave->buf[j]);                           // 更新最大绝对值
					max_i = j; //极值                                    // 更新极值点的索引位置
				}
			}
			g_Thk.wave.buf[i] = wave->buf[max_i];//wave->buf[i*8];       // 将找到的极值点存入显示缓冲区，保持波形特征
		}
	}

	//计算大概厚度                                                       // 注释：使用单峰算法计算厚度值
//	thickness = CalcSinglePeakThickness(wave->buf, wave->max_i, 1, fs, fe) * vel / (fs * 2); // 旧版单峰算法（已注释）
	thickness = SPK_CalcTOF_withDestPeak(wave->buf, wave->len_pt, wave->max_i, 1, fs, fe, g_Emat.Emit_Rep) * vel / (fs * 2); // 使用新版单峰算法计算飞行时间，然后转换为厚度
	// 厚度计算公式：厚度 = (飞行时间 × 声速) / (采样频率 × 2)
	// 除以2是因为超声波往返传播，实际距离是飞行距离的一半

	if(g_Dev.controlByApp == 1) {                                        // 如果设备被APP控制，需要发送数据到APP
//		//抽数据显示                                                     // 旧版数据抽取代码（已注释）
//		for(i = 0; i < 4096; i++) {                                      // 遍历4096个点（已注释）
//			for(j = i*8, max_i = j, max_v = abs(wave->buf[max_i]); j < (i+1)*8; j++) { // 8:1抽取（已注释）
//				if(abs(wave->buf[j]) > max_v) {                          // 寻找极值（已注释）
//					max_v = abs(wave->buf[j]);                           // 更新最大值（已注释）
//					max_i = j; //极值                                    // 更新索引（已注释）
//				}
//			}
//			wave->buf[i] = wave->buf[max_i];									//此处是否有错误？暂时修改为下行。 // 可能存在的错误（已注释）
//		}
		if(g_Wifi.sendThicknessOnly) {                                   // 如果WiFi配置为仅发送厚度值模式（节省带宽）
			// 只发送厚度值                                              // 注释：轻量级数据传输模式
			SendThicknessValueCmd2App((uint32_t)thickness, 1);           // 发送厚度值到APP，参数1表示测量有效
		} else {                                                         // 否则发送完整数据包（包含波形数据）
			// 发送完整数据包（原有逻辑）                                // 注释：完整数据传输模式
			updatePackageParameter(thickness, g_Daq.fs_MHz, vel, g_Emat.Emit_Rep, 4); // 更新数据包参数：厚度、采样频率、声速、发射次数、数据类型
			Wifi_SendData((uint16_t *)g_Thk.wave.buf, 4096+6); // 4104*2 = 8208byte // 发送波形数据，4096个数据点+6个参数，总共8208字节
		}
	}

	return thickness;                                                    // 返回计算得到的厚度值（微米）
}

/**
  * @brief  测量波采集                                                   // 函数功能：普通模式的厚度测量，包含完整的AGC和信号处理
  * @param  vel: 超声波传播速度（m/s）                                   // 输入参数：声速值，用于厚度计算
  * @param  showAgc: 是否显示AGC状态标志                                 // 输入参数：1=显示AGC调整状态，0=不显示
  * @retval 返回厚度值（微米），0表示测量失败                            // 返回值：厚度测量结果，0表示AGC调整中或测量无效
  */
uint32_t Thk_MeasureThickness(uint16_t vel, uint8_t showAgc)             // 普通厚度测量函数，支持自动增益控制
{
//	uint32_t	i, j, N;                                                 // 循环变量（已注释，未使用）
	uint16_t 	fs;                                                      // 变量：采样频率（MHz）
	float		fe;                                                      // 变量：激励频率（MHz）
	uint16_t 	avg;                                                     // 变量：平均次数，用于信号降噪
	float 		gain = g_Emat.gain_x10/10.0;                             // 变量：增益值，从十倍数转换为实际倍数（如gain_x10=200表示20倍增益）
	uint32_t 	dly_pt;                                                  // 变量：延时点数，零点时间补偿对应的采样点数
	float		delta;                                                   // 变量：增量值（声明但未使用）
	int32_t		blind_pt;                                                // 变量：盲区点数，近场干扰区域的采样点数
	int32_t 	thickness;                                               // 变量：计算得到的厚度值
	uint8_t 	needAgc;                                                 // 变量：AGC需求标志，1=需要调整增益，0=增益合适
	uint8_t 	repair_en = 0; //使能“修复法”采集波形                     // 变量：修复法使能标志（实验功能，当前设为0禁用）

	fs 	= g_Thk.wave.fs;                                                 // 获取波形采样频率，通常为50MHz
	fe 	= g_Emat.fe_MHz;                                                 // 获取EMAT激励频率，通常为3.85MHz
	avg = Emat_GetAvg();                                                 // 获取当前设置的平均次数，用于信号降噪
	dly_pt = Zpt_GetZptTimeNs()/(1000/fs);//getZero_Point(fs);          // 计算零点延时对应的采样点数：零点时间(ns) / 采样周期(ns/点)
//	if(Cali_isCalibrating_ZeroPt()) {                                    // 如果正在进行零点校准（已注释）
//		dly_pt = 0;                                                      // 校准时不使用零点补偿（已注释）
//	}

	DAQ_AcqWave(g_Thk.wave.buf, dly_pt, g_Thk.wave.len_pt, fs, avg, gain); // 执行波形采集：缓冲区、延时点数、数据长度、采样频率、平均次数、增益

	//计算盲区                                                           // 注释：计算近场盲区，避免发射余震干扰
	blind_pt 			= THK_CalcBlindPointsNum(g_Thk.wave.buf, g_Thk.wave.len_pt, fs, fe, Fpga_GetAdcMaxVal()); // 根据波形特征、采样频率、激励频率和ADC最大值计算盲区点数
	//使用新盲区计算最大值                                               // 注释：在盲区之后搜索真实的回波峰值
	g_Thk.wave.max_i 	= CalcI16ArrayMaxValIdx(g_Thk.wave.buf + blind_pt, g_Thk.wave.len_pt - blind_pt) + blind_pt; // 在盲区后的数据中找最大值位置，加上盲区偏移得到绝对位置
	g_Thk.wave.max_val	= g_Thk.wave.buf[g_Thk.wave.max_i];              // 获取最大值对应的幅度，即主回波峰值

	//AGC处理                                                            // 注释：自动增益控制处理，确保信号幅度在合适范围
	if((g_Dev.controlByApp) && (!Daq_AGC_isEnable()))  //连接APP, 且手动增益，可计算厚度 // 如果被APP控制且AGC被禁用（手动增益模式）
		needAgc = 0;                                                     // 不需要AGC调整，可以直接计算厚度
	else {                                                               // 否则需要检查AGC状态
		if(Gain_AGC_isDone(g_Thk.wave.max_val, &g_Emat.gain_x10)) //1:无需AGC // 检查当前信号幅度是否在合适范围，如果是则AGC完成
			needAgc = 0; //无需调整增益, 可计算厚度                      // AGC已完成，可以计算厚度
		else                                                             // 如果信号幅度不在合适范围
			needAgc = 1; //需要调整增益, 不计算厚度                      // 需要调整增益，本次不计算厚度
	}

	g_Thk.wave.waveValid = 1;                                            // 设置波形数据有效标志，表示波形采集成功
	
	//计算厚度                                                           // 注释：根据AGC状态决定是否计算厚度
	if(!needAgc) {                                                       // 如果不需要AGC调整（信号幅度合适）
		thickness = THK_CalcThickness(g_Thk.wave.buf, g_Thk.wave.len_pt, g_Daq.fs_MHz, g_Emat.fe_MHz, g_Emat.Emit_Rep, g_Zpt.zeroPoint_ns, vel); // 调用厚度计算算法：波形数据、数据长度、采样频率、激励频率、发射次数、零点时间、声速
		UI_ClearAGC();                                                   // 清除OLED上的AGC显示标志
	}
	else {                                                               // 如果需要AGC调整
		thickness = 0;                                                   // 厚度设为0，表示本次测量无效
		if(showAgc) {                                                    // 如果需要显示AGC状态
			UI_ShowAGC();                                                // 在OLED上显示AGC调整中的标志
		}
	}

	if(g_Dev.controlByApp == 1) {                                        // 如果设备被APP控制（WiFi连接状态）
		if(g_Wifi.sendThicknessOnly) {                                   // 如果WiFi配置为仅发送厚度值模式
			// 只发送厚度值                                              // 注释：轻量级数据传输，节省带宽
			SendThicknessValueCmd2App((uint32_t)thickness, needAgc ? 0 : 1); // 发送厚度值到APP，第二个参数表示测量是否有效（AGC完成为1，调整中为0）
		} else {                                                         // 否则发送完整数据包
			// 发送完整数据包（原有逻辑）                                // 注释：完整数据传输，包含波形
			updatePackageParameter(thickness, g_Daq.fs_MHz, vel, g_Emat.Emit_Rep, 4); // 更新数据包参数：厚度、采样频率、声速、发射次数、数据类型4
			Wifi_SendData((uint16_t *)g_Thk.wave.buf, 4104); // 4104*2 = 8208Bytes // 发送波形数据，4096个数据点+8个参数头，总共8208字节
		}
	}

	return thickness;                                                    // 返回计算得到的厚度值（微米），0表示测量无效
}


static void EmitDaqThkCalcAndDispLay(void) {                            // 静态函数：发射-采集-计算-显示的完整测量流程
	uint8_t pulNumBackup = g_Emat.Emit_Rep;                              // 备份当前的发射脉冲重复次数，用于后续恢复
	uint8_t thkResIsFromSinglePeakAlg = 0;                               // 标志：测量结果是否来自单峰算法，0=多峰算法，1=单峰算法
	uint32_t thkValUm1 = 0;                                              // 变量：第一次测量得到的厚度值（微米）
	uint32_t thkValUm2 = 0;                                              // 变量：第二次补充测量得到的厚度值（微米）

	//S1: 主发射采集(首次)                                               // 步骤1：执行主要的测量过程
	if(g_Thk.longMode) {                                                 // 如果处于长距离测量模式
		thkValUm1 = Thk_MeasureThk_Long(&g_Thk.longWave, g_Thk.ultraSonic_Velocity); // 采集一次厚度 // 使用长距离测量函数，传入长波形结构和声速
		thkResIsFromSinglePeakAlg = 1;                                   // 长距离模式总是使用单峰算法
	}
	else {                                                               // 如果处于普通测量模式
		thkValUm1 = Thk_MeasureThickness(g_Thk.ultraSonic_Velocity, 1); // 采集一次厚度 // 使用普通测量函数，参数1表示显示AGC状态
		thkResIsFromSinglePeakAlg = THK_IsSinglePeakAlgOfRes();          // 查询本次测量是否使用了单峰算法
	}


	//S2: 补充发射采集                                                   // 步骤2：可选的补充测量，提高测量精度
	if(g_Thk.supplyEmitEnable && thkResIsFromSinglePeakAlg) {            // 如果启用了补充发射且第一次测量使用了单峰算法
		g_Emat.Emit_Rep = 2;                                             // 临时设置发射重复次数为2，减少测量时间

		if(g_Thk.longMode)                                               // 如果是长距离模式
			thkValUm2 = Thk_MeasureThk_Long(&g_Thk.longWave, g_Thk.ultraSonic_Velocity); // 采集一次厚度 // 执行第二次长距离测量
		else                                                             // 如果是普通模式
			thkValUm2 = Thk_MeasureThickness(g_Thk.ultraSonic_Velocity, 1); // 采集一次厚度 // 执行第二次普通测量
	}

	g_Thk.resValid = 1;                                                  // 设置测量结果有效标志为1
	g_Thk.thkResValUm = thkValUm1;                                       // 将第一次测量结果作为最终厚度值（注：第二次测量结果未使用，可能用于验证）

	//S3: 显示结果                                                       // 步骤3：根据显示模式更新界面

	if(g_ThkHmi.isDispWave) {                                            // 如果当前处于波形显示模式
		UiDrawWave_DisplayWaveAndThk(g_Thk.wave.buf, g_Thk.wave.buflen, thkValUm1, (g_Dev.unit_type == UNIT_BS), g_Thk.referenceBitOn, g_ThkHmi.isDispWaveEnvelope); // 显示波形和厚度：波形缓冲区、缓冲区长度、厚度值、是否英制单位、是否显示参考位、是否显示包络
	}
	else {                                                               // 如果当前处于数字显示模式
		OLED_ShowLargeNumber(thkValUm1);                                 // 在OLED上显示大数字格式的厚度值
	}

	//S4: 参数恢复                                                       // 步骤4：恢复测量前的参数设置
	g_Emat.Emit_Rep = pulNumBackup;                                      // 恢复原始的发射重复次数设置
	g_Emat.US_avgTimes_old = g_Emat.US_avgTimes;                         // 更新平均次数的历史值
}

/**
  * @brief  厚度测量主任务                                               // 函数功能：厚度测量应用的主循环任务（注释中的"轴力检测"应为"厚度测量"）
  * @param  None                                                        // 输入参数：无
  * @retval None                                                        // 返回值：无
  */
void App_Thickness_MainTask(void)                                        // 厚度测量应用主任务函数，在主循环中被反复调用
{
	uint32_t thickness_val_tmp = 0;                                      // 临时厚度值变量（声明但未使用）
	WifiCmdPolling(0);                                                   // WiFi命令轮询处理，参数0表示普通优先级

	if(g_Dev.controlByApp == 1) { // APP 取得控制权                       // 如果设备被APP控制（WiFi连接状态）
		uint8_t keyId = 0;                                               // 按键ID变量（声明但未使用）
		HMI_Manage();                                                    // 处理人机界面交互，包括按键响应和界面更新

		if(g_Emat.Emit_Switch==1) {                                      // 如果EMAT发射开关被打开
			EmitDaqThkCalcAndDispLay();                                  // 执行完整的测量流程：发射-采集-计算-显示
		}
		BatteryHandler(0);                                               // 处理电池电量显示，参数0表示正常更新
		if(g_Dev.keepConnecttedWDG_Action == 1) {                        // 如果保持连接看门狗动作标志被设置
			g_Dev.wifiState = 0x01;                                      // 设置WiFi状态为连接状态
			AppBreakConnectHandler();                                    // 处理APP断开连接的情况
		}
	}
	else { // 设备自身控制权                                             // 如果设备处于自主控制模式（非APP控制）
		if(g_Dev.hmi_level == GUI_LEVEL_MAIN) {                          // 如果当前处于主界面层级
			BatteryHandler(0);                                           // 处理电池电量显示
		}
		if(g_Emat.Emit_Switch==1) {                                      // 如果EMAT发射开关被打开
			EmitDaqThkCalcAndDispLay();                                  // 执行完整的测量流程：发射-采集-计算-显示
		}
		HMI_Manage();                                                    // 处理人机界面交互，包括按键响应和菜单操作
	}
}



