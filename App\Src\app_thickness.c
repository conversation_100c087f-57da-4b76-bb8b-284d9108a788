/**
  ********************************************************************************
  * Copyright (C), 2018-2023, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : main.c
  * @project : 项目名称（主要文件必选，次要文件可选）
  * @brief   : 介绍
  * <AUTHOR> PR Team
  * @date    : 2022/08/08
  * @version : V1.0 
  * @history :
  *	2022/08/08:
  *	  1) 首次创建;
  *	2023/01/18:
  *	  1）新增波形采集时修复的功能(实验用);
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */
#include "app_thickness.h"
#include "app_cali.h"
#include "app_gui.h"
#include "emat.h"
#include "thk.h"
//#include "single_peak.h"
#include "oled.h"
#include "delay.h"
#include "app.h"
#include "adc.h"
#include "dac.h"
#include "fpga.h"
#include "wifi.h"
#include "bsp_zpt.h"
#include "ui_draw_wave.h"

THICK_FUNCTION_T 	g_Thk;

/**
  * @brief  参数初始化
			波形数组在 app.c 中统一管理 
  * @param  None
  * @retval None
  */
void App_Thk_Init(void)
{
	Emat_SetFs(50);
	
	//波形数组在 app.c 中统一管理
	g_Thk.wave.waveValid	= 0;
	g_Thk.wave.fs			= 50;
	g_Thk.wave.len_pt 		= THK_WAVE_LEN;
	
	g_Thk.longWave.waveValid	= 0;
	g_Thk.longWave.fs		= 50;
	g_Thk.longWave.len_pt 	= THK_LONG_WAVE_LEN;
	
	g_Thk.resValid = 0;
	g_Thk.thkResValUm = 0;
	
	g_Thk.supplyEmitEnable	= 0;
}

uint8_t Thk_isLongMode(void) {
	return g_Thk.longMode;
}


static uint32_t CalcI16ArrayMaxValIdx(int16_t *buf, uint32_t len) {
	int16_t max_tmp;
	uint32_t max_idx;
	
	max_tmp = buf[0];
	max_idx = 0;
	for(uint32_t i = 0; i < len; i++) {
		if(buf[i] >= max_tmp) {
			max_idx = i;
			max_tmp = buf[i];
		}
	}
	return max_idx;
}

static int32_t CalcRealPeakIdx_forFirstPeakWeak(int16_t *buf, uint32_t len, uint32_t max_i) {
	int32_t i, i0;
	int16_t max_val = buf[max_i];
	uint32_t range = 512;
	
	uint32_t max_i_1p2, max_i_1p3;
	int16_t max_v_1p2, max_v_1p3;
	
	//计算 1/2 处最大值
	i0 = (max_i/2 > range/2) ? (max_i/2 - range/2) : 0;
	max_i_1p2 = CalcI16ArrayMaxValIdx(buf + i0, range) + i0;
	max_v_1p2 = buf[max_i_1p2];
	
	//计算 1/3 处最大值
	i0 = (max_i/3 > range/2) ? (max_i/3 - range/2) : 0;
	max_i_1p3 = CalcI16ArrayMaxValIdx(buf + i0, range) + i0;
	max_v_1p3 = buf[max_i_1p3];
	
	if(max_v_1p3 > max_val/2)
		return max_i_1p3;
	else if(max_v_1p2 > max_val/2)
		return max_i_1p2;
	else
		return max_i;
}

/**
  * @brief  选波采集
  * @param  
  * @retval 
  */
uint32_t Thk_MeasureThk_Long(WAVE_T *wave, uint32_t vel) {
	uint32_t		blind_us = 20;
	uint32_t		thickness;
	uint16_t 		fs;
	float			fe;
	uint32_t 		i, j, max_v, max_i;
	
	fs = wave->fs;
	g_Daq.fs_MHz = fs;
	fe = g_Emat.fe_MHz;
//	Emat_SetAvg(128);
	
	//拼接采集
	DAQ_AcqJointWave(wave->buf, wave->len_pt, 1);
	wave->max_i	= CalcI16ArrayMaxValIdx(wave->buf + blind_us*fs, wave->len_pt - blind_us*fs) + blind_us*fs;
	wave->max_val	= wave->buf[wave->max_i];
	
	//判断是否倍值
	wave->max_i 	= CalcRealPeakIdx_forFirstPeakWeak(wave->buf, wave->len_pt, wave->max_i);
	wave->max_val	= wave->buf[wave->max_i];
	
	wave->waveValid = 1;
	
	
	if(g_Dev.controlByApp == 1) {
		//抽数据显示
		for(i = 0; i < 4096; i++) {
			for(j = i*8, max_i = j, max_v = abs(wave->buf[max_i]); j < (i+1)*8; j++) {
				if(abs(wave->buf[j]) > max_v) {
					max_v = abs(wave->buf[j]);
					max_i = j; //极值
				}
			}
			g_Thk.wave.buf[i] = wave->buf[max_i];//wave->buf[i*8];
		}
	}
	
	//计算大概厚度
//	thickness = CalcSinglePeakThickness(wave->buf, wave->max_i, 1, fs, fe) * vel / (fs * 2);
	thickness = SPK_CalcTOF_withDestPeak(wave->buf, wave->len_pt, wave->max_i, 1, fs, fe, g_Emat.Emit_Rep) * vel / (fs * 2);
	
	if(g_Dev.controlByApp == 1) {
//		//抽数据显示
//		for(i = 0; i < 4096; i++) {
//			for(j = i*8, max_i = j, max_v = abs(wave->buf[max_i]); j < (i+1)*8; j++) {
//				if(abs(wave->buf[j]) > max_v) {
//					max_v = abs(wave->buf[j]);
//					max_i = j; //极值
//				}
//			}
//			wave->buf[i] = wave->buf[max_i];									//此处是否有错误？暂时修改为下行。
//		}
		if(g_Wifi.sendThicknessOnly) {
			// 只发送厚度值
			SendThicknessValueCmd2App((uint32_t)thickness, 1);
		} else {
			// 发送完整数据包（原有逻辑）
			updatePackageParameter(thickness, g_Daq.fs_MHz, vel, g_Emat.Emit_Rep, 4);
			Wifi_SendData((uint16_t *)g_Thk.wave.buf, 4096+6); // 4104*2 = 8208byte
		}
	}
	
	return thickness;
}

/**
  * @brief  测量波采集
  * @param  
  * @retval 0:OK	
  */
uint32_t Thk_MeasureThickness(uint16_t vel, uint8_t showAgc)
{
//	uint32_t	i, j, N;
	uint16_t 	fs;
	float		fe;
	uint16_t 	avg;
	float 		gain = g_Emat.gain_x10/10.0;
	uint32_t 	dly_pt;
	float		delta;
	int32_t		blind_pt;
	int32_t 	thickness;
	uint8_t 	needAgc;
	uint8_t 	repair_en = 0; //使能“修复法”采集波形

	fs 	= g_Thk.wave.fs;
	fe 	= g_Emat.fe_MHz;
	avg = Emat_GetAvg();
	dly_pt = Zpt_GetZptTimeNs()/(1000/fs);//getZero_Point(fs);
//	if(Cali_isCalibrating_ZeroPt()) {
//		dly_pt = 0;
//	}	

	DAQ_AcqWave(g_Thk.wave.buf, dly_pt, g_Thk.wave.len_pt, fs, avg, gain);
	
	//计算盲区
	blind_pt 			= THK_CalcBlindPointsNum(g_Thk.wave.buf, g_Thk.wave.len_pt, fs, fe, Fpga_GetAdcMaxVal());
	//使用新盲区计算最大值
	g_Thk.wave.max_i 	= CalcI16ArrayMaxValIdx(g_Thk.wave.buf + blind_pt, g_Thk.wave.len_pt - blind_pt) + blind_pt;
	g_Thk.wave.max_val	= g_Thk.wave.buf[g_Thk.wave.max_i];
	
	//AGC处理
	if((g_Dev.controlByApp) && (!Daq_AGC_isEnable()))  //连接APP, 且手动增益，可计算厚度
		needAgc = 0;
	else {
		if(Gain_AGC_isDone(g_Thk.wave.max_val, &g_Emat.gain_x10)) //1:无需AGC
			needAgc = 0; //无需调整增益, 可计算厚度
		else
			needAgc = 1; //需要调整增益, 不计算厚度
	}
	
	g_Thk.wave.waveValid = 1;
	
	//计算厚度
	if(!needAgc) {
		thickness = THK_CalcThickness(g_Thk.wave.buf, g_Thk.wave.len_pt, g_Daq.fs_MHz, g_Emat.fe_MHz, g_Emat.Emit_Rep, g_Zpt.zeroPoint_ns, vel);
		UI_ClearAGC();
	}
	else {
		thickness = 0;
		if(showAgc) {
			UI_ShowAGC();
		}
	}

	if(g_Dev.controlByApp == 1) {
		if(g_Wifi.sendThicknessOnly) {
			// 只发送厚度值
			SendThicknessValueCmd2App((uint32_t)thickness, needAgc ? 0 : 1);
		} else {
			// 发送完整数据包（原有逻辑）
			updatePackageParameter(thickness, g_Daq.fs_MHz, vel, g_Emat.Emit_Rep, 4);
			Wifi_SendData((uint16_t *)g_Thk.wave.buf, 4104); // 4104*2 = 8208Bytes
		}
	}
	
	return thickness;
}


static void EmitDaqThkCalcAndDispLay(void) {
	uint8_t pulNumBackup = g_Emat.Emit_Rep;
	uint8_t thkResIsFromSinglePeakAlg = 0;
	uint32_t thkValUm1 = 0;
	uint32_t thkValUm2 = 0;
	
	//S1: 主发射采集(首次)
	if(g_Thk.longMode) {
		thkValUm1 = Thk_MeasureThk_Long(&g_Thk.longWave, g_Thk.ultraSonic_Velocity); // 采集一次厚度
		thkResIsFromSinglePeakAlg = 1;
	}
	else {
		thkValUm1 = Thk_MeasureThickness(g_Thk.ultraSonic_Velocity, 1); // 采集一次厚度
		thkResIsFromSinglePeakAlg = THK_IsSinglePeakAlgOfRes();
	}
	
	
	//S2: 补充发射采集
	if(g_Thk.supplyEmitEnable && thkResIsFromSinglePeakAlg) {
		g_Emat.Emit_Rep = 2;
		
		if(g_Thk.longMode)
			thkValUm2 = Thk_MeasureThk_Long(&g_Thk.longWave, g_Thk.ultraSonic_Velocity); // 采集一次厚度
		else
			thkValUm2 = Thk_MeasureThickness(g_Thk.ultraSonic_Velocity, 1); // 采集一次厚度
	}
	
	g_Thk.resValid = 1;
	g_Thk.thkResValUm = thkValUm1;
	
	//S3: 显示结果
	
	if(g_ThkHmi.isDispWave) {
		UiDrawWave_DisplayWaveAndThk(g_Thk.wave.buf, g_Thk.wave.buflen, thkValUm1, (g_Dev.unit_type == UNIT_BS), g_Thk.referenceBitOn, g_ThkHmi.isDispWaveEnvelope);
	}
	else {
		OLED_ShowLargeNumber(thkValUm1);
	}
	
	//S4: 参数恢复
	g_Emat.Emit_Rep = pulNumBackup;
	g_Emat.US_avgTimes_old = g_Emat.US_avgTimes;
}

/**
  * @brief  轴力检测主任务
  * @param  None
  * @retval None
  */
void App_Thickness_MainTask(void)
{
	uint32_t thickness_val_tmp = 0;
	WifiCmdPolling(0);

	if(g_Dev.controlByApp == 1) { // APP 取得控制权
		uint8_t keyId = 0;
		HMI_Manage();
	
		if(g_Emat.Emit_Switch==1) {
			EmitDaqThkCalcAndDispLay();
		}
		BatteryHandler(0); 
		if(g_Dev.keepConnecttedWDG_Action == 1) {
			g_Dev.wifiState = 0x01;
			AppBreakConnectHandler();
		}
	}
	else { // 设备自身控制权
		if(g_Dev.hmi_level == GUI_LEVEL_MAIN) {
			BatteryHandler(0);
		}
		if(g_Emat.Emit_Switch==1) {
			EmitDaqThkCalcAndDispLay();
		}
		HMI_Manage();
	}
}



