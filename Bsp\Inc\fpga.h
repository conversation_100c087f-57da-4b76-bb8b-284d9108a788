#ifndef __FPGA_H
#define __FPGA_H	 
#include <stdint.h>


#define ADS4145_BITS		14
#define ADS4145_MAX_VAL		8191
#define ADS4145_MIN_VAL		-8192

#define FPGA_ADC_BITS			ADS4145_BITS
#define FPGA_ADC_MAX_VAL		ADS4145_MAX_VAL
#define FPGA_ADC_MIN_VAL		ADS4145_MIN_VAL


#define FPGA_CMD_EMAT_DAQ				0x01
#define FPGA_CMD_MKS					0x31

#define FPGA_CMD_READ_DAQ_DATA      	0x21
#define FPGA_CMD_READ_STATE_REG     	0x30
#define FPGA_CMD_READ_VERSION_REG   	0xB0

#define FPGA_CMD_CLEAR_DAQ_TXDATA		0x34 //清除数据采集发送数据


#define FPGA_CMD_COMMON					0x01
#define FPGA_CMD_ENCODER				0x02
#define FPGA_CMD_DAQ					0x05
#define FPGA_CMD_XCORR					0x06
#define FPGA_CMD_BLIND					0x07
#define FPGA_CMD_MKS					0x31


void FPGA_IO_Init(void);
void FPGA_Reset(void);
//u32 Get_thickness(u16 velc);
//u32 Get_thickness_filter(u16 velc, u8 times);
uint32_t Get_longthickness(uint16_t vel);
uint8_t DAQ_AcqWave(int16_t *pdest, uint32_t dlyPts, uint32_t lenPts, uint16_t fs, uint16_t avg, float gain);
uint8_t DAQ_AcqJointWave(int16_t *pdest, uint32_t len, uint8_t agc_en);
//uint8_t DAQ_AcqJointWave(WAVE_T *wave);

void SendParameter2FPGA_PowerDown();
void SendParameter2FPGA(uint8_t sampRate, uint16_t avgTimes);
//void SendParameter2FPGA_stress(uint8_t fs, uint16_t avg, uint32_t dly_pt, uint32_t len_pt);
uint8_t Rx_Calc_Wave_Oled(int16_t *buf, uint32_t len, uint8_t isAGC);
uint8_t DAQ_AcqThkWaveAndSend2App(int32_t avg);

uint16_t FPGA_ReadVersionReg(void);

int32_t Fpga_GetAdcMaxVal(void);
int32_t Fpga_GetAdcMinVal(void);
int32_t Fpga_GetAdcBits(void);

uint8_t Fpga_IsNewWaveReady(void);
void Fpga_EmitTaskForTimer(void);

#endif
