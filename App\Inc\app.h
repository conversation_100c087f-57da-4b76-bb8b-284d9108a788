/**
  ******************************************************************************
  * @file    app.h
  * @brief   This file contains all the function prototypes for
  *          the app.c file
  ******************************************************************************
  * @attention
  *
  ******************************************************************************
  */
  
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __APP_H__
#define __APP_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "app_gui.h"
//#include "app_StressDetector.h"
//#include "app_thickness.h"

#define USE_FLASH_SIZE	24

#define FUNC_THICKNESS_COMMON 	0
#define FUNC_THICKNESS_LONG 	1
//#define FUNC_STRESS_DETECTOR	2

#define DEV_FUNC_SUPPORT_BOLT	0//1
#define DEV_FUNC_SUPPORT_LONG	0//1

#define CALI_THICKNESS_MAX 		20 // 校准件最大厚度 100 mm
#define CALI_THICKNESS_MIN 		5 // 校准件最大厚度 2 mm
#define VELOCITY_MAX 			9999 // 最大声速 9999m/s
#define VELOCITY_MIN 			1000 // 最小声速 1000m/s	 

#define UNIT_SI					0 //国际单位制,mm
#define UNIT_BS					1 //英制单位,inch



typedef struct
{
	uint8_t 	functionMode; 	//功能模式
	
	uint8_t		controlByApp;	//控制权归属 0:自身；1:APP
	uint8_t 	lang_type; 		//语言
	uint8_t 	unit_type; 		//单位
	uint8_t 	brightness_val;	//亮度
	uint8_t		wifiState;
	uint16_t	dismantlingTimesLocal;
	char		deviceSN[15];
	uint8_t		allowOledShow;
	uint8_t		keepConnecttedWDG_Action;
	uint8_t	 	hmi_level;
	uint8_t 	hmi_menu_id;
	uint8_t		pcCmd_buf[13];

	uint32_t 	firmWareVersion;
	
}DEV_T;
extern DEV_T g_Dev;

typedef struct
{
	uint8_t		Emit_Switch;
	uint8_t		Emit_AB_phase;
	uint8_t		Emit_brakePulNum;
	uint16_t	US_avgTimes;
	uint16_t	US_avgTimes_old;
	uint8_t		xcorr_en;
	uint8_t		Emit_Rep;
	uint16_t 	DAQ_samp_length;
	uint8_t		DAQ_samp_freq_code;
	uint16_t	DAQ_samp_delay;
	uint8_t		FPGA_filter_AvgTimes;
	int32_t		gain_x10;
	float		fe_MHz;
}EMAT_T;
extern EMAT_T g_Emat;

typedef struct
{
	uint8_t		waveValid;
	int16_t		*buf;
	int32_t		buflen;
	int32_t		dly_pt;
	int32_t		len_pt;
	int32_t		max_i;
	int16_t		max_val;
	
	uint16_t	fs;
}WAVE_T;


typedef struct
{
//	WAVE_T		longwave;
//	int32_t		zeroPoint_ns;
	uint16_t	fs_MHz;
	uint8_t		AGC_en;
}DAQ_T;
extern DAQ_T g_Daq;

void App_Init(void);
void App_MainTask(void);
//void Need_WriteFlash(uint8_t isForce);
//void ParasWriteInFlash_Forced(void);

void Dev_SetFunction(uint8_t func);

//double UnitConvert_mm2inch(double mm);
//double UnitConvert_inch2mm(double inch);

#ifdef __cplusplus
}
#endif
#endif

/******************************* end of file **********************************/