#include "adc.h"
#include "stm32f7xx_hal.h"
#include "delay.h"
#include "oled.h"
#include "wifi.h"
#include "bsp.h"
#include "app.h"

unsigned short int LiVQ_Table[3][12] = {{1, 5, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100},                           //	1???????(%)
                                        {3000, 3450, 3680, 3740, 3770, 3790, 3820, 3870, 3920, 3980, 4060, 4150},  //	1????OCV??  ??
                                        {6500, 7000, 7170, 7310, 7490, 7570, 7620, 7740, 7850, 7920, 8140, 8300}}; //	1????550mA????	 ??

uint8_t batteryPercentLast = 101;
uint8_t batteryPercent_ReduceChange_Enable = 0;
uint8_t allowBatteryPercentCheckCnt = 100;
										
static ADC_HandleTypeDef hadc1;

void ChargePin_Init(void) {
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	
	__HAL_RCC_GPIOA_CLK_ENABLE();
	
	GPIO_InitStruct.Pin = GPIO_PIN_11;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
}


//初始化ADC
void Adc_Init(void) {
	ADC_ChannelConfTypeDef sConfig = {0};
	
	hadc1.Instance = ADC1;
	hadc1.Init.ClockPrescaler = ADC_CLOCK_SYNC_PCLK_DIV4;
	hadc1.Init.Resolution = ADC_RESOLUTION_12B;
	hadc1.Init.ScanConvMode = ADC_SCAN_DISABLE;
	hadc1.Init.ContinuousConvMode = DISABLE;
	hadc1.Init.DiscontinuousConvMode = DISABLE;
	hadc1.Init.ExternalTrigConvEdge = ADC_EXTERNALTRIGCONVEDGE_NONE;
	hadc1.Init.ExternalTrigConv = ADC_SOFTWARE_START;
	hadc1.Init.DataAlign = ADC_DATAALIGN_RIGHT;
	hadc1.Init.NbrOfConversion = 1;
	hadc1.Init.DMAContinuousRequests = DISABLE;
	hadc1.Init.EOCSelection = ADC_EOC_SINGLE_CONV;
	if (HAL_ADC_Init(&hadc1) != HAL_OK)
	{
		Bsp_ErrorHandler();
	}
	
	sConfig.Channel = ADC_CHANNEL_5;
	sConfig.Rank = ADC_REGULAR_RANK_1;
	sConfig.SamplingTime = ADC_SAMPLETIME_3CYCLES;
	if (HAL_ADC_ConfigChannel(&hadc1, &sConfig) != HAL_OK)
	{
		Bsp_ErrorHandler();
	}
	
	ChargePin_Init();
	
//    GPIO_InitTypeDef GPIO_InitStructure;
//    ADC_CommonInitTypeDef ADC_CommonInitStructure;
//    ADC_InitTypeDef ADC_InitStructure;

//    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE); //使能GPIOA时钟
//    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE);  //使能ADC1时钟

//    //先初始化ADC1通道5 IO口
//    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_5;        // PA5 通道2
//    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AN;     //模拟输入
//    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL; //不带上下拉
//    GPIO_Init(GPIOA, &GPIO_InitStructure);           //初始化

//    RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC1, ENABLE);  // ADC1复位
//    RCC_APB2PeriphResetCmd(RCC_APB2Periph_ADC1, DISABLE); //复位结束

//    ADC_CommonInitStructure.ADC_Mode = ADC_Mode_Independent;                     //独立模式
//    ADC_CommonInitStructure.ADC_TwoSamplingDelay = ADC_TwoSamplingDelay_5Cycles; //两个采样阶段之间的延迟5个时钟
//    ADC_CommonInitStructure.ADC_DMAAccessMode = ADC_DMAAccessMode_Disabled;      // DMA失能
//    ADC_CommonInitStructure.ADC_Prescaler = ADC_Prescaler_Div4;                  //预分频4分频。ADCCLK=PCLK2/4=84/4=21Mhz,ADC时钟最好不要超过36Mhz
//    ADC_CommonInit(&ADC_CommonInitStructure);                                    //初始化

//    ADC_InitStructure.ADC_Resolution = ADC_Resolution_12b;                      // 12位模式
//    ADC_InitStructure.ADC_ScanConvMode = DISABLE;                               //非扫描模式
//    ADC_InitStructure.ADC_ContinuousConvMode = DISABLE;                         //关闭连续转换
//    ADC_InitStructure.ADC_ExternalTrigConvEdge = ADC_ExternalTrigConvEdge_None; //禁止触发检测，使用软件触发
//    ADC_InitStructure.ADC_ExternalTrigConv = 0x00000000;
//    ADC_InitStructure.ADC_DataAlign = ADC_DataAlign_Right; //右对齐
//    ADC_InitStructure.ADC_NbrOfConversion = 1;             // 1个转换在规则序列中 也就是只转换规则序列1
//    ADC_Init(ADC1, &ADC_InitStructure);                    // ADC初始化

//    ADC_Cmd(ADC1, ENABLE); //开启AD转换器
//    ChargePin_Init();
}

void HAL_ADC_MspInit(ADC_HandleTypeDef* adcHandle)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	if(adcHandle->Instance==ADC1)
	{
		__HAL_RCC_ADC1_CLK_ENABLE();
		__HAL_RCC_GPIOA_CLK_ENABLE();
		/**ADC1 GPIO Configuration
		PA5     ------> ADC1_IN5
		*/
		GPIO_InitStruct.Pin = GPIO_PIN_5;
		GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
		GPIO_InitStruct.Pull = GPIO_NOPULL;
		HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
	}
}
//获得ADC值
// ch: @ref ADC_channels
//通道值 0~16取值范围为：ADC_Channel_0~ADC_Channel_16
//返回值:转换结果
uint16_t Get_Adc(uint8_t ch) {
    ADC_ChannelConfTypeDef ADC1_ChanConf;
    
    ADC1_ChanConf.Channel=ch;
    ADC1_ChanConf.Rank=1; 
    ADC1_ChanConf.SamplingTime=ADC_SAMPLETIME_480CYCLES;
    ADC1_ChanConf.Offset=0;
    HAL_ADC_ConfigChannel(&hadc1,&ADC1_ChanConf);
	
    HAL_ADC_Start(&hadc1);
	
    HAL_ADC_PollForConversion(&hadc1,10);
   
	return (uint16_t)HAL_ADC_GetValue(&hadc1);
}
//获取通道ch的转换值，取times次,然后平均
// ch:通道编号
// times:获取次数
//返回值:通道ch的times次转换结果平均值
uint16_t Get_Adc_Average(uint8_t ch, uint8_t times) {
    uint32_t temp_val = 0;
    uint8_t t;
    for (t = 0; t < times; t++) {
        temp_val += Get_Adc(ch);
        delay_ms(1);
    }
    return temp_val / times;
}

unsigned char Battery_percent(void) {
    unsigned short int votage;
    unsigned char i;
    unsigned char battery_percent_val = 0;
    uint16_t voltage_compensate = 100;                                       //负载电流800mA时,补偿50mV
                                                                        //	voltage_compensate = 300/10;
    votage = Get_Adc_Average(ADC_CHANNEL_5, 10) * 2500.0 / 4096.0 * 2; //(182.6/82.6);
    for (i = 11; i > 0; i--) {
        if (votage > (LiVQ_Table[1][i] - voltage_compensate)) {
            battery_percent_val = LiVQ_Table[0][i];
            return battery_percent_val;
        }
    }
    battery_percent_val = 1;

    return battery_percent_val;
}

// 处理电池电量相关动作
// 显示电池图标,发送电量到APP
uint8_t BatteryHandler(uint8_t forceShow) {
    uint8_t tmp = 0;
    //	forceShow = 0;
    if ((allowBatteryPercentCheckCnt > 5) || (forceShow == 1)) { // 10*800ms
        allowBatteryPercentCheckCnt = 0;
        tmp = Battery_percent();
        // 电量变化, 更改OLED显示, 发送指令至APP
        if ((batteryPercentLast != tmp) || (forceShow == 1)) {
            batteryPercentLast = tmp;
            Oled_ShowBattery(tmp, BATTERY_CHARGE);
            if (g_Dev.controlByApp == 1) {
                if (BATTERY_CHARGE == 1) {
                    SendBatteryPercentCmd2App(120); //表示充电
                } else {
                    SendBatteryPercentCmd2App(tmp);
                }
            }
        }
    }
}
