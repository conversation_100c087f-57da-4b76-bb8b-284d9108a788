/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : fpga.c
  * @project : 
  * @brief   : 
  * <AUTHOR> PR Team
  * @since   : 2024/07/29
  * @version : V1.0 
  * @history :
  *	2024/07/29: V0.1
  *	  1) 首次创建;
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */
#include "fpga.h"
#include <string.h>
#include "stm32f7xx_hal.h"
#include "bsp_led.h"
#include "delay.h"
#include "dac.h"
#include "fmc.h"
#include "emat.h"
#include "usart.h"
#include "app.h"
#include "bsp_zpt.h"
#include "app_version.h"
#include "timer.h"

#define UART_BYTES_NUM 		16

//#define ADS4145_MAX_VAL		8191
//#define ADS4145_MIN_VAL		-8192

//V1: EmitTrig:C0、Reset:C1
#define EMIT_TRIG_PIN_V1(n) 	HAL_GPIO_WritePin(GPIOC, GPIO_PIN_0, n ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define FPGA_RESET_PIN_V1(n) 	HAL_GPIO_WritePin(GPIOC, GPIO_PIN_1, n ? GPIO_PIN_SET : GPIO_PIN_RESET)

//V2: EmitTrig:C3、Reset:A1
#define EMIT_TRIG_PIN_V2(n) 	HAL_GPIO_WritePin(GPIOC, GPIO_PIN_3, n ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define FPGA_RESET_PIN_V2(n) 	HAL_GPIO_WritePin(GPIOA, GPIO_PIN_1, n ? GPIO_PIN_SET : GPIO_PIN_RESET)

#define EMIT_TRIG_PIN(n) 	isUsePinCfgV2 ? EMIT_TRIG_PIN_V2(n) : EMIT_TRIG_PIN_V1(n)
#define FPGA_RESET_PIN(n) 	isUsePinCfgV2 ? FPGA_RESET_PIN_V2(n) : FPGA_RESET_PIN_V1(n)

static uint8_t isUsePinCfgV2 = 0;
static uint32_t fpgaVersionCode = 0;

static uint32_t sendToFpgaBytesNum = UART_BYTES_NUM;

static void SendParameter2FPGA_stress(uint8_t fsMhz, uint16_t avgTimes, uint32_t daqDlyPt, uint32_t daqLenPt);

//EmitTrig: PC0(ARM-FPGA-IO1)
//ResetFPGA: PC1(ARM-FPGA-IO2)
void FPGA_IO_Init(void)
{
    FPGA_FMC_DataRxTx_Init();
    
	GPIO_InitTypeDef GPIO_InitStruct = {0};
    __HAL_RCC_GPIOA_CLK_ENABLE();
	__HAL_RCC_GPIOC_CLK_ENABLE();
	
    //V1: EmitTrig:C0、Reset:C1
	GPIO_InitStruct.Pin = GPIO_PIN_0 | GPIO_PIN_1;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_0, GPIO_PIN_RESET); //发射触发脚拉低
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_1, GPIO_PIN_SET); //复位脚拉高
    
    //V2: EmitTrig:C3、Reset:A1
	GPIO_InitStruct.Pin = GPIO_PIN_3;
    HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
    HAL_GPIO_WritePin(GPIOC, GPIO_PIN_3, GPIO_PIN_RESET); //发射触发脚拉低
    
    GPIO_InitStruct.Pin = GPIO_PIN_1;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
    HAL_GPIO_WritePin(GPIOA, GPIO_PIN_1, GPIO_PIN_SET); //复位脚拉高
	
//	while(1) {
//		fpgaVersionCode = FPGA_ReadVersionReg();
//		delay_us(100);
//	}
	
    //读取版本号
    fpgaVersionCode = FPGA_ReadVersionReg();
    if(fpgaVersionCode <= 2401) {
        isUsePinCfgV2 = 0;
    }
    else {
        isUsePinCfgV2 = 1;
    }
    
    EMIT_TRIG_PIN(0);
	FPGA_Reset();
}

void FPGA_Reset(void) {
	FPGA_RESET_PIN(0);
	delay_us(70);
	FPGA_RESET_PIN(1);
}

static void Fmc_ReadOutPrepareData(void) {
	uint16_t es2b;
	es2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	es2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	es2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	es2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
}



uint16_t FPGA_ReadVersionReg(void) {
	uint16_t content2b; //	uint8_t msb, lsb;
	
	FPGA_FMC_CS(0);
	*(volatile uint8_t *)FMC_FPGA_BASE_ADDR = FPGA_CMD_READ_VERSION_REG;
	delay_us(1); 
	Fmc_ReadOutPrepareData();
//	lsb = *(volatile uint8_t *)FMC_FPGA_BASE_ADDR;
//	msb = *(volatile uint8_t *)FMC_FPGA_BASE_ADDR;
	content2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	FPGA_FMC_CS(1);
	
	return content2b; //(((uint16_t)msb << 8) | lsb);
}

uint16_t FPGA_ReadDaqStateReg(void) {
	uint16_t twoBytes;
	
	FPGA_FMC_CS(0);
	*(volatile uint8_t *)FMC_FPGA_BASE_ADDR = FPGA_CMD_READ_STATE_REG;
	delay_us(1); 
	Fmc_ReadOutPrepareData();
	twoBytes = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	FPGA_FMC_CS(1);
	
	return twoBytes;
}


uint8_t Fpga_IsNewWaveReady(void) {
	uint16_t daqStateRegData;
	daqStateRegData = FPGA_ReadDaqStateReg();
	if(daqStateRegData & 0x0001) return 1; //
	
	return 0;
}

static uint8_t Fpga_ReadDaqDataByFmc(int16_t *buf, int32_t dataNum) {
	uint32_t i;
	uint32_t fsMhz = g_Daq.fs_MHz;
	uint32_t rxMeasWaveSize;
	uint8_t *pBuf;
	uint16_t daqStateRegData;
	int16_t section1Buf[256];
	
	//读取数据采集状态寄存器, 判断数据是否就绪
	daqStateRegData = FPGA_ReadDaqStateReg();
	if(!(daqStateRegData & 0x0001))
		return 1; //无数据
	
	FPGA_FMC_CS(0);
	*(volatile uint8_t *)FMC_FPGA_BASE_ADDR = FPGA_CMD_READ_DAQ_DATA; //读取采集数据指令
	delay_us(1); //等待数据就绪
	Fmc_ReadOutPrepareData();
	
	memcpy(section1Buf, (uint32_t *)FMC_FPGA_BASE_ADDR, 256 * sizeof(int16_t));
	
	//读取测量波形
	rxMeasWaveSize = 2 * dataNum;

	memcpy(buf, (uint32_t *)FMC_FPGA_BASE_ADDR, rxMeasWaveSize);
	
	FPGA_FMC_CS(1);

	
	return 0; //读取成功
}



int32_t Fpga_GetAdcMaxVal(void) { 
	return ADS4145_MAX_VAL;
}

int32_t Fpga_GetAdcMinVal(void) { 
	return ADS4145_MIN_VAL;
}

int32_t Fpga_GetAdcBits(void) { 
	return FPGA_ADC_BITS;
}


static uint32_t CalcI16ArrayMaxIdx(int16_t *src, uint32_t len) {
	uint32_t i;
	int16_t max_val;
	uint32_t max_i;
	
	max_i = 0;
	max_val = src[max_i];
	for(i = 0; i < len; i++) {
		if(src[i] > max_val){
			max_val = src[i]; 
			max_i = i;	
		}
	}
	return max_i;
}

//float tstGainValBuf[7] = {56.0, 64.0, 70.0, 76.0, 82.0, 88.0, 94.0};
//float tstGainRatioBuf[7];
//int16_t tstMaxValBuf[7];
//int16_t tstGainCnt = 0;

typedef struct { 
	uint8_t enable;
	uint8_t waveValid;
	uint16_t waveLen;
	int16_t waveBuf[4096];
} TimerEmit_T;
static TimerEmit_T timerEmit = {.enable = 0, 0};

void Fpga_EmitTaskForTimer(void) {
	if(!timerEmit.enable) return;
	
	if(Fpga_IsNewWaveReady()) {
		if(Fpga_ReadDaqDataByFmc(timerEmit.waveBuf, timerEmit.waveLen) == 0) {
			timerEmit.waveValid = 1;
		}
		else {
			timerEmit.waveValid = 0;
		}
	}
	
	EMIT_TRIG_PIN(1); 
	delay_us(10);
	EMIT_TRIG_PIN(0);
}

/**
  * @brief  单次数据采集(包含增益的设置等一系列参数)
  * @param  pdest: 存放波形数组; 
  * @retval None
  */
uint8_t DAQ_AcqWave(int16_t *pdest, uint32_t dlyPts, uint32_t lenPts, uint16_t fs, uint16_t avg, float gain)
{
	//参数参设：增益、采样频率、平均、采样延时、采样点数
	//test
	static float test_gain;
	test_gain= gain;
	//endoftest
//	SetGain(gain*10);
	SetGain(test_gain*10);
	SendParameter2FPGA_stress(fs, avg, dlyPts, lenPts);
	
	timerEmit.enable = 1; //强制开启定时器触发模式
	if(timerEmit.enable) {
		if(lenPts <= sizeof(timerEmit.waveBuf)/sizeof(int16_t)) {
			timerEmit.waveLen = lenPts;
		}
		else {
			timerEmit.waveLen = sizeof(timerEmit.waveBuf)/sizeof(int16_t);
		}
		
		EmitTimer_SetIntervalTimeMsAndStart(2000);
	}
	
	while(1) {
		if(timerEmit.enable) {
			if(timerEmit.waveValid) {
				uint32_t readWaveLen = timerEmit.waveLen < lenPts ? timerEmit.waveLen : lenPts;
				memcpy(pdest, timerEmit.waveBuf, readWaveLen * sizeof(int16_t));
				timerEmit.waveValid = 0;
				return 0; //采集完成
			}
		}
		else {
			EMIT_TRIG_PIN(1); 
			delay_us(10);
			EMIT_TRIG_PIN(0); 
			delay_ms(2);
			
			if(Fpga_ReadDaqDataByFmc(pdest, lenPts) == 0) {
				return 0; //采集完成
			}
		}
	}
}

/**
  * @brief  采集拼接波形, 单次4096点
  * @param  pbuf: 存放波形数组; len: 点数,需要为4096整数倍
  * @retval None
  */
uint8_t DAQ_AcqJointWave(int16_t *pbuf, uint32_t len, uint8_t agc_en)
{
	uint32_t 		i, try_cnt, acq_cnt;
	uint16_t 		fs   = g_Daq.fs_MHz;
	uint16_t 		avg  = g_Emat.US_avgTimes;
	uint32_t 		dly_pt;
	const uint32_t 	ONE_LEN = 4096;
	uint32_t		max_i; 
	int32_t			max_val;
	uint32_t		blind_us = 20;
	
	if(timerEmit.enable) {
		timerEmit.enable = 0; //拼接需关闭定时器触发模式
	}
	
	for(try_cnt = 0; try_cnt < 10; try_cnt++) {
//		dly_pt = getZero_Point(fs);
		dly_pt = Zpt_GetZptTimeNs()/(1000/fs);
		for(acq_cnt = 0; acq_cnt < len/ONE_LEN; acq_cnt++) {
			DAQ_AcqWave(pbuf + acq_cnt*ONE_LEN, dly_pt, ONE_LEN, fs, avg, g_Emat.gain_x10/10.0);
			dly_pt = dly_pt + ONE_LEN;
		}
		//最值计算、AGC判断
		max_i 	= CalcI16ArrayMaxIdx(pbuf + blind_us*fs, acq_cnt*ONE_LEN - blind_us*fs)  + blind_us*fs;
		max_val = pbuf[max_i];
		
		if(!agc_en)
			return 0; //ok
		
		if(Gain_AGC_isDone(max_val, &g_Emat.gain_x10)) {
			return 0; //ok
		}
	}
	return 1; //failed
}

// 发送低功耗指令到FPGA
void SendParameter2FPGA_PowerDown(void) {
}



static void SendSection1CmdToFpga(void) {
	uint8_t buf[32];
	
	buf[0] = 0x0D;
	buf[1] = 0x0A;
	buf[2] = FPGA_CMD_MKS;
	buf[3] = 1; //使能
	buf[4] = 3; //周期数
	buf[5] = (uint8_t)((250 & 0xff00) >> 8);
	buf[6] = (uint8_t)(250 & 0x00ff);
	buf[7] = 125;
	buf[8] = 50;

	Uart2_SendDatas(buf, sendToFpgaBytesNum);
	delay_us(100);
}

static void Fpga_SendEmatDaqParasToFpga(uint8_t sampRate, uint16_t avgTimes) {
	uint8_t sampRateEncode;
	uint8_t avgCode;
	uint16_t dlyPts = 0;
	uint16_t lenPts = 0;
	uint8_t buf[16];
	
//	sampRate = 50; //采样频率固定在50MHz
	sampRateEncode = Emat_GetFsEncode(sampRate);
	avgCode = Emat_GetAvgEncode(avgTimes); 
	dlyPts = Zpt_GetZptTimeNs()/(1000/sampRate);//getZero_Point(sampRate);//getZeroPoint() * (double)sampRate;
	
	buf[0] = 0x0D;
	buf[1] = 0x0A;
	buf[2] = FPGA_CMD_EMAT_DAQ;
	buf[3] = 0x01 | ((g_Emat.Emit_AB_phase&0x01)<<1) | ((g_Emat.Emit_brakePulNum&0x03)<<2);
	buf[4] = g_Emat.Emit_Rep;
	buf[5] = sampRateEncode;
	buf[6] = avgCode;
	buf[7] = 0;
	buf[8] = (dlyPts & 0xff00) >> 8;
	buf[9] = dlyPts & 0xff;
	buf[10] = (lenPts & 0xff00) >> 8;
	buf[11] = lenPts & 0xff;

	Uart2_SendDatas(buf, 12);
	delay_us(100);
}

static void Fpga_SendEmatDaqParasToFpgaForStressMode(uint8_t fs, uint16_t avg, uint32_t dlyPts, uint32_t lenPts) {
	uint8_t sampRateEncode;
	uint8_t avgCode;
	uint8_t buf[16];

	sampRateEncode 	= Emat_GetFsEncode(fs);
	avgCode 		= Emat_GetAvgEncode(avg);

	buf[0] = 0x0D;
	buf[1] = 0x0A;
	buf[2] = FPGA_CMD_EMAT_DAQ;
	buf[3] = (g_Emat.Emit_Switch&0x01) | ((g_Emat.Emit_AB_phase&0x01)<<1) | ((g_Emat.Emit_brakePulNum&0x03)<<2);
	buf[4] = g_Emat.Emit_Rep;
	buf[5] = sampRateEncode;
	buf[6] = avgCode;
	buf[7] = 0;
	buf[8] = (dlyPts & 0xff00) >> 8;
	buf[9] = dlyPts & 0xff;
	buf[10] = (lenPts & 0xff00) >> 8;
	buf[11] = lenPts & 0xff;
	
	//当前
	g_Daq.fs_MHz = fs;
	
	Uart2_SendDatas(buf, 12);
	delay_us(100);
}


static void SendEmitCmdToFpga(uint16_t avg, uint16_t fsMhz, uint32_t daqDlyPt, uint32_t daqLenPt, uint8_t pulNum) {
	uint8_t	tmp;
	uint8_t buf[32];
	uint8_t avgCode = Emat_GetAvgEncode(avg);
	uint16_t dlyPts = 0;
	uint8_t existHalfEmitPul = 0;
	uint8_t chirpEmitEnable = 0;
	uint8_t emitBrakePulNum = g_Emat.Emit_brakePulNum;
	uint8_t emitPulDir = g_Emat.Emit_AB_phase;
	uint8_t emitExtTrigEnable = 0;
	uint8_t emitEnable = g_Emat.Emit_Switch;
	
	dlyPts = Zpt_GetZptTimeNs() / (1000 / fsMhz); //零点延迟
	dlyPts += daqDlyPt;
	
	
	
	static int32_t pulNumLast = -1;
	static int32_t fsMhzLast = -1;
	static int32_t avgCodeLast = -1;
	static int32_t dlyPtsLast = -1;
	static int32_t daqLenPtLast = -1;
	static int32_t emitEnableLast = -1;
	static int32_t emitExtTrigEnableLast = -1;
	static int32_t emitPulDirLast = -1;
	static int32_t emitBrakePulNumLast = -1;
	static int32_t existHalfEmitPulLast = -1;
	static int32_t chirpEmitEnableLast = -1;
	
	
	uint8_t needSend = 0;
	
	if(pulNum != pulNumLast) needSend = 1;
	else if(fsMhz 				!= fsMhzLast			) needSend = 1;
	else if(avgCode 			!= avgCodeLast			) needSend = 1;
	else if(dlyPts 				!= dlyPtsLast			) needSend = 1;
	else if(daqLenPt 			!= daqLenPtLast			) needSend = 1;
	else if(emitEnable 			!= emitEnableLast		) needSend = 1;
	else if(emitExtTrigEnable 	!= emitExtTrigEnableLast) needSend = 1;
	else if(emitPulDir 			!= emitPulDirLast		) needSend = 1;
	else if(emitBrakePulNum 	!= emitBrakePulNumLast	) needSend = 1;
	else if(existHalfEmitPul 	!= existHalfEmitPulLast	) needSend = 1;
	else if(chirpEmitEnable 	!= chirpEmitEnableLast	) needSend = 1;
	
	if(!needSend) return;
	
	pulNumLast 				= pulNum;
	fsMhzLast				= fsMhz;
	avgCodeLast 			= avgCode;
	dlyPtsLast 				= dlyPts;
	daqLenPtLast 			= daqLenPt;
	emitEnableLast 			= emitEnable;
	emitExtTrigEnableLast 	= emitExtTrigEnable;
	emitPulDirLast 			= emitPulDir;
	emitBrakePulNumLast 	= emitBrakePulNum;
	existHalfEmitPulLast 	= existHalfEmitPul;
	chirpEmitEnableLast 	= chirpEmitEnable;
	
	buf[0] = 0x0D;
	buf[1] = 0x0A;
	buf[2] = FPGA_CMD_COMMON; //指令类型
	buf[3] = 0; //g_Emat.EmitEn; //发射使能
	buf[4] = pulNum; //周期数
	buf[5] = fsMhz; //采样频率
	buf[6] = avgCode; //平均次数
	
	tmp = ((existHalfEmitPul & 0x01) << 1) | (chirpEmitEnable & 0x01);
	buf[7] = tmp; //chirp使能
	buf[8] = dlyPts >> 8; //只在V0版有意义(20240429)
	buf[9] = (dlyPts & 0x00ff); //只在V0版有意义(20240429)
	buf[10] = daqLenPt >> 8; //只在V0版有意义(20240429)
	buf[11] = daqLenPt & 0x00ff; //只在V0版有意义(20240429)
	Uart2_SendDatas(buf, sendToFpgaBytesNum);
	delay_us(10);
	
	tmp = ((emitBrakePulNum&0x0f)<<4) | ((emitPulDir&0x01)<<2) | ((emitExtTrigEnable&0x01)<<1) | (emitEnable&0x01);
	buf[3] = tmp; //发射使能
	Uart2_SendDatas(buf, sendToFpgaBytesNum);
	delay_us(10);
}

static void SendDaqCmdToFpga(uint32_t daqDlyPt, uint32_t daqLenPt, uint16_t fsMhz)
{
	uint8_t segNum = 2;
	uint32_t measDlyPts = daqDlyPt; //零点延迟已包含在 daqDlyPt 中
	uint8_t useSimAdcData = 0;
	uint8_t buf[32];
	
	//第二段:测量波形
	uint32_t measBgn = measDlyPts;
	uint16_t measLen = daqLenPt;
	
	uint8_t needSend = 1;
//	
//	if(!needSend) return;


	buf[ 0] = 0x0D;
	buf[ 1] = 0x0A;
	buf[ 2] = FPGA_CMD_DAQ;
	buf[ 3] = (useSimAdcData << 4) | (segNum & 0x0f);
	buf[ 4] = (0       >> 24) & 0x000000ff;
	buf[ 5] = (0       >> 16) & 0x000000ff;
	buf[ 6] = (0       >>  8) & 0x000000ff;
	buf[ 7] = (0       >>  0) & 0x000000ff;
	buf[ 8] = (256     >>  8) & 0x00ff;
	buf[ 9] = (256     >>  0) & 0x00ff;
	buf[10] = (measBgn >> 24) & 0x000000ff;
	buf[11] = (measBgn >> 16) & 0x000000ff;
	buf[12] = (measBgn >>  8) & 0x000000ff;
	buf[13] = (measBgn >>  0) & 0x000000ff;
	buf[14] = (measLen >>  8) & 0x00ff;
	buf[15] = (measLen >>  0) & 0x00ff;
	
	Uart2_SendDatas(buf, sendToFpgaBytesNum);
}

static void SendParameter2FPGA_stress(uint8_t fsMhz, uint16_t avgTimes, uint32_t daqDlyPt, uint32_t daqLenPt) {
	SendDaqCmdToFpga(daqDlyPt, daqLenPt, fsMhz);
	SendSection1CmdToFpga();
	SendEmitCmdToFpga(avgTimes, fsMhz, daqDlyPt, daqLenPt, g_Emat.Emit_Rep);
}


void SendParameter2FPGA(uint8_t fsMhz, uint16_t avgTimes) {
	SendDaqCmdToFpga(0, 4096, fsMhz);
	SendSection1CmdToFpga();
	SendEmitCmdToFpga(avgTimes, fsMhz, 0, 4096, g_Emat.Emit_Rep);
}


/***********************END OF FILE********************************************/
