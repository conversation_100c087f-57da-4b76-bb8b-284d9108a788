#ifndef __ADC_H
#define __ADC_H
#include <stdint.h>

#define BATTERY_CHARGE_INDACTOR_PIN 	GPIO_PIN_6
#define BATTERY_CHARGE 					HAL_GPIO_ReadPin(GPIOA, BATTERY_CHARGE_INDACTOR_PIN) //GPIO_ReadInputDataBit(GPIOA, BATTERY_CHARGE_INDACTOR_PIN)

void Adc_Init(void);                  // ADC通道初始化
uint16_t Get_Adc(uint8_t ch);                   //获得某个通道值
uint16_t Get_Adc_Average(uint8_t ch, uint8_t times); //得到某个通道给定次数采样的平均值
uint8_t BatteryHandler(uint8_t forceShow);
unsigned char Battery_percent(void);

#endif
