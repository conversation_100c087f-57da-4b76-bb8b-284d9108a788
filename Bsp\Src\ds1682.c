#include "ds1682.h"
#include "stm32f7xx_hal.h"
#include "delay.h"

//PB8(CLK); PB9(SDA);
//IO方向设置
#define DS1682_IIC_SDA_IN()  {GPIOB->MODER&=~(3<<(9*2)); GPIOB->MODER|=0<<(9*2);} //PB9 input
#define DS1682_IIC_SDA_OUT() {GPIOB->MODER&=~(3<<(9*2)); GPIOB->MODER|=1<<(9*2);} //PB9 outpout

#define DS1682_IIC_SCL(n)  		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_8, n ? GPIO_PIN_SET : GPIO_PIN_RESET) //SCL
#define DS1682_IIC_SDA(n)  		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_9, n ? GPIO_PIN_SET : GPIO_PIN_RESET) //SDA
#define DS1682_IIC_READ_SDA		HAL_GPIO_ReadPin(GPIOB, GPIO_PIN_9)	 


#define  ADDR_CONF 0x00
#define  ADDR_ALRM 0x01
#define  ADDR_ETC  0x05
#define  ADDR_RESET 0x1D

static void DS1682_IIC_Init(void);
static void DS1682_WriteOneByte(uint8_t WriteAddr,uint8_t DataToWrite);
static uint8_t DS1682_ReadOneByte(uint8_t ReadAddr);

//_calendar_struct ETC_calendar;
//uint8_t rtc_test[32];
//uint8_t MemoryMap_buf[32];
//uint32_t ETC_val;
//uint32_t ETC_second;
//static uint16_t eventRegVal;

//uint16_t BspDs1682_GetCounterValue(void) {
//	return eventRegVal;
//}


//static uint16_t BspDs1682_readEventRegVal(void) {
//	uint8_t i =0;
//	uint16_t regVal;
//	
//	for(i = 0; i < 32; i++) {
//		MemoryMap_buf[i] = DS1682_ReadOneByte(i);
//	}
//	regVal = (((uint16_t)MemoryMap_buf[10]) << 8) | MemoryMap_buf[9];
//	
//	return regVal;
//}

void DS1682_Init(void)
{
	uint8_t i =0;
	DS1682_IIC_Init();
//	eventRegVal = BspDs1682_readEventRegVal();
//	DS1682_WriteOneByte(ADDR_ETC, 100);
//	DS1682_WriteOneByte(ADDR_ETC+1, 0);
//	DS1682_WriteOneByte(ADDR_ETC+2, 0);
//	DS1682_WriteOneByte(ADDR_ETC+3, 0);
//	DS1682_WriteOneByte(ADDR_ALRM, 2000);
//	DS1682_WriteOneByte(ADDR_ALRM+1, 0);
//	DS1682_WriteOneByte(ADDR_ALRM+2, 0);
//	DS1682_WriteOneByte(ADDR_ALRM+3, 0);
//	while(1) {
//		for(i=0; i<32; i++) {
//			MemoryMap_buf[i] = DS1682_ReadOneByte(i);
//		}
//		eventRegVal = (((uint16_t)MemoryMap_buf[10]) << 8) | MemoryMap_buf[9];
//		delay_us(1000);
//	}
//	DS1682_Reset();
//	while(1){
//		ETC_val = DS1682_ReadOneByte(ADDR_ETC);
//		ETC_val = ETC_val + (DS1682_ReadOneByte(ADDR_ETC+1) << 8);
//		ETC_val = ETC_val + (DS1682_ReadOneByte(ADDR_ETC+2) << 16);
//		ETC_val = ETC_val + (DS1682_ReadOneByte(ADDR_ETC+3) << 24);
//		ETC_second = ETC_val/4;
//		ETC_calendar.hour = ETC_second/3600;
//		ETC_calendar.min = (ETC_second%3600) / 60;
//		ETC_calendar.sec = ETC_second%60;
//		delay_ms(20);
//		for(i=0;i<32;i++){
//			MemoryMap_buf[i] = DS1682_ReadOneByte(i);
//		}
//	}
}

//PB6(CLK); PB7(SDA);
static void DS1682_IIC_Init(void)
{
	GPIO_InitTypeDef GPIO_Initure = {0};
    
    __HAL_RCC_GPIOB_CLK_ENABLE();
    
    GPIO_Initure.Pin = GPIO_PIN_8 | GPIO_PIN_9;
    GPIO_Initure.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_Initure.Pull = GPIO_PULLUP;
    GPIO_Initure.Speed = GPIO_SPEED_HIGH;
    HAL_GPIO_Init(GPIOB, &GPIO_Initure);
	
    
    DS1682_IIC_SCL(1);
    DS1682_IIC_SDA(1);
}
//产生IIC起始信号
static void DS1682_IIC_Start(void)
{
	DS1682_IIC_SDA_OUT();     //sda线输出
	DS1682_IIC_SDA(1);	  	  
	DS1682_IIC_SCL(1);
	delay_us(4);
 	DS1682_IIC_SDA(0);//START:when CLK is high,DATA change form high to low 
	delay_us(4);
	DS1682_IIC_SCL(0);//钳住I2C总线，准备发送或接收数据 
}	  
//产生IIC停止信号
static void DS1682_IIC_Stop(void)
{
	DS1682_IIC_SDA_OUT();//sda线输出
	DS1682_IIC_SCL(0);
	DS1682_IIC_SDA(0);//STOP:when CLK is high DATA change form low to high
 	delay_us(4);
	DS1682_IIC_SCL(1); 
	DS1682_IIC_SDA(1);//发送I2C总线结束信号
	delay_us(4);							   	
}
//等待应答信号到来
//返回值：1，接收应答失败
//        0，接收应答成功
static uint8_t DS1682_IIC_Wait_Ack(void)
{
	uint8_t ucErrTime=0;
	DS1682_IIC_SDA_IN();      //SDA设置为输入  
	DS1682_IIC_SDA(1);delay_us(1);	   
	DS1682_IIC_SCL(1);delay_us(1);	 
	while(DS1682_IIC_READ_SDA)
	{
		ucErrTime++;
		if(ucErrTime>250)
		{
			DS1682_IIC_Stop();
			return 1;
		}
	}
	DS1682_IIC_SCL(0);//时钟输出0 	   
	return 0;  
} 
//产生ACK应答
static void DS1682_IIC_Ack(void)
{
	DS1682_IIC_SCL(0);
	DS1682_IIC_SDA_OUT();
	DS1682_IIC_SDA(0);
	delay_us(2);
	DS1682_IIC_SCL(1);
	delay_us(2);
	DS1682_IIC_SCL(0);
}
//不产生ACK应答		    
static void DS1682_IIC_NAck(void)
{
	DS1682_IIC_SCL(0);
	DS1682_IIC_SDA_OUT();
	DS1682_IIC_SDA(1);
	delay_us(2);
	DS1682_IIC_SCL(1);
	delay_us(2);
	DS1682_IIC_SCL(0);
}					 				     
//IIC发送一个字节
//返回从机有无应答
//1，有应答
//0，无应答			  
static void DS1682_IIC_Send_Byte(uint8_t txd)
{                        
    uint8_t t;   
	DS1682_IIC_SDA_OUT(); 	    
    DS1682_IIC_SCL(0);//拉低时钟开始数据传输
    for(t=0;t<8;t++)
    {              
        //DS1682_IIC_SDA=(txd&0x80)>>7;
		if((txd&0x80)>>7)
			DS1682_IIC_SDA(1);
		else
			DS1682_IIC_SDA(0);
		txd<<=1; 	  
		delay_us(2);   //对TEA5767这三个延时都是必须的
		DS1682_IIC_SCL(1);
		delay_us(2); 
		DS1682_IIC_SCL(0);	
		delay_us(2);
    }	 
} 	    
//读1个字节，ack=1时，发送ACK，ack=0，发送nACK   
static uint8_t DS1682_IIC_Read_Byte(unsigned char ack)
{
	unsigned char i,receive=0;
	DS1682_IIC_SDA_IN();//SDA设置为输入
    for(i=0;i<8;i++ )
	{
        DS1682_IIC_SCL(0); 
        delay_us(2);
		DS1682_IIC_SCL(1);
        receive<<=1;
        if(DS1682_IIC_READ_SDA)receive++;   
		delay_us(1); 
    }					 
    if (!ack)
        DS1682_IIC_NAck();//发送nACK
    else
        DS1682_IIC_Ack(); //发送ACK   
    return receive;
}



//uint16_t DismantlingCheck(uint16_t lastValue) {
//	uint8_t i =0;
//	for(i=0; i<32; i++){
//		MemoryMap_buf[i] = DS1682_ReadOneByte(i);
//	}
//	eventRegVal = (((uint16_t)MemoryMap_buf[10]) << 8) | MemoryMap_buf[9];
//	if(eventRegVal != lastValue) {
//		while(1){
//			//等待解锁指令
////			DismantlingUnlock();
//			break;
//		}
//		//解锁指令通过,将新的数据存储进FLASH
////		Need_WriteFlash(0);
//		return eventRegVal;
//	}
//	return 0;
//}

uint16_t DS1682_ReadEventRegVal(void) {
	uint8_t buf[32];
	
	for(uint32_t i = 0; i < 32; i++){
		buf[i] = DS1682_ReadOneByte(i);
	}
	
	return (((uint16_t)buf[10]) << 8) | buf[9];
}


static void DS1682_Reset(void)
{
	uint8_t tmp = 0;
	tmp = DS1682_ReadOneByte(ADDR_CONF);
	DS1682_WriteOneByte(ADDR_CONF, (tmp&0xFB)|0x04);
	DS1682_WriteOneByte(ADDR_CONF, (tmp&0xFB)|0x04);
	delay_ms(2);
	DS1682_WriteOneByte(ADDR_RESET, 0x55);
	delay_ms(2);
	DS1682_WriteOneByte(ADDR_RESET, 0x55);
	delay_ms(2);
//	tmp = DS1682_ReadOneByte(ADDR_CONF);
//	DS1682_WriteOneByte(ADDR_CONF, (tmp&0xF7)|0x08);
}
//在DS1682指定地址读出一个数据
//ReadAddr:开始读数的地址  
//返回值  :读到的数据
static uint8_t DS1682_ReadOneByte(uint8_t ReadAddr)
{				  
	uint8_t temp=0;
	// write addr pionter
	DS1682_IIC_Start();
	DS1682_IIC_Send_Byte(0XD6);	   // write
	DS1682_IIC_Wait_Ack();
	DS1682_IIC_Send_Byte(ReadAddr);	   //发送写命令
	DS1682_IIC_Wait_Ack();
	DS1682_IIC_Stop();
	DS1682_IIC_Start();  
	// read the addr data
	DS1682_IIC_Send_Byte(0XD7);	   // read
	DS1682_IIC_Wait_Ack(); 
	temp=DS1682_IIC_Read_Byte(0);
	DS1682_IIC_Stop(); 
	return temp;
}
//在DS1682指定地址写入一个数据
//WriteAddr  :写入数据的目的地址    
//DataToWrite:要写入的数据
static void DS1682_WriteOneByte(uint8_t WriteAddr,uint8_t DataToWrite)
{				   	  	    																 
	DS1682_IIC_Start();  
	DS1682_IIC_Send_Byte(0XD6);	   // write
	DS1682_IIC_Wait_Ack();	   
	DS1682_IIC_Send_Byte(WriteAddr);   //发送低地址
	DS1682_IIC_Wait_Ack(); 	 										  		   
	DS1682_IIC_Send_Byte(DataToWrite);     //发送字节							   
	DS1682_IIC_Wait_Ack();  		    	   
	DS1682_IIC_Stop();//产生一个停止条件 
	delay_us(10*1000);//针对综合实验做出的改动，原来为delay_ms(10)		 
}
//在DS1682里面的指定地址开始写入长度为Len的数据
//该函数用于写入16bit或者32bit的数据.
//WriteAddr  :开始写入的地址  
//DataToWrite:数据数组首地址
//Len        :要写入数据的长度2,4
static void DS1682_WriteLenByte(uint16_t WriteAddr,uint32_t DataToWrite,uint8_t Len)
{  	
	uint8_t t;
	for(t=0;t<Len;t++)
	{
		DS1682_WriteOneByte(WriteAddr+t,(DataToWrite>>(8*t))&0xff);
	}												    
}

//在DS1682里面的指定地址开始读出长度为Len的数据
//该函数用于读出16bit或者32bit的数据.
//ReadAddr   :开始读出的地址 
//返回值     :数据
//Len        :要读出数据的长度2,4
static uint32_t DS1682_ReadLenByte(uint16_t ReadAddr,uint8_t Len)
{  	
	uint8_t t;
	uint32_t temp=0;
	for(t=0;t<Len;t++)
	{
		temp<<=8;
		temp+=DS1682_ReadOneByte(ReadAddr+Len-t-1); 	 				   
	}
	return temp;												    
}

//在DS1682里面的指定地址开始读出指定个数的数据
//ReadAddr :开始读出的地址 对24c02为0~255
//pBuffer  :数据数组首地址
//NumToRead:要读出数据的个数
static void DS1682_Read(uint16_t ReadAddr,uint8_t *pBuffer,uint16_t NumToRead)
{
	while(NumToRead)
	{
		*pBuffer++=DS1682_ReadOneByte(ReadAddr++);	
		NumToRead--;
	}
}  
//在DS1682里面的指定地址开始写入指定个数的数据
//WriteAddr :开始写入的地址 对24c02为0~255
//pBuffer   :数据数组首地址
//NumToWrite:要写入数据的个数
static void DS1682_Write(uint16_t WriteAddr,uint8_t *pBuffer,uint16_t NumToWrite)
{
	while(NumToWrite--)
	{
		DS1682_WriteOneByte(WriteAddr,*pBuffer);
		WriteAddr++;
		pBuffer++;
	}
}





