/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : ui_factory.c
  * @project : 
  * @brief   : 
  * <AUTHOR> PR Team
  * @since   : 2024/08/26
  * @version : V1.0 
  * @history :
  *	2024/08/26: V0.1
  *	  1) 首次创建;
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "ui_factory.h"
#include <string.h>
#include <stdio.h>
#include <stdlib.h>
#include "gui_lib.h"
#include "ds1682.h"
#include "data_persistence.h"
#include "key.h"


//void UiFactory_ShowFactoryMainUi(void) {
//	char str[32];
//	GUI_RECT rCanvas = {0, 0, SCREEN_WIDTH - 1 - 64, SCREEN_HEIGHT - 1};
//	
//	GUI_ClearScreen();
//	GUI_SetColor(15);
//	GUI_SetTextAlign(GUI_TA_VCENTER | GUI_TA_HCENTER);
//	GUI_DispStringAt("No Wave", (rCanvas.x0 + rCanvas.x1)/2, (rCanvas.y0 + rCanvas.y1)/2);
//	
//	GUI_InvalidateRect(&rCanvas);
//	GUI_Exec();
//}


static void UiFactory_MainTask(void) {
	uint32_t valInFlash = 78956;
	uint16_t valInDs1682 = DS1682_ReadEventRegVal();
	DataPersistence_ReadSelfLockVal(&valInFlash);

	uint8_t keyId = 0;
	uint8_t step = 0;
	char str[64];
	
	GUI_ClearScreen();
	
	snprintf(str, sizeof(str), "%d/%d", valInDs1682, valInFlash);
	GUI_SetColor(15);
	GUI_DispStringAt(str, 2, 2);
	GUI_InvalidateScreen();
	GUI_Exec();
	
	while(1) {
		
		if((keyId = KEY_Scan(0)) == KEY_NULL) continue;
		
		if(keyId == KEY_M_PRES) {
			return;
		}
	}
	
}

uint8_t UiFactory_EnterMainTaskDetection(uint8_t keyId) {
	static uint8_t step = 0;
	
	if(keyId == KEY_NULL) return 0;
	
	if(keyId == KEY_UP_PRES) {
		step++;
		if(step > 10) {
			UiFactory_MainTask();
			step = 0;
			return 1;
		}
	}
	else {
		step = 0;
	}
	
	return 0;
}



/******************************* end of file **********************************/
