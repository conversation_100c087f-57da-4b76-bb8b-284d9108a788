/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : gui_lib.c
  * @project : 
  * @brief   : 
  * <AUTHOR> PR Team
  * @since   : 2024/08/15
  * @version : V1.0 
  * @history :
  *	2024/08/15: V0.1
  *	  1) 首次创建;
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "gui_lib.h"
#include <string.h>
#include "oled.h"
#include "delay.h"
#include "oledfont.h"

#define USE_DISPLAY_MEMORY	1

static const uint8_t *pFont;
static uint32_t penColor = 15;
static uint8_t displayMemory[SCREEN_HEIGHT][SCREEN_WIDTH/2]; //64x256
static GUI_RECT invaliatedDispMemRect;
static uint8_t fontWidth = 12;
static uint8_t fontHeight = 24;

static int32_t textAlign = GUI_TA_LEFT | GUI_TA_TOP;

void GUI_ClearScreen(void) {
	#if USE_DISPLAY_MEMORY
	memset(displayMemory, 0, sizeof(displayMemory));
	#else
	Oled_ClearScreen();
	#endif
}

//color: 0~15
void GUI_SetColor(uint32_t color) {
	penColor = color;
}

void GUI_SetTextAlign(int32_t align) {
	textAlign = align;
}

void GUI_DrawPixel(int32_t x, int32_t y) {
	#if USE_DISPLAY_MEMORY
	uint8_t xInMem = x / 2;
	
	if(y < 0 || y >= SCREEN_HEIGHT) return;
	if(xInMem < 0 || xInMem >= SCREEN_WIDTH/2) return;
	
	if(2 * xInMem == x)
		displayMemory[y][xInMem] = ((penColor << 4) & 0xf0) | (displayMemory[y][xInMem] & 0x0f);
	else
		displayMemory[y][xInMem] = (penColor & 0x0f) | (displayMemory[y][xInMem] & 0xf0);
	#else
	Oled_DrawPixel(x, y, penColor);
	#endif
}

void GUI_DrawLine(int32_t x0, int32_t y0, int32_t x1, int32_t y1) {
    double k, b;
    int32_t x, y;
	int32_t xBgn, xEnd;
	int32_t yBgn, yEnd;
	int32_t xPixs = x1 - x0 + 1;
	int32_t yPixs = y1 - y0 + 1;
	
	xBgn = x0 < x1 ? x0 : x1;
	xEnd = x0 < x1 ? x1 : x0;
	yBgn = y0 < y1 ? y0 : y1;
	yEnd = y0 < y1 ? y1 : y0;
	
	if(x1 == x0) {
		for(y = yBgn; y <= yEnd; y++) {
			GUI_DrawPixel(x0, y);
		}
		return;
	}
	
	if(y1 == y0) {
		for(x = xBgn; x <= xEnd; x++) {
			GUI_DrawPixel(x, y0);
		}
		return;
	}
	
	k = (y1 - y0) / (x1 - x0);
	b = y0 - k * x0;
	
	for(x = xBgn; x <= xEnd; x++) {
		GUI_DrawPixel(x, k*x+b + 0.51);
	}
	
	for(y = yBgn; y <= yEnd; y++) {
		GUI_DrawPixel((y-b)/k + 0.51, y);
	}
}

void GUI_DrawPolyLine(const GUI_POINT * pPoints, int numPoints, int x0, int y0) {
	for (int32_t i = 1; i < numPoints; i++) {
		GUI_DrawLine(pPoints[i - 1].x + x0, pPoints[i - 1].y + y0, pPoints[i].x + x0, pPoints[i].y + y0);
	}
}

void GUI_DrawRect(int32_t x0, int32_t y0, int32_t x1, int32_t y1) {
    GUI_DrawLine(x0, y0, x1, y0);
	GUI_DrawLine(x0, y1, x1, y1);
	GUI_DrawLine(x0, y0, x0, y1);
	GUI_DrawLine(x1, y0, x1, y1);
}

const uint8_t * GUI_SetFont(const uint8_t * pNewFont) {
	pFont = pNewFont;
	return pFont;
}

void GUI_DispCharAt(char chr, int32_t x, int32_t y) {
    unsigned char c = 0;
	int32_t i = 0, k = 0;
    int32_t m, x1, y1, y2, y3, y4;
    uint8_t Data1, Data2 = 0;
    c = chr - ' ';

    for (k = 0; k < 3; k++) {
        x1 = x;
        y1 = y + 8 * k;
        for (m = 0; m < 6; m++) {

            Data1 = F12X24[c * 36 + 2 * m + 12 * k];
            Data2 = F12X24[c * 36 + 2 * m + 12 * k + 1];
            for (i = 0; i < 8; i++) {
                if (Data1 & (0x01 << i)) {
                    GUI_DrawPixel(x1, y1);
                }
                if (Data2 & (0x01 << i)) {
                    GUI_DrawPixel(x1 + 1, y1);
                }
				
				y1++;
            }
            x1 += 2;
            y1 = y + 8 * k;
        }
    }
}


void GUI_DispStringAt(const char * s, int x, int y) {
	int32_t vAlign = textAlign & 0x0C;
	int32_t hAlign = textAlign & 0x03;
	
	if(vAlign == GUI_TA_TOP) y = y;
	else if(vAlign == GUI_TA_BOTTOM) y = y - fontHeight;
	else if(vAlign == GUI_TA_VCENTER) y = y - fontHeight / 2;
	
	if(hAlign == GUI_TA_LEFT) x = x;
	else if(hAlign == GUI_TA_RIGHT) x = x - fontWidth * strlen(s);
	else if(hAlign == GUI_TA_HCENTER) x = x - (fontWidth * strlen(s)) / 2;

	for(uint32_t i = 0; i < strlen(s); i++) {
		GUI_DispCharAt(s[i], x + i * fontWidth, y);
	}
}


void GUI_InvalidateScreen(void) {
	invaliatedDispMemRect.x0 = 0;
	invaliatedDispMemRect.y0 = 0;
	invaliatedDispMemRect.x1 = SCREEN_WIDTH - 1;
	invaliatedDispMemRect.y1 = SCREEN_HEIGHT - 1;
}

void GUI_InvalidateRect(GUI_RECT *pRect) {
	invaliatedDispMemRect.x0 = invaliatedDispMemRect.x0 <= pRect->x0 ? invaliatedDispMemRect.x0 : pRect->x0;
	invaliatedDispMemRect.y0 = invaliatedDispMemRect.y0 <= pRect->y0 ? invaliatedDispMemRect.y0 : pRect->y0;
	invaliatedDispMemRect.x1 = invaliatedDispMemRect.x1 >= pRect->x1 ? invaliatedDispMemRect.x1 : pRect->x1;
	invaliatedDispMemRect.y1 = invaliatedDispMemRect.y1 >= pRect->y1 ? invaliatedDispMemRect.y1 : pRect->y1;
}

void GUI_Exec(void) {
	Oled_CopyDispMemToScreenRect(displayMemory, invaliatedDispMemRect.x0, invaliatedDispMemRect.y0, invaliatedDispMemRect.x1, invaliatedDispMemRect.y1);
//	Oled_CopyDispMemToScreen(displayMemory);
	memset((uint8_t *)&invaliatedDispMemRect, 0, sizeof(invaliatedDispMemRect));
}

//void GUI_DisplayImmediately(void) {
//	Oled_CopyDispMemToScreen(displayMemory);
//}

void GUI_Init(void) {
//	while(1) {
//		ClearLED(0x00);
//		for(uint32_t i = 0; i < 256; i ++) {
//			for(uint32_t j = 0; j < 64; j ++) {
//				drawpoint(i, j, 1);
//				delay_us(100);
//			}
//		}
//		
//		delay_ms(100);
//		ClearLED(0x00);
//		
//		for(uint32_t i = 0; i < 256; i ++) {
//			for(uint32_t j = 0; j < 64; j ++) {
//				Oled_DrawPixel(i, j, 1);
//				delay_us(100);
//			}
//		}
//		delay_ms(100);
//	}
//	
//	while(1) {
//		for(uint32_t i = 0; i < 256; i ++) {
//			Oled_DrawPixel(i, 0, 1);
//		}
//	}
//	while(1) {
//		GUI_DispCharAt('a', 0, 0);
//		GUI_DispCharAt('7', 20, 20);
//		
//		GUI_DispStringAt("abc123#", 50, 10);
//		GUI_Exec();
//	}

//	while(1) {
//		GUI_SetColor(15);
//		GUI_DrawLine(10, 10, 20, 20);
//		
//		GUI_DrawLine(30, 30, 40, 20);
//		
//		GUI_Exec();
//	}
	
//	while(1) {
//		GUI_SetColor(1);
//		GUI_DrawLine(0, 0, 128, 0);
//		GUI_InvalidateScreen();
//		
//		GUI_SetColor(3);
//		GUI_DrawLine(10, 10, 254, 10);
//		GUI_InvalidateScreen();
//		
////		GUI_SetColor(15);
////		GUI_DrawLine(2, 2, 254, 62);
////		GUI_InvalidateScreen();
//		
//		GUI_SetColor(15);
//		GUI_DrawLine(0, 0, 255, 63);
//		GUI_InvalidateScreen();
//		
//		GUI_SetColor(15);
//		GUI_DrawLine(128, 10, 128, 72);
//		GUI_InvalidateScreen();
//	}
}

/******************************* end of file **********************************/
