#ifndef __APP_GUI_H
#define __APP_GUI_H
#include <stdint.h>


#define GUI_LEVEL_MAIN 	0
#define GUI_LEVEL_MENU 	1


typedef struct { 
	uint8_t isDispWave; 
	uint8_t isDispWaveEnvelope; 
} THK_HMI_T;

extern THK_HMI_T g_ThkHmi;

void UI_ShowAGC(void);
void UI_ClearAGC(void);

uint8_t OLED_ShowLargeNumber(uint32_t num);
void Oled_ShowWifiName(char *str);
//void Oled_ShowMainScreen(uint8_t pause, uint8_t wState);
uint32_t CommonVelcocity_Show(uint8_t lang, uint8_t id, uint32_t velcity_old);
void Thk_DispMainUI(void);
//void GuiThk_DispMainUiForMeasureing(void);

uint8_t HMI_Manage(void);

#endif
