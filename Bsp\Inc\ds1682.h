#ifndef __DS1682_H
#define __DS1682_H
#include <stdint.h>


//void DS1682_Reset(void);

					  
//uint8_t DS1682_ReadOneByte(uint8_t ReadAddr);							//指定地址读取一个字节
//void DS1682_WriteOneByte(uint8_t WriteAddr,uint8_t DataToWrite);		//指定地址写入一个字节
//void DS1682_WriteLenByte(uint16_t WriteAddr,uint32_t DataToWrite,uint8_t Len);//指定地址开始写入指定长度的数据
//uint32_t DS1682_ReadLenByte(uint16_t ReadAddr,uint8_t Len);					//指定地址开始读取指定长度数据
//void DS1682_Write(uint16_t WriteAddr,uint8_t *pBuffer,uint16_t NumToWrite);	//从指定地址开始写入指定长度的数据
//void DS1682_Read(uint16_t ReadAddr,uint8_t *pBuffer,uint16_t NumToRead);   	//从指定地址开始读出指定长度的数据

void DS1682_Init(void); //初始化IIC
//uint16_t DismantlingCheck(uint16_t lastValue);
//uint16_t BspDs1682_GetCounterValue(void);
uint16_t DS1682_ReadEventRegVal(void);

#endif
















