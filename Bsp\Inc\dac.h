#ifndef __DAC_H
#define __DAC_H	 
#include <stdint.h>
#include "fpga.h"

//#define DAC_REF_VOL	2500 	// mV
//#define GAINx10_MAX 	555 	// x0.1dB
//#define GAINx10_MIN 	75		// x0.1dB
//#define GAINx10_MAX 	1110 //555 // x0.1dB
//#define GAINx10_MIN 	150 //75 // x0.1dB

#define GAINx10_MAX 	1110 //555 // x0.1dB
#define GAINx10_MIN 	150 //75 // x0.1dB

#define ACG_VAL			(0.8 * FPGA_ADC_MAX_VAL) // 8192, 最大2048(1600)
#define ACG_VAL_ERR		(0.05 * FPGA_ADC_MAX_VAL) //100 //
//#define AUTO_ACG_MAX	1850 //2048*0.9
//#define AUTO_ACG_MIN	512



void Dac1_Init(void);		//DAC通道1初始化	 	 
//void Dac1_Set_Vol(uint16_t vol);	//设置通道1输出电压
//uint32_t Gain_Voltage(int32_t gainX10);
//uint16_t ACG(uint16_t  avgTimes, uint8_t sampRate);
uint16_t AGC_stress(uint16_t  avgTimes, uint8_t sampRate);
void SetGain(uint32_t dBx10);

//uint16_t  getAcgFailedGain();
//uint8_t checkNeedACG();
uint8_t checkNeedAGC_stress();
uint8_t Gain_AGC_isDone(int32_t max_val, int32_t *gain_x10);

#endif

















