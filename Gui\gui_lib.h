/**
  ******************************************************************************
  * @file    ui_draw_wave.h
  * @brief   This file contains all the function prototypes for
  *          the ui_draw_wave.c file
  ******************************************************************************
  * @attention
  *
  ******************************************************************************
  */
#ifndef __GUI_LIB_H__
#define __GUI_LIB_H__

#ifdef __cplusplus
extern "C" {
#endif
#include <stdint.h>

#define SCREEN_WIDTH 		256
#define SCREEN_HEIGHT 		64

typedef struct { int16_t x, y; } GUI_POINT;
typedef struct { int16_t x0, y0, x1, y1; } GUI_RECT;


/* Text alignment flags, horizontal */
#define GUI_TA_LEFT       (0)
#define GUI_TA_HORIZONTAL (3u)
#define GUI_TA_RIGHT      (1u)
#define GUI_TA_CENTER     (2u)
#define GUI_TA_HCENTER    GUI_TA_CENTER  /* easier to remember :-)  */

/* Text alignment flags, vertical */
#define GUI_TA_TOP        (0)
#define GUI_TA_VERTICAL   (3u << 2)
#define GUI_TA_BOTTOM     (1u << 2)
#define GUI_TA_BASELINE   (2u << 2)
#define GUI_TA_VCENTER    (3u << 2)


void GUI_Init(void);
void GUI_DrawPixel(int32_t x, int32_t y);
void GUI_SetColor(uint32_t color);
void GUI_SetTextAlign(int32_t align);
void GUI_ClearScreen(void);
void GUI_DrawLine(int32_t x0, int32_t y0, int32_t x1, int32_t y1);
void GUI_DrawRect(int32_t x0, int32_t y0, int32_t x1, int32_t y1);
void GUI_DrawPolyLine(const GUI_POINT * pPoints, int numPoints, int x0, int y0);
void GUI_DispCharAt(char chr, int32_t x, int32_t y);
void GUI_DispStringAt(const char * s, int x, int y);

void GUI_Exec(void);
void GUI_InvalidateRect(GUI_RECT *pRect);
void GUI_InvalidateScreen(void);

#ifdef __cplusplus
}
#endif
#endif

/******************************* end of file **********************************/