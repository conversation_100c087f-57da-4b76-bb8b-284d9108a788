#include "fmc.h"
#include "delay.h"
#include "stm32f7xx_hal.h"
#include "bsp.h"

uint16_t test_ReadStateReg(void);

static SRAM_HandleTypeDef hsram1;

//使用NOR/SRAM的 Bank1.sector3,地址位HADDR[27,26]=10
//对IS61LV25616/IS62WV25616,地址线范围为A0~A17
//对IS61LV51216/IS62WV51216,地址线范围为A0~A18
//#define Bank1_SRAM3_ADDR ((u32)(0x68000000))

//配置MPU的region(SRAM区域为透写模式)
static void MPU_Config(void) {
    MPU_Region_InitTypeDef MPU_Initure;

    HAL_MPU_Disable(); //配置MPU之前先关闭MPU,配置完成以后在使能MPU
    //外部SRAM为region0，大小为2MB，此区域可读写
    MPU_Initure.Enable = MPU_REGION_ENABLE;      //使能region
    MPU_Initure.Number = LCD_REGION_NUMBER;      //设置region，外部SRAM使用的region0
    MPU_Initure.BaseAddress = LCD_ADDRESS_START; // region基地址
    MPU_Initure.Size = LCD_REGION_SIZE;          // region大小
    MPU_Initure.SubRegionDisable = 0X00;
    MPU_Initure.TypeExtField = MPU_TEX_LEVEL0;
    MPU_Initure.AccessPermission = MPU_REGION_FULL_ACCESS;   //此region可读写
    MPU_Initure.DisableExec = MPU_INSTRUCTION_ACCESS_ENABLE; //允许读取此区域中的指令
    MPU_Initure.IsShareable = MPU_ACCESS_NOT_SHAREABLE;
    MPU_Initure.IsCacheable = MPU_ACCESS_NOT_CACHEABLE;
    MPU_Initure.IsBufferable = MPU_ACCESS_BUFFERABLE;
    HAL_MPU_ConfigRegion(&MPU_Initure);
    HAL_MPU_Enable(MPU_PRIVILEGED_DEFAULT); //开启MPU
}

//初始化外部SRAM
void FPGA_FMC_DataRxTx_Init(void) {
	FMC_NORSRAM_TimingTypeDef readWriteTiming = {0};
	FMC_NORSRAM_TimingTypeDef writeTiming = {0};

	MPU_Config(); //使能MPU保护LCD区域
	/** Perform the SRAM1 memory initialization sequence
	*/
	hsram1.Instance = FMC_NORSRAM_DEVICE;
	hsram1.Extended = FMC_NORSRAM_EXTENDED_DEVICE;
	/* hsram1.Init */
	hsram1.Init.NSBank = FMC_NORSRAM_BANK1;
	hsram1.Init.DataAddressMux = FMC_DATA_ADDRESS_MUX_DISABLE;
	hsram1.Init.MemoryType = FMC_MEMORY_TYPE_SRAM;
	hsram1.Init.MemoryDataWidth = FMC_NORSRAM_MEM_BUS_WIDTH_16;
	hsram1.Init.BurstAccessMode = FMC_BURST_ACCESS_MODE_DISABLE;
	hsram1.Init.WaitSignalPolarity = FMC_WAIT_SIGNAL_POLARITY_LOW;
	hsram1.Init.WaitSignalActive = FMC_WAIT_TIMING_BEFORE_WS;
	hsram1.Init.WriteOperation = FMC_WRITE_OPERATION_ENABLE;
	hsram1.Init.WaitSignal = FMC_WAIT_SIGNAL_DISABLE;
	hsram1.Init.ExtendedMode = FMC_EXTENDED_MODE_ENABLE;
	hsram1.Init.AsynchronousWait = FMC_ASYNCHRONOUS_WAIT_DISABLE;
	hsram1.Init.WriteBurst = FMC_WRITE_BURST_DISABLE;
	hsram1.Init.ContinuousClock = FMC_CONTINUOUS_CLOCK_SYNC_ASYNC;
	hsram1.Init.WriteFifo = FMC_WRITE_FIFO_ENABLE;
	hsram1.Init.PageSize = FMC_PAGE_SIZE_NONE;
	
	/* Timing */
	readWriteTiming.AddressSetupTime = 0 + 2;
	readWriteTiming.AddressHoldTime = 1 + 2;
	readWriteTiming.DataSetupTime = 1 + 2;
//	readWriteTiming.BusTurnAroundDuration = 0;
//	readWriteTiming.CLKDivision = 2;
//	readWriteTiming.DataLatency = 2;
	readWriteTiming.AccessMode = FMC_ACCESS_MODE_A;
	
	/* FMC写时序控制寄存器 */
    writeTiming.AddressSetupTime = 0 + 2; //地址建立时间(ADDSET)为21个HCLK=105ns
    writeTiming.AddressHoldTime = 1 + 2;
    writeTiming.DataSetupTime = 1 + 2; //数据保存时间(DATAST)为5ns*21个HCLK=105ns
//	writeTiming.CLKDivision = 2;
//	writeTiming.DataLatency = 2;
    writeTiming.AccessMode = FMC_ACCESS_MODE_A; //模式A

	if (HAL_SRAM_Init(&hsram1, &readWriteTiming, &writeTiming) != HAL_OK)
	{
		Bsp_ErrorHandler();
	}

	delay_us(50000); // delay 50 ms
}


static uint32_t FMC_Initialized = 0;

static void HAL_FMC_MspInit(void){
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	if (FMC_Initialized) {
		return;
	}
	FMC_Initialized = 1;

	/* Peripheral clock enable */
	__HAL_RCC_FMC_CLK_ENABLE();
	__HAL_RCC_GPIOD_CLK_ENABLE();
	__HAL_RCC_GPIOE_CLK_ENABLE();
	
	//CS/NE1
	GPIO_InitStruct.Pin = GPIO_PIN_7; //CS
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
    FPGA_FMC_CS(1);

	/** FMC GPIO Configuration
	PE7   ------> FMC_DA4
	PE8   ------> FMC_DA5
	PE9   ------> FMC_DA6
	PE10  ------> FMC_DA7
	PE11  ------> FMC_DA8
	PE12  ------> FMC_DA9
	PE13  ------> FMC_DA10
	PE14  ------> FMC_DA11
	PE15  ------> FMC_DA12
	PD8   ------> FMC_DA13
	PD9   ------> FMC_DA14
	PD10  ------> FMC_DA15
	PD14  ------> FMC_DA0
	PD15  ------> FMC_DA1
	PD0   ------> FMC_DA2
	PD1   ------> FMC_DA3
	PD4   ------> FMC_NOE
	PD5   ------> FMC_NWE
	PD7   ------> FMC_NE1
	*/
	/* GPIO_InitStruct */
	GPIO_InitStruct.Pin = GPIO_PIN_7|GPIO_PIN_8|GPIO_PIN_9|GPIO_PIN_10
		|GPIO_PIN_11|GPIO_PIN_12|GPIO_PIN_13|GPIO_PIN_14
		|GPIO_PIN_15;
	GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	GPIO_InitStruct.Alternate = GPIO_AF12_FMC;

	HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);

	/* GPIO_InitStruct */
	GPIO_InitStruct.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_4 | GPIO_PIN_5 | GPIO_PIN_14 | GPIO_PIN_15
		|GPIO_PIN_8 | GPIO_PIN_9 | GPIO_PIN_10;
	GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	GPIO_InitStruct.Alternate = GPIO_AF12_FMC;

	HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
}

void HAL_SRAM_MspInit(SRAM_HandleTypeDef* sramHandle){
  HAL_FMC_MspInit();
}

uint16_t es2b[3], content2b;
uint16_t test_ReadStateReg(void) {
//	static uint16_t es2b[3], content2b;
	FPGA_FMC_CS(0);
	*(volatile uint8_t *)FMC_FPGA_BASE_ADDR = 0xB0;
//	*(volatile uint8_t *)FMC_FPGA_BASE_ADDR = 0x55;
	delay_us(1); 
	es2b[0] = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	es2b[1] = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	es2b[2] = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	content2b = *(volatile uint16_t *)FMC_FPGA_BASE_ADDR;
	FPGA_FMC_CS(1);
	return content2b;
}


//在指定地址(WriteAddr+Bank1_SRAM3_ADDR)开始,连续写入n个字节.
// pBuffer:字节指针
// WriteAddr:要写入的地址
// n:要写入的字节数
void FSMC_SRAM_WriteBuffer(uint8_t *pBuffer, uint32_t WriteAddr, uint32_t n) {
    for (; n != 0; n--) {
        *(volatile uint8_t *)(FMC_FPGA_BASE_ADDR + WriteAddr) = *pBuffer;
        WriteAddr++;
        pBuffer++;
    }
}
//在指定地址((WriteAddr+Bank1_SRAM3_ADDR))开始,连续读出n个字节.
// pBuffer:字节指针
// ReadAddr:要读出的起始地址
// n:要写入的字节数
void FSMC_SRAM_ReadBuffer(uint8_t *pBuffer, uint32_t ReadAddr, uint32_t n) {
    for (; n != 0; n--) {
        *pBuffer++ = *(volatile uint8_t *)(FMC_FPGA_BASE_ADDR + ReadAddr);
        ReadAddr++;
    }
}
