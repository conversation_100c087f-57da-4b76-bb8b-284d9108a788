/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : data_persistence.c
  * @project : 
  * @brief   : 
  * <AUTHOR> PR Team
  * @since   : 2024/07/30
  * @version : V1.0 
  * @history :
  *	2024/07/30: V0.1
  *	  1) 首次创建;
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */
#include "data_persistence.h"
#include <string.h>
#include "24cxx.h"
#include "app.h"
#include "app_thickness.h"
#include "emat.h"
#include "bsp_zpt.h"

//应用部分
/***
// 32bits 每个参数, 地址加4
// 0~4:N/A; 5:上次声速; 6:亮度; 7:语言; 8:单位制; 9:校准试块的厚度(1000=10.00mm); 
// 10:用户存储声速1; 11:用户存储声速2; 12:用户存储声速3; 
//用户声速总共4字节, 高2字节存放温度, 低2字节存放声速
	____________________________________________________________________________________________________________
	|0 |1      |2-3|4       |5    |6         |7       |8     |9              |10       |11        |12        |13       |14       |
	------------------------------------------------------------------------------------------------------------
	|  |HDCode |N/A|DevFunc |velc |brightness|Language|Unit  |cali thickness |usr velc1|usr velc2 |usr velc3 |材料序号 |wifi开关 |
	------------------------------------------------------------------------------------------------------------
	______________________________________________________________________________________________________________
	|15      |16     |17           |18          |19           |20          |21~23 | 24、25、26、27      |
	------------------------------------------------------------------------------------------------------------
	|Average |参考位 |拆机次数记录 |零点偏移,ns |相关信号零点 |EmitAB Phase|SN    | (未用)|
	--------------------------------------------------------------------------------------------------------------
    __________________
	|30         |31     |
	------------------
	|芯片验证码 |保留   |
    --------------------
	__________________
	|47       |48     |
	------------------
	|         |保留   |
    --------------------

	//400~449 系统数据区（Bootloader使用,App禁止修改，同时Bootloader禁止使用以外的数据区间）
	___________________________________________________________________________________________________
	| 400-409 | 410      | 411-419 | 420           | 425-428    | 430         |  431~449   |  
	---------------------------------------------------------------------------------------------------
	| 保留    | 硬件代号 | 保留    | 软件代号(int) | 软件版本名 | App有效标志 |   保留     |
	---------------------------------------------------------------------------------------------------
	hardwareCodeWrite = 0x03000301; //0x0300-03-01(系列号-硬件大版本-硬件小版本)(HD 2020版本)
	hardwareCodeWrite = 0x03000302; //0x0300-03-02(系列号-硬件大版本-硬件小版本)(HD 2023版本)
	
***/
#define USE_FLASH_SIZE	24

#define OFFSET_ADDR_VERIFY_ENCODE	30

static uint32_t flashBuf[USE_FLASH_SIZE];


uint8_t DataPersistence_Init(void) {
	
	return 0;
}

uint8_t DataPersistence_SaveSelfLockVal(uint32_t val) {
	if(AT24CXX_Write4BytesChecked(17 * 4, val) != 0) return 1; //failed
	
	return 0; //ok
}

uint8_t DataPersistence_ReadSelfLockVal(uint32_t *retVal) {
	if(AT24CXX_Read4BytesChecked(17 * 4, retVal) != 0) return 1; //failed
	
	return 0; //ok
}


uint8_t DataPers_WriteWifiEnable(uint32_t enable) {
	return AT24CXX_Write4BytesChecked(14*4, enable);
}

uint8_t DataPers_ReadWifiEnable(uint32_t *retVal) {
	return AT24CXX_Read4BytesChecked(14*4, retVal);
}

uint8_t DataPers_WriteSnCode(char *strSnCode) {
	uint8_t res = 0;

	res += AT24CXX_Write4BytesChecked(21*4, *(uint32_t *)strSnCode);
	res += AT24CXX_Write4BytesChecked(22*4, *((uint32_t *)strSnCode + 1));
	res += AT24CXX_Write4BytesChecked(23*4, *((uint32_t *)strSnCode + 2));
	
	return res;
}

uint8_t DataPers_ReadSnCode(char *retSnCode, uint32_t size) {
	uint8_t res = 0;
	char str[16] = {0};
	
	res += AT24CXX_Read4BytesChecked(21*4, (uint32_t *)str);
	res += AT24CXX_Read4BytesChecked(22*4, (uint32_t *)str + 1);
	res += AT24CXX_Read4BytesChecked(23*4, (uint32_t *)str + 2);
	
	if(strlen(str) > size) return 1; //failed
	
	memcpy(retSnCode, str, strlen(str));
	
	return res;
}

uint8_t DataPers_ReadUidEncode(uint32_t *retEncode) {
	uint32_t readVal;
	
	if(AT24CXX_Read4BytesChecked(OFFSET_ADDR_VERIFY_ENCODE*4, &readVal) == 0) {
		*retEncode = readVal;
		return 0; //ok
	}
	
	return 1;
}

uint8_t DataPers_WriteUidEncode(uint32_t encode) {
	return AT24CXX_Write4BytesChecked(OFFSET_ADDR_VERIFY_ENCODE*4, encode);
}

uint32_t DataPersistence_ReadAllDatas(void) {
	uint32_t res = 0;
	uint32_t i = 0;
	uint32_t tmpU32 = 0;
	
	
	memset(flashBuf, 0, sizeof(flashBuf));
	for(i = 0; i < USE_FLASH_SIZE; i++) {
		if(AT24CXX_Read4BytesChecked(i * 4, &tmpU32) == 0) {
			flashBuf[i] = tmpU32;
		}
		else {
			flashBuf[i] = 0;
			res++;
		}
	}
	
	Dev_SetFunction(flashBuf[4]);
	g_Thk.ultraSonic_Velocity 				= flashBuf[5];
	g_Dev.brightness_val 					= flashBuf[6] > 100 || flashBuf[6] <= 0 ? 100 : flashBuf[6];
	g_Dev.lang_type 						= flashBuf[7];
	g_Dev.unit_type 						= flashBuf[8];
	g_Thk.caliThicknessVal 					= (float)flashBuf[9]/100;
	g_Thk.usrSaveVelocity_1 				= flashBuf[10];
	g_Thk.usrSaveVelocity_2 				= flashBuf[11];
	g_Thk.usrSaveVelocity_3 				= flashBuf[12];
	g_Thk.pre_velc_id 						= flashBuf[13];
	g_Dev.wifiState 						= flashBuf[14];
	g_Emat.US_avgTimes 						= flashBuf[15];
	g_Thk.referenceBitOn 					= flashBuf[16];
	g_Dev.dismantlingTimesLocal 			= flashBuf[17];
	Zpt_SetZptTimeNs(flashBuf[18]); 
	g_Emat.Emit_AB_phase 					= flashBuf[20];//发射AB相位
	g_Emat.Emit_brakePulNum	= 0;
	*(uint32_t *)(g_Dev.deviceSN + 4*0) 	= flashBuf[21];
	*(uint32_t *)(g_Dev.deviceSN + 4*1) 	= flashBuf[22];
	*(uint32_t *)(g_Dev.deviceSN + 4*2) 	= flashBuf[23];

	g_Dev.deviceSN[0] = 'P'; g_Dev.deviceSN[1] = 'R'; g_Dev.deviceSN[2] = 'E'; g_Dev.deviceSN[3] = 'M';
	g_Dev.deviceSN[4] = 'A'; g_Dev.deviceSN[5] = 'T'; g_Dev.deviceSN[6] = '3'; g_Dev.deviceSN[7] = '#';
	
	return res;
}


// 用于判断是否需要存储数据到FLASH
uint32_t DataPersistence_SaveAllDatas(uint8_t isForced)
{
	uint32_t res = 0;

	res += AT24CXX_Write4BytesChecked( 4 * 4, g_Dev.functionMode);
	res += AT24CXX_Write4BytesChecked( 5 * 4, g_Thk.ultraSonic_Velocity);
	res += AT24CXX_Write4BytesChecked( 6 * 4, g_Dev.brightness_val);
	res += AT24CXX_Write4BytesChecked( 7 * 4, g_Dev.lang_type);
	res += AT24CXX_Write4BytesChecked( 8 * 4, g_Dev.unit_type);
	res += AT24CXX_Write4BytesChecked( 9 * 4, g_Thk.caliThicknessVal*100);
	res += AT24CXX_Write4BytesChecked(10 * 4, g_Thk.usrSaveVelocity_1);
	res += AT24CXX_Write4BytesChecked(11 * 4, g_Thk.usrSaveVelocity_2);
	res += AT24CXX_Write4BytesChecked(12 * 4, g_Thk.usrSaveVelocity_3);
	res += AT24CXX_Write4BytesChecked(13 * 4, g_Thk.pre_velc_id);
	res += AT24CXX_Write4BytesChecked(14 * 4, (g_Dev.wifiState==0) ? 0 : 1);
	res += AT24CXX_Write4BytesChecked(15 * 4, (g_Emat.US_avgTimes < 8) ? 8 : g_Emat.US_avgTimes);
	res += AT24CXX_Write4BytesChecked(16 * 4, g_Thk.referenceBitOn);
//	res += AT24CXX_Write4BytesChecked(17 * 4, g_Dev.dismantlingTimesLocal);
	res += AT24CXX_Write4BytesChecked(18 * 4, Zpt_GetZptTimeNs());
//	res += AT24CXX_Write4BytesChecked(19 * 4, );
	res += AT24CXX_Write4BytesChecked(20 * 4,g_Emat.Emit_AB_phase );
//	res += AT24CXX_Write4BytesChecked(21 * 4, *(uint32_t *)(g_Dev.deviceSN + 4*0));
//	res += AT24CXX_Write4BytesChecked(22 * 4, *(uint32_t *)(g_Dev.deviceSN + 4*1));
//	res += AT24CXX_Write4BytesChecked(23 * 4, *(uint32_t *)(g_Dev.deviceSN + 4*2));
	res += DataPers_WriteSnCode(g_Dev.deviceSN);
	
	return res;
}

uint32_t DataPersistence_SaveAllDatasForced(void) {
	return DataPersistence_SaveAllDatas(1);
}


/******************************* end of file **********************************/
