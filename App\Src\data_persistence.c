/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com  // 版权信息：苏州博昇科技有限公司
  * @file    : data_persistence.c                                       // 文件名：数据持久化模块实现文件
  * @project :                                                          // 项目名称（未填写）
  * @brief   :                                                          // 文件简介（未填写）
  * <AUTHOR> PR Team                                                  // 作者：PR团队
  * @since   : 2024/07/30                                               // 创建日期：2024年7月30日
  * @version : V1.0                                                     // 版本号：V1.0
  * @history :                                                          // 版本历史记录
  *	2024/07/30: V0.1                                                   // 2024年7月30日版本V0.1
  *	  1) 首次创建;                                                     // 首次创建文件
  *
  ********************************************************************************
  * @note                                                               // 注意事项字段（未填写）
  *
  ********************************************************************************
  * @attention                                                          // 特别注意字段（未填写）
  *
  ********************************************************************************
  */
#include "data_persistence.h"  // 包含数据持久化头文件，定义了持久化相关的函数声明和数据结构
#include <string.h>            // 包含标准字符串操作函数库，提供memcpy、strlen等函数
#include "24cxx.h"             // 包含24CXX系列EEPROM驱动头文件，提供底层存储器读写接口
#include "app.h"               // 包含应用程序主头文件，定义全局设备状态和配置结构体
#include "app_thickness.h"     // 包含厚度测量应用头文件，定义测厚相关的参数结构体
#include "emat.h"              // 包含电磁超声换能器头文件，定义EMAT相关的配置参数
#include "bsp_zpt.h"           // 包含零点时间板级支持包头文件，提供零点时间管理功能

//应用部分                     // 注释：以下是应用层数据持久化的实现部分
/***                                                                    // 数据存储映射表详细说明
// 32bits 每个参数, 地址加4                                             // 每个参数占用32位（4字节），地址按4字节递增
// 0~4:N/A; 5:上次声速; 6:亮度; 7:语言; 8:单位制; 9:校准试块的厚度(1000=10.00mm);  // 地址0-4未使用；地址5存储声速；地址6存储亮度；地址7存储语言设置；地址8存储单位制；地址9存储校准试块厚度（以0.01mm为单位）
// 10:用户存储声速1; 11:用户存储声速2; 12:用户存储声速3;                  // 地址10-12存储用户自定义的三个声速值
//用户声速总共4字节, 高2字节存放温度, 低2字节存放声速                      // 用户声速数据格式说明：高16位存储温度，低16位存储声速值
	____________________________________________________________________________________________________________  // 数据存储映射表（地址0-14）
	|0 |1      |2-3|4       |5    |6         |7       |8     |9              |10       |11        |12        |13       |14       |  // 表头：地址索引
	------------------------------------------------------------------------------------------------------------  // 分隔线
	|  |HDCode |N/A|DevFunc |velc |brightness|Language|Unit  |cali thickness |usr velc1|usr velc2 |usr velc3 |材料序号 |wifi开关 |  // 表内容：对应存储的参数类型
	------------------------------------------------------------------------------------------------------------  // 分隔线
	______________________________________________________________________________________________________________  // 数据存储映射表（地址15-27）
	|15      |16     |17           |18          |19           |20          |21~23 | 24、25、26、27      |  // 表头：地址索引
	------------------------------------------------------------------------------------------------------------  // 分隔线
	|Average |参考位 |拆机次数记录 |零点偏移,ns |相关信号零点 |EmitAB Phase|SN    | (未用)|  // 表内容：Average=平均次数，参考位=小数位显示控制，拆机次数记录=设备拆机统计，零点偏移=时间补偿，SN=设备序列号
	--------------------------------------------------------------------------------------------------------------  // 分隔线
    __________________                                                         // 验证码存储区映射表
	|30         |31     |                                                     // 表头：地址索引
	------------------                                                         // 分隔线
	|芯片验证码 |保留   |                                                     // 表内容：芯片验证码用于设备认证，地址31保留未使用
    --------------------                                                       // 分隔线
	__________________                                                         // 扩展存储区映射表
	|47       |48     |                                                       // 表头：地址索引
	------------------                                                         // 分隔线
	|         |保留   |                                                       // 表内容：地址47-48保留未使用
    --------------------                                                       // 分隔线

	//400~449 系统数据区（Bootloader使用,App禁止修改，同时Bootloader禁止使用以外的数据区间）  // 系统级数据存储区说明：由Bootloader管理，应用程序不得修改
	___________________________________________________________________________________________________  // 系统数据区映射表
	| 400-409 | 410      | 411-419 | 420           | 425-428    | 430         |  431~449   |    // 表头：地址范围
	---------------------------------------------------------------------------------------------------  // 分隔线
	| 保留    | 硬件代号 | 保留    | 软件代号(int) | 软件版本名 | App有效标志 |   保留     |    // 表内容：硬件代号=硬件版本标识，软件代号=软件版本标识，App有效标志=应用程序完整性标志
	---------------------------------------------------------------------------------------------------  // 分隔线
	hardwareCodeWrite = 0x03000301; //0x0300-03-01(系列号-硬件大版本-硬件小版本)(HD 2020版本)  // 硬件代号示例：0x0300表示系列号，03表示硬件大版本，01表示硬件小版本（2020年版本）
	hardwareCodeWrite = 0x03000302; //0x0300-03-02(系列号-硬件大版本-硬件小版本)(HD 2023版本)  // 硬件代号示例：0x0300表示系列号，03表示硬件大版本，02表示硬件小版本（2023年版本）

***/
#define USE_FLASH_SIZE	24                                                 // 宏定义：应用程序使用的EEPROM存储区大小，24个32位字（96字节）

#define OFFSET_ADDR_VERIFY_ENCODE	30                                     // 宏定义：验证码存储的偏移地址，位于地址30（120字节偏移）

static uint32_t flashBuf[USE_FLASH_SIZE];                              // 静态数组：EEPROM数据缓冲区，用于批量读取和缓存存储的参数


uint8_t DataPersistence_Init(void) {                                   // 数据持久化模块初始化函数

	return 0;                                                           // 返回0表示初始化成功（当前为空实现，可能在未来版本中添加初始化代码）
}

uint8_t DataPersistence_SaveSelfLockVal(uint32_t val) {                 // 保存自锁值函数，用于存储设备拆机次数等安全相关数据
	if(AT24CXX_Write4BytesChecked(17 * 4, val) != 0) return 1; //failed // 向EEPROM地址68（17*4）写入4字节数据，如果写入失败返回1

	return 0; //ok                                                      // 写入成功返回0
}

uint8_t DataPersistence_ReadSelfLockVal(uint32_t *retVal) {            // 读取自锁值函数，用于读取设备拆机次数等安全相关数据
	if(AT24CXX_Read4BytesChecked(17 * 4, retVal) != 0) return 1; //failed // 从EEPROM地址68（17*4）读取4字节数据，如果读取失败返回1

	return 0; //ok                                                      // 读取成功返回0，数据通过retVal指针返回
}


uint8_t DataPers_WriteWifiEnable(uint32_t enable) {                    // 写入WiFi使能状态函数
	return AT24CXX_Write4BytesChecked(14*4, enable);                    // 向EEPROM地址56（14*4）写入WiFi使能状态，返回写入结果（0=成功，非0=失败）
}

uint8_t DataPers_ReadWifiEnable(uint32_t *retVal) {                    // 读取WiFi使能状态函数
	return AT24CXX_Read4BytesChecked(14*4, retVal);                     // 从EEPROM地址56（14*4）读取WiFi使能状态，返回读取结果（0=成功，非0=失败）
}

uint8_t DataPers_WriteSnCode(char *strSnCode) {                         // 写入设备序列号函数，参数为指向序列号字符串的指针
	uint8_t res = 0;                                                    // 定义结果变量，累计写入操作的错误次数

	res += AT24CXX_Write4BytesChecked(21*4, *(uint32_t *)strSnCode);    // 向EEPROM地址84（21*4）写入序列号的前4个字节，将字符指针转换为32位整数指针并解引用
	res += AT24CXX_Write4BytesChecked(22*4, *((uint32_t *)strSnCode + 1)); // 向EEPROM地址88（22*4）写入序列号的第5-8字节，指针偏移1个32位单位（4字节）
	res += AT24CXX_Write4BytesChecked(23*4, *((uint32_t *)strSnCode + 2)); // 向EEPROM地址92（23*4）写入序列号的第9-12字节，指针偏移2个32位单位（8字节）

	return res;                                                         // 返回累计错误次数，0表示全部写入成功，非0表示有写入失败
}

uint8_t DataPers_ReadSnCode(char *retSnCode, uint32_t size) {           // 读取设备序列号函数，参数为返回缓冲区指针和缓冲区大小
	uint8_t res = 0;                                                    // 定义结果变量，累计读取操作的错误次数
	char str[16] = {0};                                                 // 定义16字节的临时缓冲区，并初始化为0

	res += AT24CXX_Read4BytesChecked(21*4, (uint32_t *)str);            // 从EEPROM地址84（21*4）读取序列号的前4个字节到临时缓冲区
	res += AT24CXX_Read4BytesChecked(22*4, (uint32_t *)str + 1);        // 从EEPROM地址88（22*4）读取序列号的第5-8字节，存储到缓冲区偏移4字节位置
	res += AT24CXX_Read4BytesChecked(23*4, (uint32_t *)str + 2);        // 从EEPROM地址92（23*4）读取序列号的第9-12字节，存储到缓冲区偏移8字节位置

	if(strlen(str) > size) return 1; //failed                           // 检查读取的字符串长度是否超过目标缓冲区大小，超过则返回1（失败）

	memcpy(retSnCode, str, strlen(str));                                // 将临时缓冲区中的有效字符串复制到返回缓冲区中

	return res;                                                         // 返回累计错误次数，0表示读取成功，非0表示有读取失败
}

uint8_t DataPers_ReadUidEncode(uint32_t *retEncode) {                   // 读取设备UID验证码函数，用于设备身份验证
	uint32_t readVal;                                                   // 定义临时变量存储从EEPROM读取的数据

	if(AT24CXX_Read4BytesChecked(OFFSET_ADDR_VERIFY_ENCODE*4, &readVal) == 0) { // 从EEPROM地址120（30*4）读取验证码，读取成功返回0
		*retEncode = readVal;                                           // 将读取的验证码通过指针返回给调用者
		return 0; //ok                                                  // 返回0表示读取成功
	}

	return 1;                                                           // 返回1表示读取失败
}

uint8_t DataPers_WriteUidEncode(uint32_t encode) {                     // 写入设备UID验证码函数，用于设备身份验证
	return AT24CXX_Write4BytesChecked(OFFSET_ADDR_VERIFY_ENCODE*4, encode); // 向EEPROM地址120（30*4）写入验证码，返回写入结果（0=成功，非0=失败）
}

uint32_t DataPersistence_ReadAllDatas(void) {                          // 从EEPROM读取所有持久化数据的函数
	uint32_t res = 0;                                                   // 定义结果变量，累计读取操作的错误次数
	uint32_t i = 0;                                                     // 循环计数器
	uint32_t tmpU32 = 0;                                                // 临时变量，存储从EEPROM读取的单个32位数据


	memset(flashBuf, 0, sizeof(flashBuf));                              // 将缓冲区清零，确保未读取的数据为0
	for(i = 0; i < USE_FLASH_SIZE; i++) {                               // 循环读取24个32位数据（96字节）
		if(AT24CXX_Read4BytesChecked(i * 4, &tmpU32) == 0) {            // 从EEPROM地址i*4读取4字节数据，读取成功返回0
			flashBuf[i] = tmpU32;                                       // 将读取的数据存入缓冲区对应位置
		}
		else {                                                          // 如果读取失败
			flashBuf[i] = 0;                                            // 将缓冲区对应位置设为0（默认值）
			res++;                                                      // 错误计数器加1
		}
	}

	Dev_SetFunction(flashBuf[4]);                                       // 设置设备功能模式，从缓冲区索引4读取功能模式参数
	g_Thk.ultraSonic_Velocity 				= flashBuf[5];              // 从缓冲区索引5读取超声波传播速度（m/s）
	g_Dev.brightness_val 					= flashBuf[6] > 100 || flashBuf[6] <= 0 ? 100 : flashBuf[6]; // 从缓冲区索引6读取OLED亮度值，范围检查：超出1-100范围则设为100%
	g_Dev.lang_type 						= flashBuf[7];              // 从缓冲区索引7读取语言类型（0=英文，1=中文）
	g_Dev.unit_type 						= flashBuf[8];              // 从缓冲区索引8读取单位类型（0=国际制mm，1=英制inch）
	g_Thk.caliThicknessVal 					= (float)flashBuf[9]/100;   // 从缓冲区索引9读取校准试块厚度，除以100转换为毫米（存储时以0.01mm为单位）
	g_Thk.usrSaveVelocity_1 				= flashBuf[10];             // 从缓冲区索引10读取用户自定义声速1（m/s）
	g_Thk.usrSaveVelocity_2 				= flashBuf[11];             // 从缓冲区索引11读取用户自定义声速2（m/s）
	g_Thk.usrSaveVelocity_3 				= flashBuf[12];             // 从缓冲区索引12读取用户自定义声速3（m/s）
	g_Thk.pre_velc_id 						= flashBuf[13];             // 从缓冲区索引13读取预设材料声速ID（0-11，对应不同材料）
	g_Dev.wifiState 						= flashBuf[14];             // 从缓冲区索引14读取WiFi开关状态（0=关闭，1=开启）
	g_Emat.US_avgTimes 						= flashBuf[15];             // 从缓冲区索引15读取超声信号平均次数（8-1024，用于降噪）
	g_Thk.referenceBitOn 					= flashBuf[16];             // 从缓冲区索引16读取参考位显示开关（0=关闭小数点后第三位，1=显示）
	g_Dev.dismantlingTimesLocal 			= flashBuf[17];             // 从缓冲区索引17读取设备拆机次数记录（安全审计用）
	Zpt_SetZptTimeNs(flashBuf[18]);                                     // 从缓冲区索引18读取零点时间偏移并设置到零点时间管理模块（纳秒）
	g_Emat.Emit_AB_phase 					= flashBuf[20];//发射AB相位 // 从缓冲区索引20读取EMAT发射AB相位设置（控制发射波形相位）
	g_Emat.Emit_brakePulNum	= 0;                                            // 设置EMAT发射制动脉冲数为0（初始化发射参数）
	*(uint32_t *)(g_Dev.deviceSN + 4*0) 	= flashBuf[21];                 // 从缓冲区索引21读取设备序列号的前4个字节（字节0-3）
	*(uint32_t *)(g_Dev.deviceSN + 4*1) 	= flashBuf[22];                 // 从缓冲区索引22读取设备序列号的第5-8个字节（字节4-7）
	*(uint32_t *)(g_Dev.deviceSN + 4*2) 	= flashBuf[23];                 // 从缓冲区索引23读取设备序列号的第9-12个字节（字节8-11）

	g_Dev.deviceSN[0] = 'P'; g_Dev.deviceSN[1] = 'R'; g_Dev.deviceSN[2] = 'E'; g_Dev.deviceSN[3] = 'M'; // 强制设置设备序列号前缀为"PREM"（产品标识）
	g_Dev.deviceSN[4] = 'A'; g_Dev.deviceSN[5] = 'T'; g_Dev.deviceSN[6] = '3'; g_Dev.deviceSN[7] = '#'; // 强制设置设备序列号中段为"AT3#"（产品型号标识）

	return res;                                                         // 返回累计错误次数，0表示所有数据读取成功
}


// 用于判断是否需要存储数据到FLASH                                        // 注释：数据保存到EEPROM的主函数
uint32_t DataPersistence_SaveAllDatas(uint8_t isForced)                // 保存所有持久化数据到EEPROM的函数，参数isForced表示是否强制保存
{
	uint32_t res = 0;                                                   // 定义结果变量，累计写入操作的错误次数

	res += AT24CXX_Write4BytesChecked( 4 * 4, g_Dev.functionMode);      // 向EEPROM地址16写入设备功能模式（普通测厚/长距离测厚等）
	res += AT24CXX_Write4BytesChecked( 5 * 4, g_Thk.ultraSonic_Velocity); // 向EEPROM地址20写入超声波传播速度（m/s）
	res += AT24CXX_Write4BytesChecked( 6 * 4, g_Dev.brightness_val);    // 向EEPROM地址24写入OLED屏幕亮度值（10-100%）
	res += AT24CXX_Write4BytesChecked( 7 * 4, g_Dev.lang_type);         // 向EEPROM地址28写入语言类型（0=英文，1=中文）
	res += AT24CXX_Write4BytesChecked( 8 * 4, g_Dev.unit_type);         // 向EEPROM地址32写入单位类型（0=国际制mm，1=英制inch）
	res += AT24CXX_Write4BytesChecked( 9 * 4, g_Thk.caliThicknessVal*100); // 向EEPROM地址36写入校准试块厚度，乘以100转换为0.01mm单位存储
	res += AT24CXX_Write4BytesChecked(10 * 4, g_Thk.usrSaveVelocity_1); // 向EEPROM地址40写入用户自定义声速1（m/s）
	res += AT24CXX_Write4BytesChecked(11 * 4, g_Thk.usrSaveVelocity_2); // 向EEPROM地址44写入用户自定义声速2（m/s）
	res += AT24CXX_Write4BytesChecked(12 * 4, g_Thk.usrSaveVelocity_3); // 向EEPROM地址48写入用户自定义声速3（m/s）
	res += AT24CXX_Write4BytesChecked(13 * 4, g_Thk.pre_velc_id);       // 向EEPROM地址52写入预设材料声速ID（0-11）
	res += AT24CXX_Write4BytesChecked(14 * 4, (g_Dev.wifiState==0) ? 0 : 1); // 向EEPROM地址56写入WiFi开关状态，确保只存储0或1
	res += AT24CXX_Write4BytesChecked(15 * 4, (g_Emat.US_avgTimes < 8) ? 8 : g_Emat.US_avgTimes); // 向EEPROM地址60写入平均次数，确保最小值为8
	res += AT24CXX_Write4BytesChecked(16 * 4, g_Thk.referenceBitOn);    // 向EEPROM地址64写入参考位显示开关（0=关闭，1=开启）
//	res += AT24CXX_Write4BytesChecked(17 * 4, g_Dev.dismantlingTimesLocal); // 向EEPROM地址68写入拆机次数（已注释，可能出于安全考虑）
	res += AT24CXX_Write4BytesChecked(18 * 4, Zpt_GetZptTimeNs());      // 向EEPROM地址72写入零点时间偏移（纳秒）
//	res += AT24CXX_Write4BytesChecked(19 * 4, );                        // EEPROM地址76保留未使用（已注释）
	res += AT24CXX_Write4BytesChecked(20 * 4,g_Emat.Emit_AB_phase );    // 向EEPROM地址80写入EMAT发射AB相位设置
//	res += AT24CXX_Write4BytesChecked(21 * 4, *(uint32_t *)(g_Dev.deviceSN + 4*0)); // 设备序列号写入（已注释，改用专用函数）
//	res += AT24CXX_Write4BytesChecked(22 * 4, *(uint32_t *)(g_Dev.deviceSN + 4*1)); // 设备序列号写入（已注释，改用专用函数）
//	res += AT24CXX_Write4BytesChecked(23 * 4, *(uint32_t *)(g_Dev.deviceSN + 4*2)); // 设备序列号写入（已注释，改用专用函数）
	res += DataPers_WriteSnCode(g_Dev.deviceSN);                        // 调用专用函数写入设备序列号到EEPROM地址84-95

	return res;                                                         // 返回累计错误次数，0表示所有数据保存成功
}

uint32_t DataPersistence_SaveAllDatasForced(void) {                    // 强制保存所有数据的便捷函数
	return DataPersistence_SaveAllDatas(1);                             // 调用主保存函数，参数1表示强制保存（忽略是否有变化）
}


/******************************* end of file **********************************/  // 文件结束标记
