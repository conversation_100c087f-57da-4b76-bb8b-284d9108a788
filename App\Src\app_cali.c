#include "app_cali.h"
#include "app.h"
#include "thk.h"
#include "emat.h"
#include "timer.h"
#include "delay.h"
#include "wifi.h"
#include "oled.h"
#include "app_thickness.h"
#include "data_persistence.h"
#include "bsp_zpt.h"

//static uint8_t isCalibrating_zeroPt = 0;//1:校准被取消
static uint8_t caliCancel;//1:校准被取消
int32_t zeroPt_calibrated;

void Cali_CancelCali() {
	caliCancel = 1;
}

float peaktime_sum = 0;
static uint8_t Calibrate_VelocityZero(uint32_t thickness, uint8_t item)
{
	uint8_t res;
	uint8_t tryCnt = 0;
	uint8_t acqCnt = 0;
//	float peaktime_sum = 0;
	uint32_t vel=0; 
	uint16_t avgBak;
	uint8_t caliWord_xPos = 85;
	peaktime_sum = 0;
//	isCalibrating_zeroPt = (item == 2) ? 1 : 0;
	avgBak = Emat_GetAvg();
	Emat_SetAvg(256);
	caliCancel = 0;
	g_Emat.Emit_Switch = 1;
	Feed_KeepConnect_WDG();
	while(1) {
		if(Thk_MeasureThickness(3240, 0)) { //此处声速不为0即可
			WifiCmdPolling(1);//高优先级指令可以直接执行
			if(caliCancel == 1) {//校准被取消
				OLED_ShowString(caliWord_xPos, 0 , "  ");
				res = 2;	//校准取消
				break;
			}
			if(g_Dev.controlByApp == 0) {
				OLED_ShowMediumChar(acqCnt*10 + 20, 32, '.');
			}
			else {
				Feed_KeepConnect_WDG();
				if(tryCnt%2 == 0) {
					OLED_ShowString(caliWord_xPos, 0 , "C.");
				}
				else {
					OLED_ShowString(caliWord_xPos, 0 , "C ");
				}
			}
			if(THK_GetCaliEchoTime() != 0) {
				acqCnt++;
				SendCaliProgCmd2App(acqCnt*10);
				peaktime_sum = peaktime_sum + THK_GetCaliEchoTime();
			}
			if(acqCnt == 10) { //校准10次,计算结果
				peaktime_sum = peaktime_sum / 10.0;
				vel = (double)(thickness * g_Daq.fs_MHz * 2) / peaktime_sum;
				OLED_ShowString(caliWord_xPos, 0 , "  ");
				
				if((vel <= VELOCITY_MAX) && (vel >= VELOCITY_MIN)) {
					if(item == 1) { //校准声速
						g_Thk.ultraSonic_Velocity = vel;
						res = 0; //成功
					}
					else if(item == 2) { //校准零点
						float flagPeak_tof;
						int32_t flagPeak_periodIdx;
						THK_GetCaliResParas(&flagPeak_tof, &flagPeak_periodIdx);
						zeroPt_calibrated = ((int32_t)(Zpt_GetZptTimeNs()/(1000/(int32_t)g_Daq.fs_MHz)) + flagPeak_tof - peaktime_sum*flagPeak_periodIdx)*1000/g_Daq.fs_MHz; //此处需要注意零点尾数的处理
						res = Zpt_SetZptTimeNs(zeroPt_calibrated);
						
						if(res != 0) break; //failed
						
						break; //0:成功; 1:失败;
					}
					delay_ms(400);
					break; //校准成功
				}
				else {
					res = 1; //校准失败
					break;
				}
			}
		}
		tryCnt++;
		if(tryCnt >= 20) {
			OLED_ShowString(caliWord_xPos, 0 , "  ");
			res = 1; //校准失败
			break;
		}						
	}
	g_Emat.Emit_Switch = 0;
	Emat_SetAvg(avgBak);

	DataPersistence_SaveAllDatasForced();
	return res;
}

uint8_t Calibrate_Velocity(uint32_t thickness) {
	return Calibrate_VelocityZero(thickness, 1);
}
uint8_t Calibrate_ZeroPoint(uint32_t thickness) {
	return Calibrate_VelocityZero(thickness, 2);
}

	