#include "app_cali.h"           // 包含校准应用头文件
#include "app.h"               // 包含应用程序主头文件
#include "thk.h"               // 包含测厚功能头文件
#include "emat.h"              // 包含电磁超声换能器头文件
#include "timer.h"             // 包含定时器功能头文件
#include "delay.h"             // 包含延时功能头文件
#include "wifi.h"              // 包含WiFi功能头文件
#include "oled.h"              // 包含OLED显示屏头文件
#include "app_thickness.h"     // 包含厚度测量应用头文件
#include "data_persistence.h"  // 包含数据持久化头文件
#include "bsp_zpt.h"           // 包含零点时间板级支持包头文件

//static uint8_t isCalibrating_zeroPt = 0;//1:校准被取消  // 零点校准状态标志（已注释，原用于标识校准是否被取消）
static uint8_t caliCancel;//1:校准被取消                 // 静态变量，校准取消标志，1表示校准被取消
int32_t zeroPt_calibrated;                              // 全局变量，存储校准后的零点时间值

void Cali_CancelCali() {                                // 取消校准的函数
	caliCancel = 1;                                     // 设置校准取消标志为1
}

float peaktime_sum = 0;                                 // 全局变量，存储峰值时间的累加和，用于计算平均值
static uint8_t Calibrate_VelocityZero(uint32_t thickness, uint8_t item) // 声速和零点校准的核心函数，thickness为已知厚度值，item为校准类型（1=声速，2=零点）
{
	uint8_t res;                                        // 定义返回结果变量
	uint8_t tryCnt = 0;                                 // 定义尝试次数计数器，初始化为0
	uint8_t acqCnt = 0;                                 // 定义采集次数计数器，初始化为0
//	float peaktime_sum = 0;                             // 峰值时间累加和（已注释，使用全局变量）
	uint32_t vel=0;                                     // 定义计算得到的声速值，初始化为0
	uint16_t avgBak;                                    // 定义平均次数备份变量
	uint8_t caliWord_xPos = 85;                         // 定义校准提示字符在OLED上的X坐标位置
	peaktime_sum = 0;                                   // 初始化峰值时间累加和为0
//	isCalibrating_zeroPt = (item == 2) ? 1 : 0;        // 设置零点校准状态标志（已注释）
	avgBak = Emat_GetAvg();                             // 备份当前的平均次数设置
	Emat_SetAvg(256);                                   // 设置平均次数为256，提高测量精度
	caliCancel = 0;                                     // 初始化校准取消标志为0（未取消）
	g_Emat.Emit_Switch = 1;                             // 开启EMAT发射开关
	Feed_KeepConnect_WDG();                             // 喂看门狗，保持连接
	while(1) {                                          // 无限循环，直到校准完成或失败
		if(Thk_MeasureThickness(3240, 0)) { //此处声速不为0即可  // 执行厚度测量，使用3240 m/s作为临时声速值
			WifiCmdPolling(1);//高优先级指令可以直接执行      // 处理WiFi高优先级命令
			if(caliCancel == 1) {//校准被取消               // 检查是否收到校准取消指令
				OLED_ShowString(caliWord_xPos, 0 , "  ");   // 清除OLED上的校准提示字符
				res = 2;	//校准取消                       // 设置返回结果为2（校准取消）
				break;                                      // 跳出循环
			}
			if(g_Dev.controlByApp == 0) {                   // 如果设备由自身控制（非APP控制）
				OLED_ShowMediumChar(acqCnt*10 + 20, 32, '.'); // 在OLED上显示进度点，位置根据采集次数计算
			}
			else {                                          // 如果设备由APP控制
				Feed_KeepConnect_WDG();                     // 喂看门狗
				if(tryCnt%2 == 0) {                         // 如果尝试次数为偶数
					OLED_ShowString(caliWord_xPos, 0 , "C."); // 显示"C."表示校准中
				}
				else {                                      // 如果尝试次数为奇数
					OLED_ShowString(caliWord_xPos, 0 , "C "); // 显示"C "（闪烁效果）
				}
			}
			if(THK_GetCaliEchoTime() != 0) {                // 如果获取到有效的回波时间
				acqCnt++;                                   // 采集次数加1
				SendCaliProgCmd2App(acqCnt*10);             // 向APP发送校准进度命令（百分比）
				peaktime_sum = peaktime_sum + THK_GetCaliEchoTime(); // 累加峰值时间
			}
			if(acqCnt == 10) { //校准10次,计算结果            // 如果已采集10次数据，开始计算校准结果
				peaktime_sum = peaktime_sum / 10.0;            // 计算10次测量的平均峰值时间
				vel = (double)(thickness * g_Daq.fs_MHz * 2) / peaktime_sum; // 根据公式计算声速：v = 2*厚度*采样频率/飞行时间
				OLED_ShowString(caliWord_xPos, 0 , "  ");      // 清除OLED上的校准提示字符

				if((vel <= VELOCITY_MAX) && (vel >= VELOCITY_MIN)) { // 检查计算得到的声速是否在有效范围内
					if(item == 1) { //校准声速                  // 如果是声速校准模式
						g_Thk.ultraSonic_Velocity = vel;       // 将计算得到的声速保存到全局变量
						res = 0; //成功                        // 设置返回结果为0（成功）
					}
					else if(item == 2) { //校准零点             // 如果是零点校准模式
						float flagPeak_tof;                    // 定义标志峰值飞行时间变量
						int32_t flagPeak_periodIdx;           // 定义标志峰值周期索引变量
						THK_GetCaliResParas(&flagPeak_tof, &flagPeak_periodIdx); // 获取校准结果参数
						zeroPt_calibrated = ((int32_t)(Zpt_GetZptTimeNs()/(1000/(int32_t)g_Daq.fs_MHz)) + flagPeak_tof - peaktime_sum*flagPeak_periodIdx)*1000/g_Daq.fs_MHz; //此处需要注意零点尾数的处理  // 计算校准后的零点时间值，公式较复杂，涉及采样频率转换和周期补偿
						res = Zpt_SetZptTimeNs(zeroPt_calibrated); // 设置新的零点时间值

						if(res != 0) break; //failed          // 如果设置失败则跳出循环

						break; //0:成功; 1:失败;               // 跳出循环（零点校准完成）
					}
					delay_ms(400);                             // 延时400毫秒
					break; //校准成功                          // 跳出循环，校准成功
				}
				else {                                         // 如果计算得到的声速超出有效范围
					res = 1; //校准失败                        // 设置返回结果为1（校准失败）
					break;                                     // 跳出循环
				}
			}
		}
		tryCnt++;                                              // 尝试次数加1
		if(tryCnt >= 20) {                                     // 如果尝试次数达到20次
			OLED_ShowString(caliWord_xPos, 0 , "  ");          // 清除OLED上的校准提示字符
			res = 1; //校准失败                                // 设置返回结果为1（校准失败）
			break;                                             // 跳出循环
		}
	}
	g_Emat.Emit_Switch = 0;                                // 关闭EMAT发射开关
	Emat_SetAvg(avgBak);                                   // 恢复之前备份的平均次数设置

	DataPersistence_SaveAllDatasForced();                  // 强制保存所有数据到持久化存储
	return res;                                            // 返回校准结果（0=成功，1=失败，2=取消）
}

uint8_t Calibrate_Velocity(uint32_t thickness) {          // 声速校准函数，参数为已知厚度值
	return Calibrate_VelocityZero(thickness, 1);          // 调用核心校准函数，参数1表示声速校准
}
uint8_t Calibrate_ZeroPoint(uint32_t thickness) {         // 零点校准函数，参数为已知厚度值
	return Calibrate_VelocityZero(thickness, 2);          // 调用核心校准函数，参数2表示零点校准
}

