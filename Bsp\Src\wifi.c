
//(内部代号第三版/第四版)                                                // 注释：WiFi模块硬件版本标识，第三版/第四版指硬件迭代版本
//wifi ID记录:                                                          // 注释：WiFi设备ID分配记录，用于追踪设备生产和分发
//2018.10.xx : 鄂尔多斯(8) #0101~#0108                                  // 2018年10月：鄂尔多斯客户8台设备，ID范围#0101-#0108
//2018.11.xx : 济宁鲁科(1) #0100                                        // 2018年11月：济宁鲁科客户1台设备，ID为#0100
//2018.12.17 : 济宁鲁科(1) #0110                                        // 2018年12月17日：济宁鲁科客户1台设备，ID为#0110
//2018.12.20 : #0111(2),#0112,#0113,#0114(2)                           // 2018年12月20日：分配ID #0111-#0114，括号内数字可能表示硬件版本
//2018.12.24 : #0115(2), #0116                                         // 2018年12月24日：分配ID #0115-#0116
//2018.12.25 : #0109(2),#0117,#0118(2),#0119(2)                        // 2018年12月25日：分配ID #0109,#0117-#0119
//2018.12.28 : #0120(2),#0121(2),#0122(2),#0123(2),#0124(2),#0125(2)   // 2018年12月28日：分配ID #0120-#0125，均为版本2
//2018.01.08 : #0126(2),#0127(2)                                       // 2019年1月8日：分配ID #0126-#0127（年份可能有误）
//2019.03.07 : #0132(2),#0133(2),#0134(2),#0135(2),#0136(2),#0137(2),#0138(2),#0139(2),#0140(2),#0141(2),#0142(2),#0143(2),#0144(2), // 2019年3月7日：大批量分配ID #0132-#0150
//		#0145(2),#0146(2),#0147(2),#0148(2),#0149(),#0150()              // 继续上行，#0149和#0150版本信息缺失
//2019.05.22 : #0151(2)，#0152(2)                                      // 2019年5月22日：分配ID #0151-#0152
//2019.05.29 : #0153(2), #0154(2), #0155(2), #0156(2), #0157(2), #0158(2), #0159(2), #0160(2), #0161(2), #0162(2), #0163(2), #0164(2), #0165(2), // 2019年5月29日：大批量分配ID #0153-#0180
// 		#0166(2), #0167(2), #0168(2), #0169(2), #0170(2), #0171(2), #0172(2), #0173(2), #0174(2), #0175(2), #0176(2), #0177(2), #0178(2), #0179(2), #0180(2) // 继续上行
//2019.09.18 : #0200(2),#0201(2),#0202(175),#0203(160),#0204(153),#0205(162),#0206(158),,#0002(179),,#0003(172),,#0001(143), // 2019年9月18日：新系列ID #0200开始，括号内可能为零点校准值
//2019.09.18 : #0207(161),#0208(168),#0209(174),#0210(177),#0211(169),#0212(157), // 继续上行，分配更多200系列ID
//50套新板                                                              // 注释：50套新硬件板卡的生产批次标记
//2019.11.11 : #0301,#0302,#0303,#0304,#0305,                          // 2019年11月11日：新系列ID #0301开始，300系列
//2019.11.26 : #0154 -> #0124                                          // 2019年11月26日：设备ID重新分配，#0154改为#0124
//2019.12.05 : #0306,#0307,#0308,#0309,#0310,#0311,#0312,#0313,#0314,#0315,,#0316,#0317,#0318,#0319,#0320,#0321,#0322,#0323,#0324 // 2019年12月5日：继续分配300系列ID
//2020.01.08 : #0205 -> #0110, #0206 -> #0112, #0207 -> #0116          // 2020年1月8日：多个设备ID重新分配
//2020.03.23 : #0211 -> #0139(零点355,-245)                            // 2020年3月23日：设备重新分配，包含零点校准参数
//2020.11.17 : #0325, #0326, #0327                                     // 2020年11月17日：分配ID #0325-#0327

//版本更新记录：                                                        // 注释：软件版本更新历史记录
//20200401 -> 920200402:                                               // 版本20200401升级到920200402的更新内容
//死机处理;                                                             // 修复：解决系统死机问题
//920200402 -> 920200403：                                             // 版本920200402升级到920200403的更新内容
//关闭多次结果比较,改为直接出值;                                        // 优化：取消多次测量结果比较，改为直接输出单次测量结果

#include "wifi.h"              // 包含WiFi模块头文件，定义WiFi通信相关的函数声明和数据结构
#include <string.h>            // 包含标准字符串操作函数库，提供strcpy、strlen等函数
#include "stm32f7xx_hal.h"     // 包含STM32F7系列HAL库头文件，提供硬件抽象层接口
#include "thk.h"               // 包含测厚算法头文件，提供厚度计算相关函数
#include "delay.h"             // 包含延时函数头文件，提供毫秒和微秒级延时功能
#include "oled.h"              // 包含OLED显示屏头文件，控制液晶显示
#include "dac.h"               // 包含数模转换器头文件，控制DAC输出
#include "timer.h"             // 包含定时器头文件，提供定时功能
#include "adc.h"               // 包含模数转换器头文件，控制ADC采样
#include "fpga.h"              // 包含FPGA控制头文件，管理FPGA硬件资源
//#include "main.h"            // 主函数头文件（已注释，可能不再需要）
#include "emat.h"              // 包含电磁超声换能器头文件，控制EMAT发射和接收参数
#include "app.h"               // 包含应用程序主头文件，定义全局设备状态和配置
#include "bsp.h"               // 包含板级支持包头文件，提供底层硬件接口
#include "app_cali.h"          // 包含校准应用头文件，提供声速和零点校准功能
#include "app_thickness.h"     // 包含厚度测量应用头文件，提供厚度测量接口
#include "data_persistence.h"  // 包含数据持久化头文件，管理EEPROM数据存储
#include "bsp_zpt.h"           // 包含零点时间板级支持包头文件，管理零点时间补偿


Wifi_t g_Wifi;                 // 全局WiFi结构体变量，存储WiFi模块的配置参数和状态信息

//EN(output):PB6; RST(output):PD2; BUSY(input):PA12(IO4); NEED_READ(DATA_VALID)(input):PC10(IO2); // 注释：WiFi模块GPIO引脚分配说明
//CS(output):PC11;                                                      // 注释：SPI片选信号引脚分配
#define WIFI_EN(n) 			HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, n ? GPIO_PIN_SET : GPIO_PIN_RESET) // 宏定义：WiFi模块使能控制，PB6引脚，1=使能，0=禁用
#define WIFI_RST(n) 		HAL_GPIO_WritePin(GPIOD, GPIO_PIN_2, n ? GPIO_PIN_SET : GPIO_PIN_RESET) // 宏定义：WiFi模块复位控制，PD2引脚，1=正常，0=复位
#define WIFI_CS(n) 			HAL_GPIO_WritePin(GPIOC, GPIO_PIN_11, n ? GPIO_PIN_SET : GPIO_PIN_RESET) // 宏定义：SPI片选信号控制，PC11引脚，0=选中，1=释放

#define WIFI_BUSY 			HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_12) //PAin(12) // ESP8266-IO4 // 宏定义：读取WiFi模块忙状态，PA12引脚，1=忙，0=空闲
#define WIFI_NEED_READ_SPI 	HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_10) //ESP8266-IO2 // 宏定义：读取WiFi模块数据就绪信号，PC10引脚，1=有数据，0=无数据

static void SPI1_Init(void);			 //初始化SPI1口        // 静态函数声明：SPI1接口初始化函数
static void SPI1_SetSpeed(uint8_t SPI_BaudRatePrescaler); //设置SPI1速度 // 静态函数声明：设置SPI1通信速度
static uint8_t SPI1_ReadWriteByte(uint8_t TxData);//SPI1总线读写一个字节 // 静态函数声明：SPI1单字节读写函数

#define RX_WIFI_CAHCE_DEEPTH 5                                         // 宏定义：WiFi接收缓存深度为5（当前未使用）
#define WIFI_VERSION_2TH 0 // 0:第三版/第四版; 1:第二版                 // 宏定义：WiFi硬件版本标识，0表示第三版/第四版，1表示第二版
//uint8_t wifiReceivedCmd = 0;                                         // WiFi接收命令标志（已注释，未使用）

uint8_t wifiRxData_buf[32];                                            // WiFi接收数据缓冲区，32字节大小
//uint8_t wifiRxData_buf[RX_WIFI_CAHCE_DEEPTH][32];                    // 多级缓存接收缓冲区（已注释，未使用）
uint8_t wifiRx_RdCache_id = 0;                                         // WiFi接收缓存读指针（声明但未使用）
uint8_t wifiRx_WrCache_id = 0;                                         // WiFi接收缓存写指针（声明但未使用）
uint8_t wifiTxData_buf[64];                                            // WiFi发送数据缓冲区，64字节大小
uint16_t dataPackageParameter_buf[30];                                 // 数据包参数缓冲区，30个16位参数
uint16_t wifiSendData_buf[4200];                                       // WiFi发送数据缓冲区，4200个16位数据（用于波形传输）
static uint16_t frameId = 0;                                           // 静态变量：数据帧ID计数器，用于数据包标识
//uint8_t isAutoACG = 1;                                               // 自动AGC标志（已注释，未使用）

static SPI_HandleTypeDef hspi1;                                        // 静态变量：SPI1句柄结构体，用于HAL库SPI操作
////u32 g_Dev.firmWareVersion = 18092814;                              // 固件版本号（已注释的旧版本）
//u32 g_Dev.firmWareVersion = 920210819;//920200402;//20200401;//20030601;//20011401;//19090101;//19081201;//19041201;//19011801;//18122801;//18122801开始,wifi版本更新为第二版 // 固件版本号历史记录（已注释）
////This location reads the software version.                          // 注释：此处读取软件版本信息

// ESP8266 EN/RST 脚复位时，HSPI_CS(IO15)脚需要为低，否则无法复位      // 注释：ESP8266复位时序要求，CS引脚必须为低电平
// HSPI_CS(IO15) 的状态对开启关闭ESP8266很敏感                         // 注释：CS引脚状态对ESP8266启动关闭非常关键
void Wifi_Init(void)                                                   // WiFi模块初始化函数
{
	//EN(output):PB6; RST(output):PD2; BUSY(input):PA12(IO4); NEED_READ(DATA_VALID)(input):PC10(IO2); // 注释：GPIO引脚分配说明
	//CS:PC11;                                                          // 注释：片选引脚分配
	GPIO_InitTypeDef GPIO_InitStruct = {0};                            // 定义GPIO初始化结构体并清零

	__HAL_RCC_GPIOA_CLK_ENABLE();                                      // 使能GPIOA时钟，为PA12(BUSY)引脚供电
	__HAL_RCC_GPIOB_CLK_ENABLE();                                      // 使能GPIOB时钟，为PB6(EN)和SPI引脚供电
	__HAL_RCC_GPIOC_CLK_ENABLE();                                      // 使能GPIOC时钟，为PC10(NEED_READ)和PC11(CS)引脚供电
	__HAL_RCC_GPIOD_CLK_ENABLE();                                      // 使能GPIOD时钟，为PD2(RST)引脚供电

	// EN 脚初始化                                                     // 注释：WiFi模块使能引脚初始化
	GPIO_InitStruct.Pin = GPIO_PIN_6;                                  // 设置引脚为PB6
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;                        // 设置为推挽输出模式
	GPIO_InitStruct.Pull = GPIO_PULLUP;                                // 设置内部上拉电阻
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;                 // 设置引脚速度为最高频率
	HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);                            // 初始化GPIOB的PB6引脚

	// RST 脚初始化                                                    // 注释：WiFi模块复位引脚初始化
	GPIO_InitStruct.Pin = GPIO_PIN_2;                                  // 设置引脚为PD2
	HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);                            // 初始化GPIOD的PD2引脚（复用上面的配置）
	WIFI_RST(1);                                                       // 设置复位引脚为高电平（正常工作状态）

	// CS 脚初始化                                                     // 注释：SPI片选引脚初始化
	GPIO_InitStruct.Pin = GPIO_PIN_11;                                 // 设置引脚为PC11
	HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);                            // 初始化GPIOC的PC11引脚

	// ESP8266 BUSY 引脚初始化                                         // 注释：WiFi模块忙状态引脚初始化
	GPIO_InitStruct.Pin = GPIO_PIN_12;                                 // 设置引脚为PA12
	GPIO_InitStruct.Mode = GPIO_MODE_INPUT;                            // 设置为输入模式
	GPIO_InitStruct.Pull = GPIO_PULLDOWN; //GPIO_NOPULL; //            // 设置内部下拉电阻（注释显示曾考虑无上下拉）
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;                 // 设置引脚速度为最高频率
	HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);                            // 初始化GPIOA的PA12引脚

	// ESP8266 NEED_READ 引脚初始化                                    // 注释：WiFi模块数据就绪引脚初始化
	GPIO_InitStruct.Pin = GPIO_PIN_10;                                 // 设置引脚为PC10
	HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);                            // 初始化GPIOC的PC10引脚（复用上面的输入配置）

	WIFI_EN(0);                                                        // 初始化时禁用WiFi模块（低电平）
	delay_ms(100);                                                     // 延时100毫秒，确保WiFi模块完全关闭
	SPI1_Init();                                                       // 初始化SPI1接口

	// 初始化WiFi结构体                                                // 注释：初始化WiFi配置参数
	g_Wifi.adcDataBitsToApp = 12;                                      // 设置发送给APP的ADC数据位数为12位（默认值）
	g_Wifi.sendThicknessOnly = 0; // 默认发送完整数据包                // 设置数据传输模式为完整数据包模式（包含波形数据）


}

void setESP8266_Ssid_Pwd(void) {                                       // 设置ESP8266 WiFi热点名称和密码的函数
//	strcpy(g_Dev.deviceSN, "PREMAT3#0000");                            // 设置默认设备序列号（已注释，实际序列号从EEPROM读取）
	OpenWifi();                                                        // 打开WiFi模块
	delay_ms(1000);                                                    // 延时1秒，等待WiFi模块完全启动
	setSsidPassword(g_Dev.deviceSN, "PREMAT3Y");                       // 设置WiFi热点：SSID为设备序列号，密码为"PREMAT3Y"
}

//以下是SPI模块的初始化代码，配置成主机模式 						  // 注释：SPI接口初始化代码说明
//SPI1初始化                                                          // 注释：SPI1接口初始化
//SCLK: PB3; MISO: PB4; MOSI: PB5; CS: PC11;                         // 注释：SPI1引脚分配说明
static void SPI1_Init(void) {                                         // 静态函数：SPI1接口初始化
	hspi1.Instance = SPI1;                                            // 设置SPI实例为SPI1
	hspi1.Init.Mode = SPI_MODE_MASTER;                                 // 设置SPI工作模式为主机模式
	hspi1.Init.Direction = SPI_DIRECTION_2LINES;                       // 设置SPI通信方向为全双工（2线双向）
	hspi1.Init.DataSize = SPI_DATASIZE_8BIT;                           // 设置SPI数据帧大小为8位
	hspi1.Init.CLKPolarity = SPI_POLARITY_LOW;                         // 设置SPI时钟极性为低电平空闲
	hspi1.Init.CLKPhase = SPI_PHASE_1EDGE;                             // 设置SPI时钟相位为第一个边沿采样
	hspi1.Init.NSS = SPI_NSS_SOFT;                                     // 设置片选信号为软件控制
	hspi1.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_16;           // 设置SPI波特率预分频为16（降低通信速度确保稳定）
	hspi1.Init.FirstBit = SPI_FIRSTBIT_MSB;                            // 设置数据传输为高位先行（MSB first）
	hspi1.Init.TIMode = SPI_TIMODE_DISABLE;                            // 禁用TI模式（使用标准SPI协议）
	hspi1.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;            // 禁用CRC校验计算
	hspi1.Init.CRCPolynomial = 7;                                      // 设置CRC多项式为7（虽然CRC被禁用）

	if (HAL_SPI_Init(&hspi1) != HAL_OK)                                // 初始化SPI1，检查是否成功
	{
		Bsp_ErrorHandler();                                            // 如果初始化失败，调用错误处理函数
	}

	__HAL_SPI_ENABLE(&hspi1);                                          // 使能SPI1外设
	SPI1_ReadWriteByte(0Xff); //启动传输                               // 发送一个字节数据启动SPI传输（清除可能的初始状态）
	WIFI_CS(1);                                                        // 设置片选信号为高电平（释放片选，空闲状态）
}


void HAL_SPI_MspInit(SPI_HandleTypeDef* spiHandle)                     // HAL库SPI MSP（MCU Support Package）初始化回调函数
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};                            // 定义GPIO初始化结构体并清零
	if(spiHandle->Instance==SPI1)                                      // 如果是SPI1实例
	{
		/* SPI1 clock enable */                                        // 注释：使能SPI1时钟
		__HAL_RCC_SPI1_CLK_ENABLE();                                   // 使能SPI1外设时钟

		__HAL_RCC_GPIOB_CLK_ENABLE();                                  // 使能GPIOB时钟（SPI1引脚位于GPIOB）
		/**SPI1 GPIO Configuration                                     // 注释：SPI1 GPIO配置说明
		PB3     ------> SPI1_SCK                                       // PB3引脚配置为SPI1时钟线
		PB4     ------> SPI1_MISO                                      // PB4引脚配置为SPI1主机输入从机输出线
		PB5     ------> SPI1_MOSI                                      // PB5引脚配置为SPI1主机输出从机输入线
		*/
		GPIO_InitStruct.Pin = GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5;        // 设置要配置的引脚：PB3、PB4、PB5
		GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;                        // 设置引脚模式为复用功能推挽输出
		GPIO_InitStruct.Pull = GPIO_PULLUP;                            // 设置内部上拉电阻（确保信号稳定）
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;             // 设置引脚速度为最高频率（支持高速SPI通信）
		GPIO_InitStruct.Alternate = GPIO_AF5_SPI1;                     // 设置复用功能为SPI1（AF5）
		HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);                        // 初始化GPIOB的SPI1相关引脚
	}
}


//SPI1速度设置函数                                                     // 注释：SPI1通信速度设置函数
//SPI速度=fAPB2/分频系数                                               // 注释：SPI速度计算公式
//@ref SPI_BaudRate_Prescaler:SPI_BaudRatePrescaler_2~SPI_BaudRatePrescaler_256 // 注释：波特率预分频器取值范围
//fAPB2时钟一般为84Mhz：                                               // 注释：APB2总线时钟频率说明
static void SPI1_SetSpeed(uint8_t SPI_BaudRatePrescaler)              // 静态函数：设置SPI1通信速度
{
	assert_param(IS_SPI_BAUDRATE_PRESCALER(SPI_BaudRatePrescaler));//判断有效性 // 参数有效性检查，确保预分频器值在有效范围内
    __HAL_SPI_DISABLE(&hspi1);            //关闭SPI                   // 禁用SPI1外设，准备修改配置
    hspi1.Instance->CR1&=0XFFC7;          //位3-5清零，用来设置波特率 // 清除CR1寄存器的位3-5（波特率控制位）
    hspi1.Instance->CR1|=SPI_BaudRatePrescaler;//设置SPI速度           // 设置新的波特率预分频器值
	__HAL_SPI_ENABLE(&hspi1); //使能SPI1                              // 重新使能SPI1外设，应用新配置
}
//SPI1 读写一个字节                                                    // 注释：SPI1单字节读写函数
//TxData:要写入的字节                                                  // 注释：输入参数说明
//返回值:读取到的字节                                                  // 注释：返回值说明
static uint8_t SPI1_ReadWriteByte(uint8_t txData)                     // 静态函数：SPI1单字节同步读写
{
	uint8_t rxData;                                                    // 定义接收数据变量
    HAL_SPI_TransmitReceive(&hspi1, &txData, &rxData, 1, 1000);       // 调用HAL库函数进行SPI同步收发：发送1字节，接收1字节，超时1000ms
 	return rxData;                                                     // 返回接收到的数据
}

uint8_t Wifi_ReceiveData(void){                                        // WiFi数据接收函数
	uint8_t i = 0;                                                     // 循环计数器
	uint8_t slaveState;                                                // 从机状态变量（声明但未使用）

	WIFI_CS(0);                                                        // 拉低片选信号，选中WiFi模块
	SPI1_ReadWriteByte(0x03); // 读取Slave指令                         // 发送SPI读命令（0x03）
	SPI1_ReadWriteByte(0x00); // Slave 地址                            // 发送读取起始地址（0x00）
	for( i = 0; i < 32; i++){ // 读取数据                              // 循环读取32字节数据
		wifiRxData_buf[i] = SPI1_ReadWriteByte(i);                     // 读取第i个字节到接收缓冲区（注意：这里发送的是索引i，可能有问题）
	}

	WIFI_CS(1);                                                        // 拉高片选信号，释放WiFi模块
}

void WifiSendCmd(uint8_t *buf){                                        // WiFi命令发送函数
	uint8_t i = 0;                                                     // 循环计数器
	WIFI_CS(0);                                                        // 拉低片选信号，选中WiFi模块
	SPI1_ReadWriteByte(0x02); // SPI写指令                             // 发送SPI写命令（0x02）
	SPI1_ReadWriteByte(0x00); // SPI写地址                             // 发送写入起始地址（0x00）
	for( i =0; i < 32;i++){                                            // 循环发送32字节命令数据
		SPI1_ReadWriteByte(buf[i]);                                    // 发送缓冲区中的第i个字节
	}
	delay_us(2);                                                       // 延时2微秒，确保数据传输完成
	WIFI_CS(1);                                                        // 拉高片选信号，释放WiFi模块
}

static void SPI_WriteBytes(uint16_t *buf, uint8_t endNum){            // 静态函数：SPI写入16位数据块
	uint8_t i = 0;                                                     // 循环计数器
	WIFI_CS(0);                                                        // 拉低片选信号，选中WiFi模块
	SPI1_ReadWriteByte(0x02); // SPI写指令                             // 发送SPI写命令（0x02）
	SPI1_ReadWriteByte(0x00); // SPI写地址                             // 发送写入起始地址（0x00）
	SPI1_ReadWriteByte(0x0D|(endNum<<4)); // 帧/尾帧 标识              // 发送帧标识：0x0D为基础标识，endNum左移4位表示帧类型（0=中间帧，1=尾帧）
	SPI1_ReadWriteByte(frameId&0x00ff); // 包ID                        // 发送数据包ID的低8位（用于数据包序列标识）
	for( i =0; i < 15;i++){                                            // 循环发送15个16位数据（30字节）
		SPI1_ReadWriteByte((uint8_t)(buf[i]>>8)&0x00ff);               // 发送第i个16位数据的高8位
		SPI1_ReadWriteByte((uint8_t)(buf[i]&0x00ff));                  // 发送第i个16位数据的低8位
	}
	delay_us(2);                                                       // 延时2微秒，确保数据传输完成
	WIFI_CS(1);                                                        // 拉高片选信号，释放WiFi模块
}

void Wifi_SendData(uint16_t *buf, uint16_t length) {                  // WiFi大数据块发送函数（用于发送波形数据）
	int32_t j = 0;                                                     // 循环变量j
	int32_t bufOffset = 0;                                             // 缓冲区偏移量
	uint16_t i = 0;                                                    // 循环变量i
	uint16_t groupNum; //需要发送多少次30字节数据                      // 计算需要发送的数据包数量（每包15个16位数据）
	uint16_t tmpBuf[64];                                               // 临时缓冲区，用于数据重组
	frameId++;                                                         // 数据帧ID递增，用于标识不同的数据传输

	groupNum = length/15; // 1020点68次; 510点34次;                    // 计算数据包数量：总长度除以15（每包15个16位数据）
	if((length%15) != 0) {                                             // 如果有余数（不能整除）
		groupNum = groupNum + 1;                                       // 数据包数量加1，处理最后一个不完整的包
	}
	for(i = 0; i < groupNum; i++) {                                    // 循环发送所有数据包

		//波形除以4，即将14位转成12位发送给 App                         // 注释：数据位数转换，将14位ADC数据转换为12位发送
		if(i == (groupNum-1)) {                                        // 如果是最后一个数据包
			bufOffset = 15*i;                                         // 计算缓冲区偏移量（最后一包的起始位置）
		}
		else {                                                         // 如果不是最后一个数据包
			bufOffset = 15*(i-1);                                      // 计算缓冲区偏移量（注意：这里的逻辑可能有问题，第一包时i-1=-1）
		}
		for(j = 0; j < 30; j++) {                                      // 处理30个数据点（15个16位数据转换为30个处理单元）
			tmpBuf[j] = ((int16_t*)buf)[bufOffset + j]/4;              // 将原始数据除以4（14位转12位），并转换为有符号16位整数
		}

		if(i == (groupNum-1)) {                                        // 如果是最后一个数据包
			SPI_WriteBytes(tmpBuf, length - (groupNum-1)*15);          // 发送剩余数据，参数为实际剩余数据长度
		}
		else {                                                         // 如果不是最后一个数据包
			if(i == 0) { //第一帧发送参数(30Byte)                      // 如果是第一个数据包
				SPI_WriteBytes(dataPackageParameter_buf, 0x00);        // 发送参数包（包含测量参数信息）
			}
			else {                                                     // 如果是中间数据包
				SPI_WriteBytes(tmpBuf, 0x00);                          // 发送波形数据包
			}
		}

		delay_us(10);                                                  // 延时10微秒，给WiFi模块处理时间
		while(WIFI_BUSY);                                              // 等待WiFi模块处理完成（BUSY信号变为低电平）
		delay_us(2);                                                   // 额外延时2微秒，确保时序稳定
	}
}

void CloseWifi(void) {                                                 // 关闭WiFi模块函数
	delay_ms(1);                                                       // 延时1毫秒，确保操作时序
	WIFI_EN(0);                                                        // 设置WiFi使能引脚为低电平，关闭WiFi模块
	g_Dev.wifiState = 0;                                               // 设置全局WiFi状态为关闭（0）
	g_Dev.controlByApp = 0;                                            // 清除APP控制标志，将控制权交还给设备本身
	Close_KeepConnect_WDG();                                           // 关闭保持连接看门狗定时器
	DataPers_WriteWifiEnable(0);                                       // 将WiFi关闭状态保存到EEPROM
}
// 开启wifi, 需要在数百毫秒内保持 HSPI_CS(IO15) 引脚为低!              // 注释：WiFi开启时序要求，CS引脚必须保持低电平
void OpenWifi(void) {                                                  // 打开WiFi模块函数
	WIFI_CS(0);                                                        // 拉低片选信号，满足ESP8266启动时序要求
	delay_ms(100);                                                     // 延时100毫秒，确保CS信号稳定
	WIFI_EN(1);                                                        // 设置WiFi使能引脚为高电平，启动WiFi模块
	delay_ms(500); // 由于开启了 HSPI 接收中断, 需要延迟后 g_Dev.wifiState = 1 // 延时500毫秒，等待WiFi模块完全启动和HSPI中断稳定
	g_Dev.wifiState = 1;                                               // 设置全局WiFi状态为开启（1）
	WIFI_CS(1);                                                        // 拉高片选信号，释放SPI总线
	DataPers_WriteWifiEnable(1);                                       // 将WiFi开启状态保存到EEPROM
}

uint8_t getWifiState(void) {                                           // 获取WiFi状态函数
	return g_Dev.wifiState;                                            // 返回全局WiFi状态（0=关闭，1=开启）
}


// 查询是否需要处理指令任务                                           // 注释：WiFi命令轮询处理相关变量和函数
static uint8_t tst_wifiRxCmdCnt = 0;                                   // 静态变量：WiFi接收命令计数器（用于测试统计）
static uint8_t wifiTaskPending = 0; //是否有wifi指令待处理            // 静态变量：WiFi任务挂起标志，1=有待处理任务，0=无任务

uint8_t WifiCmdPolling(uint8_t priority) { // 处理等级,0:所有任务; 1:处理及时性相关任务(电机) // WiFi命令轮询处理函数，参数priority指定处理优先级
	uint8_t cmdType;                                                   // 命令类型变量

	while(1) {                                                         // 无限循环处理WiFi命令
		if(wifiTaskPending == 0) { //无待处理任务                      // 如果当前没有待处理的WiFi任务
			if(WIFI_NEED_READ_SPI == 1) { // 判断是否有新数据需要接收	// 检查WiFi模块是否有新数据就绪
				delay_us(20); //去重复,因为STM32该任务完成远快于ESP8266 // 延时20微秒，避免重复读取（STM32处理速度比ESP8266快）
				if(WIFI_NEED_READ_SPI == 1) {                          // 再次确认有数据需要读取
					Wifi_ReceiveData();                                // 接收WiFi数据到缓冲区
					wifiTaskPending = 1;                               // 设置任务挂起标志，表示有任务待处理
					if(wifiRxData_buf[2] != CMD_KEEPCONNECT) {         // 如果不是保持连接命令
						tst_wifiRxCmdCnt++;                            // 接收命令计数器递增
					}
				}
				else {                                                 // 如果数据就绪信号消失
					return 0x02;                                       // 返回0x02，表示无数据可读
				}
			}
			else {                                                     // 如果没有数据就绪信号
				return 0x02;                                           // 返回0x02，表示无任务需要处理
			}
		}
		//有待处理任务                                                 // 注释：处理接收到的WiFi命令
		if((wifiRxData_buf[0] != 0xCD) || (wifiRxData_buf[1] != 0xEF)) { // 检查命令头是否正确（0xCDEF为协议头）
			wifiTaskPending = 0;                                       // 清除任务挂起标志（无效命令）
			continue;                                                  // 继续下一次循环
		}
		cmdType = wifiRxData_buf[2];                                   // 获取命令类型（第3个字节）
		if(priority == 0) { //所有指令都可以执行                      // 如果优先级为0（处理所有命令）
			wifiTaskPending = 0;                                       // 清除任务挂起标志
			WifiCmdExe();                                              // 执行WiFi命令
		}
		else if(priority == 1) { //紧急任务                           // 如果优先级为1（仅处理紧急任务）
			break;                                                     // 跳出循环，进入紧急任务处理
		}
	}

	if(priority == 1) { //紧急任务                                    // 紧急任务处理分支
		Feed_KeepConnect_WDG();                                        // 喂养保持连接看门狗
		if(cmdType == CMD_KEEPCONNECT) {                               // 如果是保持连接命令
			Feed_KeepConnect_WDG();
		}
		else if(cmdType == CMD_CALI_CANCEL) { //取消校准指令
			// 取消校准指令
			Feed_KeepConnect_WDG();
			Cali_CancelCali();//取消校准指令
		}
		else if(cmdType == 0x10) { // 修改增益
			g_Emat.gain_x10 = ((uint16_t)wifiRxData_buf[3]<<8) | wifiRxData_buf[4];
			g_Emat.gain_x10 = (g_Emat.gain_x10 - 0)*(GAINx10_MAX - GAINx10_MIN)/1000 + GAINx10_MIN;
			SetGain(g_Emat.gain_x10);
			Feed_KeepConnect_WDG();
		}
		else { //接收到的指令为普通任务，先挂起
			wifiTaskPending = 1;
			return 0x03;
		}
		wifiTaskPending = 0;
	}
	return 0;
}

static uint8_t keepTest = 0;                                          // 静态变量：保持连接测试计数器
static uint8_t caliTest = 0;                                           // 静态变量：校准测试计数器
uint8_t WifiCmdExe(void) {                                             // WiFi命令执行函数
	uint8_t tmp;                                                       // 临时变量
	uint16_t dBx10;                                                    // 增益值变量（声明但未使用）
	uint8_t sampRate;                                                  // 采样率变量
	uint16_t caliThickness;                                            // 校准厚度变量
	uint8_t DAQ_samp_freq_tmp;                                         // 临时采样频率变量
	if((wifiRxData_buf[0] != 0xCD) || (wifiRxData_buf[1] != 0xEF))     // 检查命令头是否正确
		return 0x01;                                                   // 命令头错误，返回0x01
	switch(wifiRxData_buf[2]) {                                        // 根据命令类型进行分支处理
		case 0x02 : // 建立连接指令                                   // 命令0x02：建立/断开连接
			if(wifiRxData_buf[3]==0x01) { // 建立连接                  // 如果参数为0x01，表示建立连接
				int ipAddress;                                         // APP的IP地址变量
				if(g_Dev.controlByApp == 0) { // 设备处于未连接状态才可以被连接 // 只有设备未被APP控制时才能建立新连接
					ipAddress = ((uint32_t)wifiRxData_buf[4]<<24) | ((uint32_t)wifiRxData_buf[5]<<16) | ((uint32_t)wifiRxData_buf[6]<<8) | ((uint32_t)wifiRxData_buf[7]); // 从命令数据中提取32位IP地址
					delay_ms(10);//勿删                               // 延时10毫秒（注释标明不可删除，可能是时序要求）
					setAppIpAddress(ipAddress);                        // 设置APP的IP地址到ESP8266
					g_Emat.Emit_Switch = 0;// 关闭发射,连接上默认不发射，此处清除发射 // 关闭EMAT发射，连接时默认不发射
					SendParameter2FPGA_PowerDown();//发送低功耗指令    // 发送低功耗指令到FPGA，节省电能
//					quitMenu();                                        // 退出菜单（已注释）
					GUI_DrawSnow(g_Emat.Emit_Switch);//显示雪花        // 在OLED上显示雪花图标（表示未发射状态）
					delay_ms(100);                                     // 延时100毫秒
					wifiTxData_buf[0] = 0x0A;                          // 设置响应命令头
					wifiTxData_buf[1] = 0x02;                          // 设置响应命令类型
					wifiTxData_buf[2] = Battery_percent();             // 获取电池电量百分比
					wifiTxData_buf[3] = (g_Dev.firmWareVersion >> 24) & 0x000000ff; // 固件版本号的最高字节
					wifiTxData_buf[4] = (g_Dev.firmWareVersion >> 16) & 0x000000ff; // 固件版本号的第二字节
					wifiTxData_buf[5] = (g_Dev.firmWareVersion >> 8) & 0x000000ff;  // 固件版本号的第三字节
					wifiTxData_buf[6] = (g_Dev.firmWareVersion) & 0x000000ff;       // 固件版本号的最低字节
					if(BATTERY_CHARGE == 1) {                          // 如果电池正在充电
						wifiTxData_buf[2] = 120;                       // 设置电量显示为120（充电标识）
					}
					WifiSendCmd(wifiTxData_buf);                       // 发送连接确认响应到APP
					g_Dev.wifiState = 0x02; //建链接                   // 设置WiFi状态为已连接（0x02）
					GUI_DrawWifi(0x02);                                // 在OLED上显示WiFi连接状态图标
					g_Dev.controlByApp = 1;                            // 设置APP控制标志，将控制权交给APP
					Start_KeepConnect_WDG();                           // 启动保持连接看门狗定时器
				}
			}
			else { // 断开连接                                        // 如果参数不是0x01，表示断开连接
				g_Dev.wifiState = 0x01;                                // 设置WiFi状态为开启但未连接（0x01）
				AppBreakConnectHandler();                              // 调用APP断开连接处理函数
			}
			Feed_KeepConnect_WDG();                                    // 喂养保持连接看门狗
			break;
		case 0x03 : // 保持连接指令
			keepTest++;
			Feed_KeepConnect_WDG();
			break;
		case 0x05 : // 开启/关闭发射
			if(wifiRxData_buf[3]==0x00) {
				g_Emat.Emit_Switch = 0;// 关闭发射
				SendParameter2FPGA_PowerDown();//发送低功耗指令
				GUI_DrawSnow(g_Emat.Emit_Switch);//显示雪花
			}
			else {
				g_Emat.US_avgTimes = ((uint16_t)wifiRxData_buf[5]<<8) | wifiRxData_buf[6];
				g_Thk.ultraSonic_Velocity = ((uint16_t)wifiRxData_buf[7]<<8) | wifiRxData_buf[8];
				Daq_SetAGC(wifiRxData_buf[10]);
//				if(ACG(Emat_GetAvg(), wifiRxData_buf[9]) == 0) {
//				}
				g_Emat.Emit_Switch = 1;
				GUI_DrawSnow(g_Emat.Emit_Switch);//不显示雪花
			}
			Feed_KeepConnect_WDG();
			break;
		case 0x10 : // 修改增益
			g_Emat.gain_x10 = ((uint16_t)wifiRxData_buf[3]<<8) | wifiRxData_buf[4];
//			g_Emat.gain_x10 = (g_Emat.gain_x10 - 0)*(555 - 75)/1000 + 75;
			g_Emat.gain_x10 = (g_Emat.gain_x10 - 0)*(GAINx10_MAX - GAINx10_MIN)/1000 + GAINx10_MIN;
			SetGain(g_Emat.gain_x10);
			Daq_SetAGC(0);
			Feed_KeepConnect_WDG();
			break;
		case 0x11 : // 修改采样率
			sampRate = wifiRxData_buf[3];
			SendParameter2FPGA(sampRate, g_Emat.US_avgTimes);
			Feed_KeepConnect_WDG();
			break;
		case 0x12 : // 修改声速
			g_Thk.ultraSonic_Velocity = ((uint16_t)wifiRxData_buf[3]<<8) | wifiRxData_buf[4];
			Feed_KeepConnect_WDG();
			break;
		case 0x13 : // 修改平均次数
			g_Emat.US_avgTimes = ((uint16_t)wifiRxData_buf[3]<<8) | wifiRxData_buf[4];
			SendParameter2FPGA(g_Daq.fs_MHz, g_Emat.US_avgTimes);
			Feed_KeepConnect_WDG();
			break; 
		case 0x14 : // 校准指令
			caliTest++;
			wifiTxData_buf[0] = 0x0A;
			wifiTxData_buf[1] = 0x14;
			DAQ_samp_freq_tmp = g_Daq.fs_MHz;
			Feed_KeepConnect_WDG();
			caliThickness = ((uint16_t)wifiRxData_buf[3]<<8) | wifiRxData_buf[4];
			
			if(caliThickness == 0) {

			}
			else {
				tmp = Calibrate_Velocity((uint32_t)(caliThickness)); //1:校准声速
				if(tmp == 0) {
					wifiTxData_buf[2] = 0x01; // 校准成功
				}
				else if(tmp == 1){
					wifiTxData_buf[2] = 0x00; // 校准失败
				}
				else if(tmp == 2) {
					break; //校准取消
				}
				wifiTxData_buf[3] = (g_Thk.ultraSonic_Velocity>>8)&0x00ff;
				wifiTxData_buf[4] =g_Thk.ultraSonic_Velocity&0x00ff;
				WifiSendCmd(wifiTxData_buf);
				SendParameter2FPGA(DAQ_samp_freq_tmp, g_Emat.US_avgTimes);
			}
			
			break;
		case 0x20 : // 增益模式
			if(wifiRxData_buf[3] == 1) {
				if(wifiRxData_buf[4] <= 1) g_Emat.Emit_AB_phase    = wifiRxData_buf[4];
				if(wifiRxData_buf[5] <= 1) g_Emat.Emit_brakePulNum = wifiRxData_buf[5];
				g_Emat.Emit_brakePulNum = 0;//暂不开放
				SendParameter2FPGA(g_Daq.fs_MHz, g_Emat.US_avgTimes);
				DataPersistence_SaveAllDatas(1);
			}
			Feed_KeepConnect_WDG();
			break;
		case 0x21 : // 设置上传数据对应的 ADC 位数
			if(wifiRxData_buf[3] == 12) {
				g_Wifi.adcDataBitsToApp = 12;
			}
			else if(wifiRxData_buf[3] == 14) {
				g_Wifi.adcDataBitsToApp = 14;
			}
			else {
				g_Wifi.adcDataBitsToApp = 12;
			}
			Feed_KeepConnect_WDG();
			break;
		case 0x22 : // 单独传输厚度值
			{
				uint32_t thicknessUm;
				uint8_t i;

				// 清空发送缓冲区
				for(i = 0; i < 32; i++) {
					wifiTxData_buf[i] = 0;
				}

				wifiTxData_buf[0] = 0x0A;
				wifiTxData_buf[1] = 0x22;
				// 将厚度值转换为32位整数（以微米为单位）
				thicknessUm = (uint32_t)g_Thk.thkResValUm;
				wifiTxData_buf[2] = (thicknessUm >> 24) & 0xFF;
				wifiTxData_buf[3] = (thicknessUm >> 16) & 0xFF;
				wifiTxData_buf[4] = (thicknessUm >> 8) & 0xFF;
				wifiTxData_buf[5] = thicknessUm & 0xFF;
				// 添加厚度值有效性标志
				wifiTxData_buf[6] = g_Thk.resValid;

				// 在OLED上显示调试信息
				WifiSendCmd(wifiTxData_buf);
				Feed_KeepConnect_WDG();
			}
			break;
		case 0x23 : // 设置数据传输模式
			g_Wifi.sendThicknessOnly = wifiRxData_buf[3]; // 0: 完整数据包; 1: 只发送厚度值
			wifiTxData_buf[0] = 0x0A;
			wifiTxData_buf[1] = 0x23;
			wifiTxData_buf[2] = g_Wifi.sendThicknessOnly; // 确认设置的模式
			WifiSendCmd(wifiTxData_buf);
			Feed_KeepConnect_WDG();
			break;
		default:;
	}
}

void updatePackageParameter(uint32_t thickness, uint16_t sampRate, uint16_t velocity, uint16_t rep, uint16_t ultraSonicFreq) {
	uint8_t combinePara1 = 0;
	uint8_t adcBits = Fpga_GetAdcBits();
	combinePara1 = adcBits & 0x1f;
	
	dataPackageParameter_buf[0] = (thickness >> 16) & 0x0000ffff;
	dataPackageParameter_buf[1] = thickness & 0x0000ffff;
	dataPackageParameter_buf[2] = sampRate;
	dataPackageParameter_buf[3] = velocity;
	dataPackageParameter_buf[4] = combinePara1; //组合参数
	dataPackageParameter_buf[5] = 0;
//	g_Emat.gain_x10 = (g_Emat.gain_x10 - 75)*1000/(555-75); // 将增益放到大0~100的空间
	dataPackageParameter_buf[6] = (g_Emat.gain_x10 - GAINx10_MIN)*1000/(GAINx10_MAX-GAINx10_MIN);
	dataPackageParameter_buf[7] = g_Emat.US_avgTimes;
	dataPackageParameter_buf[8] = Zpt_GetZptTimeNs();
	dataPackageParameter_buf[9] = THK_GetBlindPointNum(); //自动算法数据偏移地址
	dataPackageParameter_buf[10] = 0;//g_thick.pos_threshold & 0x0000ffff; //自动算法的阈值
	dataPackageParameter_buf[11] = 0;//g_thick.SelPointNum; //计算此次厚度所使用的极值数
	dataPackageParameter_buf[12] = 0;//test_selectPointSinglePeak;
	dataPackageParameter_buf[13] = ((g_Emat.Emit_AB_phase&0x01)<<0) | ((g_Emat.Emit_brakePulNum&0x03)<<1);//0:ABphase; 1~2:brakenum
	dataPackageParameter_buf[14] = 0;//abs(g_thick.neg_threshold) & 0x0000ffff;//result_posPeak_buf[0][4];
}

// 设置ESP8266 的SSID和密码
// SSID 字符串最大14, 以结束符收尾
// 密码 字符串最大14, 以结束符收尾
void setSsidPassword(char *ssid, char *password) {
	uint8_t i = 0;
	uint8_t j = 0;
	uint8_t txBuf[32];
	txBuf[0] = 0x0E;
	txBuf[1] = 0x05;
	j = 2;
	
	for(i = 0; i < 15; i++) {
		if(i == 14) {
			ssid[i] = '\0';
		}
		txBuf[j++] = ssid[i];
		if(ssid[i] == '\0') {
			break;
		}
	}
	j = 17;
	for(i = 0; i < 15; i++) {
		if(i == 14) {
			password[i] = '\0';
		}
		txBuf[j++] = password[i];
		if(ssid[i] == '\0') {
			break;
		}
	}
	
	WifiSendCmd(txBuf);
}

// APP 的连接断开后，处理相关任务                                      // 注释：APP断开连接后的清理处理函数
void AppBreakConnectHandler() {                                        // APP断开连接处理函数
	GUI_DrawWifi(g_Dev.wifiState);                                     // 更新OLED上的WiFi状态显示图标
	g_Emat.Emit_Switch = 0;// 关闭发射                                 // 关闭EMAT发射功能
	SendParameter2FPGA_PowerDown();//发送低功耗指令                     // 发送低功耗指令到FPGA，节省电能
	g_Dev.controlByApp = 0;                                            // 清除APP控制标志，将控制权交还给设备本身
	GUI_DrawSnow(g_Emat.Emit_Switch);//显示雪花                        // 在OLED上显示雪花图标（表示未发射状态）
	Close_KeepConnect_WDG();                                           // 关闭保持连接看门狗定时器
}
// 发送校准进度指令                                                   // 注释：向APP发送校准进度信息
void SendCaliProgCmd2App(uint8_t progress) {                          // 发送校准进度命令到APP
	wifiTxData_buf[0] = 0x0A;                                          // 设置命令头
	wifiTxData_buf[1] = 0x14;                                          // 设置命令类型为校准命令（0x14）
	wifiTxData_buf[2] = 0x02; // 校准进行中                            // 设置校准状态为进行中（0x02）
	wifiTxData_buf[5] = progress;                                      // 设置校准进度百分比（0-100）
	WifiSendCmd(wifiTxData_buf);                                       // 发送命令到APP
}
// 发送电池电量指令到 APP                                             // 注释：向APP发送电池电量信息
void SendBatteryPercentCmd2App(uint8_t val) {                         // 发送电池电量命令到APP
	wifiTxData_buf[0] = 0x0A;                                          // 设置命令头
	wifiTxData_buf[1] = 0x05;                                          // 设置命令类型为电池电量命令（0x05）
	wifiTxData_buf[2] = val;                                           // 设置电池电量百分比（0-100）
	WifiSendCmd(wifiTxData_buf);                                       // 发送命令到APP
}
// 将成功建立连接的APP的IP地址发送给ESP8266                           // 注释：设置APP IP地址到ESP8266模块
void setAppIpAddress(int ipAddress) {                                  // 设置APP IP地址函数
	wifiTxData_buf[0] = 0x0E;                                          // 设置ESP8266配置命令头
	wifiTxData_buf[1] = 0x06;                                          // 设置命令类型为IP地址设置（0x06）
	wifiTxData_buf[2] = (ipAddress >> 24) & 0x000000ff;                // IP地址的最高字节（第一段）
	wifiTxData_buf[3] = (ipAddress >> 16) & 0x000000ff;                // IP地址的第二字节（第二段）
	wifiTxData_buf[4] = (ipAddress >> 8) & 0x000000ff;                 // IP地址的第三字节（第三段）
	wifiTxData_buf[5] = (ipAddress >> 0) & 0x000000ff;                 // IP地址的最低字节（第四段）
	delay_ms(10);                                                      // 延时10毫秒，确保ESP8266处理完成
	WifiSendCmd(wifiTxData_buf);                                       // 发送IP地址设置命令到ESP8266
}

// 设置ESP8266调试模式                                                // 注释：控制ESP8266的调试模式开关
void setESP8266DebugMode(uint8_t isOpen) {                            // 设置ESP8266调试模式函数
	wifiTxData_buf[0] = 0x0E;                                          // 设置ESP8266配置命令头
	wifiTxData_buf[1] = 0x02;                                          // 设置命令类型为调试模式设置（0x02）
	wifiTxData_buf[2] = 0;                                             // 保留字节，设为0
	wifiTxData_buf[3] = 0;                                             // 保留字节，设为0
	wifiTxData_buf[4] = 0;                                             // 保留字节，设为0
	wifiTxData_buf[5] = 0;                                             // 保留字节，设为0
	wifiTxData_buf[6] = isOpen;                                        // 调试模式开关：1=开启调试，0=关闭调试
	delay_ms(10);                                                      // 延时10毫秒，确保ESP8266处理完成
	WifiSendCmd(wifiTxData_buf);                                       // 发送调试模式设置命令到ESP8266
}

// 发送厚度值数据包到APP                                               // 注释：向APP发送单独的厚度测量值
void SendThicknessValueCmd2App(uint32_t thicknessUm, uint8_t isValid) { // 发送厚度值命令到APP函数
	uint8_t i;                                                         // 循环计数器

	// 清空发送缓冲区                                                  // 注释：清零发送缓冲区，确保数据干净
	for(i = 0; i < 32; i++) {                                          // 循环清空32字节缓冲区
		wifiTxData_buf[i] = 0;                                         // 将每个字节设为0
	}

	wifiTxData_buf[0] = 0x0A;                                          // 设置命令头
	wifiTxData_buf[1] = 0x30; // 使用0x30作为厚度值推送命令码          // 设置命令类型为厚度值推送（0x30）
	// 将厚度值转换为32位整数（以微米为单位）                          // 注释：将32位厚度值分解为4个字节
	wifiTxData_buf[2] = (thicknessUm >> 24) & 0xFF;                    // 厚度值的最高字节（位31-24）
	wifiTxData_buf[3] = (thicknessUm >> 16) & 0xFF;                    // 厚度值的第二字节（位23-16）
	wifiTxData_buf[4] = (thicknessUm >> 8) & 0xFF;                     // 厚度值的第三字节（位15-8）
	wifiTxData_buf[5] = thicknessUm & 0xFF;                            // 厚度值的最低字节（位7-0）
	// 添加厚度值有效性标志                                            // 注释：添加测量结果的有效性标志
	wifiTxData_buf[6] = isValid;                                       // 有效性标志：1=测量有效，0=测量无效（如AGC调整中）
	WifiSendCmd(wifiTxData_buf);                                       // 发送厚度值命令到APP
}

























