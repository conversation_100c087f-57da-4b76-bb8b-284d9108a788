
//(内部代号第三版/第四版)
//wifi ID记录:
//2018.10.xx : 鄂尔多斯(8) #0101~#0108
//2018.11.xx : 济宁鲁科(1) #0100
//2018.12.17 : 济宁鲁科(1) #0110
//2018.12.20 : #0111(2),#0112,#0113,#0114(2)
//2018.12.24 : #0115(2), #0116
//2018.12.25 : #0109(2),#0117,#0118(2),#0119(2)
//2018.12.28 : #0120(2),#0121(2),#0122(2),#0123(2),#0124(2),#0125(2)
//2018.01.08 : #0126(2),#0127(2)
//2019.03.07 : #0132(2),#0133(2),#0134(2),#0135(2),#0136(2),#0137(2),#0138(2),#0139(2),#0140(2),#0141(2),#0142(2),#0143(2),#0144(2),
//		#0145(2),#0146(2),#0147(2),#0148(2),#0149(),#0150()
//2019.05.22 : #0151(2)，#0152(2)
//2019.05.29 : #0153(2), #0154(2), #0155(2), #0156(2), #0157(2), #0158(2), #0159(2), #0160(2), #0161(2), #0162(2), #0163(2), #0164(2), #0165(2),
// 		#0166(2), #0167(2), #0168(2), #0169(2), #0170(2), #0171(2), #0172(2), #0173(2), #0174(2), #0175(2), #0176(2), #0177(2), #0178(2), #0179(2), #0180(2)
//2019.09.18 : #0200(2),#0201(2),#0202(175),#0203(160),#0204(153),#0205(162),#0206(158),,#0002(179),,#0003(172),,#0001(143),
//2019.09.18 : #0207(161),#0208(168),#0209(174),#0210(177),#0211(169),#0212(157),
//50套新板
//2019.11.11 : #0301,#0302,#0303,#0304,#0305,
//2019.11.26 : #0154 -> #0124
//2019.12.05 : #0306,#0307,#0308,#0309,#0310,#0311,#0312,#0313,#0314,#0315,,#0316,#0317,#0318,#0319,#0320,#0321,#0322,#0323,#0324
//2020.01.08 : #0205 -> #0110, #0206 -> #0112, #0207 -> #0116
//2020.03.23 : #0211 -> #0139(零点355,-245)
//2020.11.17 : #0325, #0326, #0327

//版本更新记录：
//20200401 -> 920200402:
//死机处理;
//920200402 -> 920200403：
//关闭多次结果比较,改为直接出值;

#include "wifi.h"
#include <string.h>
#include "stm32f7xx_hal.h"
#include "thk.h"
#include "delay.h"
#include "oled.h"
#include "dac.h"
#include "timer.h"
#include "adc.h"
#include "fpga.h"
//#include "main.h"
#include "emat.h" 
#include "app.h"
#include "bsp.h"
#include "app_cali.h"
#include "app_thickness.h"
#include "data_persistence.h"
#include "bsp_zpt.h"


Wifi_t g_Wifi;

//EN(output):PB6; RST(output):PD2; BUSY(input):PA12(IO4); NEED_READ(DATA_VALID)(input):PC10(IO2);
//CS(output):PC11; 
#define WIFI_EN(n) 			HAL_GPIO_WritePin(GPIOB, GPIO_PIN_6, n ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define WIFI_RST(n) 		HAL_GPIO_WritePin(GPIOD, GPIO_PIN_2, n ? GPIO_PIN_SET : GPIO_PIN_RESET)
#define WIFI_CS(n) 			HAL_GPIO_WritePin(GPIOC, GPIO_PIN_11, n ? GPIO_PIN_SET : GPIO_PIN_RESET)

#define WIFI_BUSY 			HAL_GPIO_ReadPin(GPIOA, GPIO_PIN_12) //PAin(12) // ESP8266-IO4
#define WIFI_NEED_READ_SPI 	HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_10) //ESP8266-IO2

static void SPI1_Init(void);			 //初始化SPI1口
static void SPI1_SetSpeed(uint8_t SPI_BaudRatePrescaler); //设置SPI1速度   
static uint8_t SPI1_ReadWriteByte(uint8_t TxData);//SPI1总线读写一个字节

#define RX_WIFI_CAHCE_DEEPTH 5
#define WIFI_VERSION_2TH 0 // 0:第三版/第四版; 1:第二版
//uint8_t wifiReceivedCmd = 0;

uint8_t wifiRxData_buf[32];
//uint8_t wifiRxData_buf[RX_WIFI_CAHCE_DEEPTH][32];
uint8_t wifiRx_RdCache_id = 0;
uint8_t wifiRx_WrCache_id = 0;
uint8_t wifiTxData_buf[64];
uint16_t dataPackageParameter_buf[30];
uint16_t wifiSendData_buf[4200];
static uint16_t frameId = 0;
//uint8_t isAutoACG = 1;

static SPI_HandleTypeDef hspi1;
////u32 g_Dev.firmWareVersion = 18092814;
//u32 g_Dev.firmWareVersion = 920210819;//920200402;//20200401;//20030601;//20011401;//19090101;//19081201;//19041201;//19011801;//18122801;//18122801开始,wifi版本更新为第二版
////This location reads the software version.

// ESP8266 EN/RST 脚复位时，HSPI_CS(IO15)脚需要为低，否则无法复位
// HSPI_CS(IO15) 的状态对开启关闭ESP8266很敏感
void Wifi_Init(void)
{
	//EN(output):PB6; RST(output):PD2; BUSY(input):PA12(IO4); NEED_READ(DATA_VALID)(input):PC10(IO2);
	//CS:PC11;
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	
	__HAL_RCC_GPIOA_CLK_ENABLE();
	__HAL_RCC_GPIOB_CLK_ENABLE();
	__HAL_RCC_GPIOC_CLK_ENABLE();
	__HAL_RCC_GPIOD_CLK_ENABLE();
	
	// EN 脚初始化
	GPIO_InitStruct.Pin = GPIO_PIN_6;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
	
	// RST 脚初始化
	GPIO_InitStruct.Pin = GPIO_PIN_2;
	HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
	WIFI_RST(1);
	
	// CS 脚初始化
	GPIO_InitStruct.Pin = GPIO_PIN_11;
	HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
	
	// ESP8266 BUSY 引脚初始化
	GPIO_InitStruct.Pin = GPIO_PIN_12;
	GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
	GPIO_InitStruct.Pull = GPIO_PULLDOWN; //GPIO_NOPULL; //
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
	
	// ESP8266 NEED_READ 引脚初始化
	GPIO_InitStruct.Pin = GPIO_PIN_10;
	HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);

	WIFI_EN(0);
	delay_ms(100);
	SPI1_Init();

	// 初始化WiFi结构体
	g_Wifi.adcDataBitsToApp = 12;
	g_Wifi.sendThicknessOnly = 0; // 默认发送完整数据包


}

void setESP8266_Ssid_Pwd(void) {
//	strcpy(g_Dev.deviceSN, "PREMAT3#0000");
	OpenWifi();
	delay_ms(1000);	
	setSsidPassword(g_Dev.deviceSN, "PREMAT3Y");
}

//以下是SPI模块的初始化代码，配置成主机模式 						  
//SPI1初始化
//SCLK: PB3; MISO: PB4; MOSI: PB5; CS: PC11;
static void SPI1_Init(void) {
	hspi1.Instance = SPI1;
	hspi1.Init.Mode = SPI_MODE_MASTER;
	hspi1.Init.Direction = SPI_DIRECTION_2LINES;
	hspi1.Init.DataSize = SPI_DATASIZE_8BIT;
	hspi1.Init.CLKPolarity = SPI_POLARITY_LOW;
	hspi1.Init.CLKPhase = SPI_PHASE_1EDGE;
	hspi1.Init.NSS = SPI_NSS_SOFT;
	hspi1.Init.BaudRatePrescaler = SPI_BAUDRATEPRESCALER_16;
	hspi1.Init.FirstBit = SPI_FIRSTBIT_MSB;
	hspi1.Init.TIMode = SPI_TIMODE_DISABLE;
	hspi1.Init.CRCCalculation = SPI_CRCCALCULATION_DISABLE;
	hspi1.Init.CRCPolynomial = 7;

	if (HAL_SPI_Init(&hspi1) != HAL_OK)
	{
		Bsp_ErrorHandler();
	}
	
	__HAL_SPI_ENABLE(&hspi1);
	SPI1_ReadWriteByte(0Xff); //启动传输
	WIFI_CS(1);
}


void HAL_SPI_MspInit(SPI_HandleTypeDef* spiHandle)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	if(spiHandle->Instance==SPI1)
	{
		/* SPI1 clock enable */
		__HAL_RCC_SPI1_CLK_ENABLE();

		__HAL_RCC_GPIOB_CLK_ENABLE();
		/**SPI1 GPIO Configuration
		PB3     ------> SPI1_SCK
		PB4     ------> SPI1_MISO
		PB5     ------> SPI1_MOSI
		*/
		GPIO_InitStruct.Pin = GPIO_PIN_3|GPIO_PIN_4|GPIO_PIN_5;
		GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
		GPIO_InitStruct.Pull = GPIO_PULLUP;
		GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
		GPIO_InitStruct.Alternate = GPIO_AF5_SPI1;
		HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
	}
}


//SPI1速度设置函数
//SPI速度=fAPB2/分频系数
//@ref SPI_BaudRate_Prescaler:SPI_BaudRatePrescaler_2~SPI_BaudRatePrescaler_256  
//fAPB2时钟一般为84Mhz：
static void SPI1_SetSpeed(uint8_t SPI_BaudRatePrescaler)
{
	assert_param(IS_SPI_BAUDRATE_PRESCALER(SPI_BaudRatePrescaler));//判断有效性
    __HAL_SPI_DISABLE(&hspi1);            //关闭SPI
    hspi1.Instance->CR1&=0XFFC7;          //位3-5清零，用来设置波特率
    hspi1.Instance->CR1|=SPI_BaudRatePrescaler;//设置SPI速度
	__HAL_SPI_ENABLE(&hspi1); //使能SPI1
} 
//SPI1 读写一个字节
//TxData:要写入的字节
//返回值:读取到的字节
static uint8_t SPI1_ReadWriteByte(uint8_t txData)
{		 			 
	uint8_t rxData;
    HAL_SPI_TransmitReceive(&hspi1, &txData, &rxData, 1, 1000);       
 	return rxData;
}

uint8_t Wifi_ReceiveData(void){
	uint8_t i = 0;
	uint8_t slaveState;

	WIFI_CS(0);
	SPI1_ReadWriteByte(0x03); // 读取Slave指令
	SPI1_ReadWriteByte(0x00); // Slave 地址
	for( i = 0; i < 32; i++){ // 读取数据
		wifiRxData_buf[i] = SPI1_ReadWriteByte(i);
	}

	WIFI_CS(1);
}

void WifiSendCmd(uint8_t *buf){
	uint8_t i = 0;
	WIFI_CS(0);
	SPI1_ReadWriteByte(0x02); // SPI写指令
	SPI1_ReadWriteByte(0x00); // SPI写地址
	for( i =0; i < 32;i++){
		SPI1_ReadWriteByte(buf[i]);
	}
	delay_us(2);
	WIFI_CS(1);
}

static void SPI_WriteBytes(uint16_t *buf, uint8_t endNum){
	uint8_t i = 0;
	WIFI_CS(0);
	SPI1_ReadWriteByte(0x02); // SPI写指令
	SPI1_ReadWriteByte(0x00); // SPI写地址
	SPI1_ReadWriteByte(0x0D|(endNum<<4)); // 帧/尾帧 标识
	SPI1_ReadWriteByte(frameId&0x00ff); // 包ID
	for( i =0; i < 15;i++){
		SPI1_ReadWriteByte((uint8_t)(buf[i]>>8)&0x00ff);
		SPI1_ReadWriteByte((uint8_t)(buf[i]&0x00ff));
	}
	delay_us(2);
	WIFI_CS(1);
}

void Wifi_SendData(uint16_t *buf, uint16_t length) {
	int32_t j = 0;
	int32_t bufOffset = 0;
	uint16_t i = 0;
	uint16_t groupNum; //需要发送多少次30字节数据
	uint16_t tmpBuf[64];
	frameId++;
	
	groupNum = length/15; // 1020点68次; 510点34次;
	if((length%15) != 0) {
		groupNum = groupNum + 1;
	}
	for(i = 0; i < groupNum; i++) {
		
		//波形除以4，即将14位转成12位发送给 App
		if(i == (groupNum-1)) {
			bufOffset = 15*i;
		}
		else {
			bufOffset = 15*(i-1);
		}
		for(j = 0; j < 30; j++) {
			tmpBuf[j] = ((int16_t*)buf)[bufOffset + j]/4;
		}
		
		if(i == (groupNum-1)) {
			SPI_WriteBytes(tmpBuf, length - (groupNum-1)*15);
		}
		else {
			if(i == 0) { //第一帧发送参数(30Byte)
				SPI_WriteBytes(dataPackageParameter_buf, 0x00);
			}
			else {
				SPI_WriteBytes(tmpBuf, 0x00);
			}
		}

		delay_us(10);
		while(WIFI_BUSY);
		delay_us(2);
	}
}

void CloseWifi(void) {
	delay_ms(1);
	WIFI_EN(0);
	g_Dev.wifiState = 0;
	g_Dev.controlByApp = 0;
	Close_KeepConnect_WDG();
	DataPers_WriteWifiEnable(0);
}
// 开启wifi, 需要在数百毫秒内保持 HSPI_CS(IO15) 引脚为低!
void OpenWifi(void) {
	WIFI_CS(0);
	delay_ms(100);
	WIFI_EN(1);
	delay_ms(500); // 由于开启了 HSPI 接收中断, 需要延迟后 g_Dev.wifiState = 1
	g_Dev.wifiState = 1;
	WIFI_CS(1);
	DataPers_WriteWifiEnable(1);
}

uint8_t getWifiState(void) {
	return g_Dev.wifiState;
}


// 查询是否需要处理指令任务
static uint8_t tst_wifiRxCmdCnt = 0;
static uint8_t wifiTaskPending = 0; //是否有wifi指令待处理

uint8_t WifiCmdPolling(uint8_t priority) { // 处理等级,0:所有任务; 1:处理及时性相关任务(电机)
	uint8_t cmdType;
	
	while(1) {
		if(wifiTaskPending == 0) { //无待处理任务
			if(WIFI_NEED_READ_SPI == 1) { // 判断是否有新数据需要接收	
				delay_us(20); //去重复,因为STM32该任务完成远快于ESP8266
				if(WIFI_NEED_READ_SPI == 1) {
					Wifi_ReceiveData();
					wifiTaskPending = 1;
					if(wifiRxData_buf[2] != CMD_KEEPCONNECT) {
						tst_wifiRxCmdCnt++;
					}
				}
				else {
					return 0x02;
				}
			}
			else {
				return 0x02;
			}
		}
		//有待处理任务
		if((wifiRxData_buf[0] != 0xCD) || (wifiRxData_buf[1] != 0xEF)) {
			wifiTaskPending = 0;
			continue;
		}
		cmdType = wifiRxData_buf[2];
		if(priority == 0) { //所有指令都可以执行
			wifiTaskPending = 0;
			WifiCmdExe();
		}
		else if(priority == 1) { //紧急任务
			break;
		}
	}
	
	if(priority == 1) { //紧急任务
		Feed_KeepConnect_WDG();
		if(cmdType == CMD_KEEPCONNECT) {
			Feed_KeepConnect_WDG();
		}
		else if(cmdType == CMD_CALI_CANCEL) { //取消校准指令
			// 取消校准指令
			Feed_KeepConnect_WDG();
			Cali_CancelCali();//取消校准指令
		}
		else if(cmdType == 0x10) { // 修改增益
			g_Emat.gain_x10 = ((uint16_t)wifiRxData_buf[3]<<8) | wifiRxData_buf[4];
			g_Emat.gain_x10 = (g_Emat.gain_x10 - 0)*(GAINx10_MAX - GAINx10_MIN)/1000 + GAINx10_MIN;
			SetGain(g_Emat.gain_x10);
			Feed_KeepConnect_WDG();
		}
		else { //接收到的指令为普通任务，先挂起
			wifiTaskPending = 1;
			return 0x03;
		}
		wifiTaskPending = 0;
	}
	return 0;
}

static uint8_t keepTest = 0;
static uint8_t caliTest = 0;
uint8_t WifiCmdExe(void) {
	uint8_t tmp;
	uint16_t dBx10;
	uint8_t sampRate;
	uint16_t caliThickness;
	uint8_t DAQ_samp_freq_tmp;
	if((wifiRxData_buf[0] != 0xCD) || (wifiRxData_buf[1] != 0xEF))
		return 0x01;
	switch(wifiRxData_buf[2]) {
		case 0x02 : // 建立连接指令
			if(wifiRxData_buf[3]==0x01) { // 建立连接
				int ipAddress;
				if(g_Dev.controlByApp == 0) { // 设备处于未连接状态才可以被连接
					ipAddress = ((uint32_t)wifiRxData_buf[4]<<24) | ((uint32_t)wifiRxData_buf[5]<<16) | ((uint32_t)wifiRxData_buf[6]<<8) | ((uint32_t)wifiRxData_buf[7]);
					delay_ms(10);//勿删
					setAppIpAddress(ipAddress);
					g_Emat.Emit_Switch = 0;// 关闭发射,连接上默认不发射，此处清除发射
					SendParameter2FPGA_PowerDown();//发送低功耗指令
//					quitMenu();
					GUI_DrawSnow(g_Emat.Emit_Switch);//显示雪花
					delay_ms(100);
					wifiTxData_buf[0] = 0x0A;
					wifiTxData_buf[1] = 0x02;
					wifiTxData_buf[2] = Battery_percent();
					wifiTxData_buf[3] = (g_Dev.firmWareVersion >> 24) & 0x000000ff;
					wifiTxData_buf[4] = (g_Dev.firmWareVersion >> 16) & 0x000000ff;
					wifiTxData_buf[5] = (g_Dev.firmWareVersion >> 8) & 0x000000ff;
					wifiTxData_buf[6] = (g_Dev.firmWareVersion) & 0x000000ff;
					if(BATTERY_CHARGE == 1) {
						wifiTxData_buf[2] = 120;
					}
					WifiSendCmd(wifiTxData_buf);
					g_Dev.wifiState = 0x02; //建链接
					GUI_DrawWifi(0x02);
					g_Dev.controlByApp = 1;
					Start_KeepConnect_WDG();
				}
			}
			else { // 断开连接
				g_Dev.wifiState = 0x01;
				AppBreakConnectHandler();
			}
			Feed_KeepConnect_WDG();
			break;
		case 0x03 : // 保持连接指令
			keepTest++;
			Feed_KeepConnect_WDG();
			break;
		case 0x05 : // 开启/关闭发射
			if(wifiRxData_buf[3]==0x00) {
				g_Emat.Emit_Switch = 0;// 关闭发射
				SendParameter2FPGA_PowerDown();//发送低功耗指令
				GUI_DrawSnow(g_Emat.Emit_Switch);//显示雪花
			}
			else {
				g_Emat.US_avgTimes = ((uint16_t)wifiRxData_buf[5]<<8) | wifiRxData_buf[6];
				g_Thk.ultraSonic_Velocity = ((uint16_t)wifiRxData_buf[7]<<8) | wifiRxData_buf[8];
				Daq_SetAGC(wifiRxData_buf[10]);
//				if(ACG(Emat_GetAvg(), wifiRxData_buf[9]) == 0) {
//				}
				g_Emat.Emit_Switch = 1;
				GUI_DrawSnow(g_Emat.Emit_Switch);//不显示雪花
			}
			Feed_KeepConnect_WDG();
			break;
		case 0x10 : // 修改增益
			g_Emat.gain_x10 = ((uint16_t)wifiRxData_buf[3]<<8) | wifiRxData_buf[4];
//			g_Emat.gain_x10 = (g_Emat.gain_x10 - 0)*(555 - 75)/1000 + 75;
			g_Emat.gain_x10 = (g_Emat.gain_x10 - 0)*(GAINx10_MAX - GAINx10_MIN)/1000 + GAINx10_MIN;
			SetGain(g_Emat.gain_x10);
			Daq_SetAGC(0);
			Feed_KeepConnect_WDG();
			break;
		case 0x11 : // 修改采样率
			sampRate = wifiRxData_buf[3];
			SendParameter2FPGA(sampRate, g_Emat.US_avgTimes);
			Feed_KeepConnect_WDG();
			break;
		case 0x12 : // 修改声速
			g_Thk.ultraSonic_Velocity = ((uint16_t)wifiRxData_buf[3]<<8) | wifiRxData_buf[4];
			Feed_KeepConnect_WDG();
			break;
		case 0x13 : // 修改平均次数
			g_Emat.US_avgTimes = ((uint16_t)wifiRxData_buf[3]<<8) | wifiRxData_buf[4];
			SendParameter2FPGA(g_Daq.fs_MHz, g_Emat.US_avgTimes);
			Feed_KeepConnect_WDG();
			break; 
		case 0x14 : // 校准指令
			caliTest++;
			wifiTxData_buf[0] = 0x0A;
			wifiTxData_buf[1] = 0x14;
			DAQ_samp_freq_tmp = g_Daq.fs_MHz;
			Feed_KeepConnect_WDG();
			caliThickness = ((uint16_t)wifiRxData_buf[3]<<8) | wifiRxData_buf[4];
			
			if(caliThickness == 0) {

			}
			else {
				tmp = Calibrate_Velocity((uint32_t)(caliThickness)); //1:校准声速
				if(tmp == 0) {
					wifiTxData_buf[2] = 0x01; // 校准成功
				}
				else if(tmp == 1){
					wifiTxData_buf[2] = 0x00; // 校准失败
				}
				else if(tmp == 2) {
					break; //校准取消
				}
				wifiTxData_buf[3] = (g_Thk.ultraSonic_Velocity>>8)&0x00ff;
				wifiTxData_buf[4] =g_Thk.ultraSonic_Velocity&0x00ff;
				WifiSendCmd(wifiTxData_buf);
				SendParameter2FPGA(DAQ_samp_freq_tmp, g_Emat.US_avgTimes);
			}
			
			break;
		case 0x20 : // 增益模式
			if(wifiRxData_buf[3] == 1) {
				if(wifiRxData_buf[4] <= 1) g_Emat.Emit_AB_phase    = wifiRxData_buf[4];
				if(wifiRxData_buf[5] <= 1) g_Emat.Emit_brakePulNum = wifiRxData_buf[5];
				g_Emat.Emit_brakePulNum = 0;//暂不开放
				SendParameter2FPGA(g_Daq.fs_MHz, g_Emat.US_avgTimes);
				DataPersistence_SaveAllDatas(1);
			}
			Feed_KeepConnect_WDG();
			break;
		case 0x21 : // 设置上传数据对应的 ADC 位数
			if(wifiRxData_buf[3] == 12) {
				g_Wifi.adcDataBitsToApp = 12;
			}
			else if(wifiRxData_buf[3] == 14) {
				g_Wifi.adcDataBitsToApp = 14;
			}
			else {
				g_Wifi.adcDataBitsToApp = 12;
			}
			Feed_KeepConnect_WDG();
			break;
		case 0x22 : // 单独传输厚度值
			{
				uint32_t thicknessUm;
				uint8_t i;

				// 清空发送缓冲区
				for(i = 0; i < 32; i++) {
					wifiTxData_buf[i] = 0;
				}

				wifiTxData_buf[0] = 0x0A;
				wifiTxData_buf[1] = 0x22;
				// 将厚度值转换为32位整数（以微米为单位）
				thicknessUm = (uint32_t)g_Thk.thkResValUm;
				wifiTxData_buf[2] = (thicknessUm >> 24) & 0xFF;
				wifiTxData_buf[3] = (thicknessUm >> 16) & 0xFF;
				wifiTxData_buf[4] = (thicknessUm >> 8) & 0xFF;
				wifiTxData_buf[5] = thicknessUm & 0xFF;
				// 添加厚度值有效性标志
				wifiTxData_buf[6] = g_Thk.resValid;

				// 在OLED上显示调试信息
				WifiSendCmd(wifiTxData_buf);
				Feed_KeepConnect_WDG();
			}
			break;
		case 0x23 : // 设置数据传输模式
			g_Wifi.sendThicknessOnly = wifiRxData_buf[3]; // 0: 完整数据包; 1: 只发送厚度值
			wifiTxData_buf[0] = 0x0A;
			wifiTxData_buf[1] = 0x23;
			wifiTxData_buf[2] = g_Wifi.sendThicknessOnly; // 确认设置的模式
			WifiSendCmd(wifiTxData_buf);
			Feed_KeepConnect_WDG();
			break;
		default:;
	}
}

void updatePackageParameter(uint32_t thickness, uint16_t sampRate, uint16_t velocity, uint16_t rep, uint16_t ultraSonicFreq) {
	uint8_t combinePara1 = 0;
	uint8_t adcBits = Fpga_GetAdcBits();
	combinePara1 = adcBits & 0x1f;
	
	dataPackageParameter_buf[0] = (thickness >> 16) & 0x0000ffff;
	dataPackageParameter_buf[1] = thickness & 0x0000ffff;
	dataPackageParameter_buf[2] = sampRate;
	dataPackageParameter_buf[3] = velocity;
	dataPackageParameter_buf[4] = combinePara1; //组合参数
	dataPackageParameter_buf[5] = 0;
//	g_Emat.gain_x10 = (g_Emat.gain_x10 - 75)*1000/(555-75); // 将增益放到大0~100的空间
	dataPackageParameter_buf[6] = (g_Emat.gain_x10 - GAINx10_MIN)*1000/(GAINx10_MAX-GAINx10_MIN);
	dataPackageParameter_buf[7] = g_Emat.US_avgTimes;
	dataPackageParameter_buf[8] = Zpt_GetZptTimeNs();
	dataPackageParameter_buf[9] = THK_GetBlindPointNum(); //自动算法数据偏移地址
	dataPackageParameter_buf[10] = 0;//g_thick.pos_threshold & 0x0000ffff; //自动算法的阈值
	dataPackageParameter_buf[11] = 0;//g_thick.SelPointNum; //计算此次厚度所使用的极值数
	dataPackageParameter_buf[12] = 0;//test_selectPointSinglePeak;
	dataPackageParameter_buf[13] = ((g_Emat.Emit_AB_phase&0x01)<<0) | ((g_Emat.Emit_brakePulNum&0x03)<<1);//0:ABphase; 1~2:brakenum
	dataPackageParameter_buf[14] = 0;//abs(g_thick.neg_threshold) & 0x0000ffff;//result_posPeak_buf[0][4];
}

// 设置ESP8266 的SSID和密码
// SSID 字符串最大14, 以结束符收尾
// 密码 字符串最大14, 以结束符收尾
void setSsidPassword(char *ssid, char *password) {
	uint8_t i = 0;
	uint8_t j = 0;
	uint8_t txBuf[32];
	txBuf[0] = 0x0E;
	txBuf[1] = 0x05;
	j = 2;
	
	for(i = 0; i < 15; i++) {
		if(i == 14) {
			ssid[i] = '\0';
		}
		txBuf[j++] = ssid[i];
		if(ssid[i] == '\0') {
			break;
		}
	}
	j = 17;
	for(i = 0; i < 15; i++) {
		if(i == 14) {
			password[i] = '\0';
		}
		txBuf[j++] = password[i];
		if(ssid[i] == '\0') {
			break;
		}
	}
	
	WifiSendCmd(txBuf);
}

// APP 的连接断开后，处理相关任务
void AppBreakConnectHandler() {
	GUI_DrawWifi(g_Dev.wifiState);
	g_Emat.Emit_Switch = 0;// 关闭发射
	SendParameter2FPGA_PowerDown();//发送低功耗指令
	g_Dev.controlByApp = 0;
	GUI_DrawSnow(g_Emat.Emit_Switch);//显示雪花
	Close_KeepConnect_WDG();
}
// 发送校准进度指令
void SendCaliProgCmd2App(uint8_t progress) {
	wifiTxData_buf[0] = 0x0A;
	wifiTxData_buf[1] = 0x14;
	wifiTxData_buf[2] = 0x02; // 校准进行中
	wifiTxData_buf[5] = progress;
	WifiSendCmd(wifiTxData_buf);
}
// 发送电池电量指令到 APP
void SendBatteryPercentCmd2App(uint8_t val) {
	wifiTxData_buf[0] = 0x0A;
	wifiTxData_buf[1] = 0x05;
	wifiTxData_buf[2] = val;
	WifiSendCmd(wifiTxData_buf);
}
// 将成功建立连接的APP的IP地址发送给ESP8266
void setAppIpAddress(int ipAddress) {
	wifiTxData_buf[0] = 0x0E;
	wifiTxData_buf[1] = 0x06;
	wifiTxData_buf[2] = (ipAddress >> 24) & 0x000000ff;
	wifiTxData_buf[3] = (ipAddress >> 16) & 0x000000ff;
	wifiTxData_buf[4] = (ipAddress >> 8) & 0x000000ff;
	wifiTxData_buf[5] = (ipAddress >> 0) & 0x000000ff;
	delay_ms(10);
	WifiSendCmd(wifiTxData_buf);
}

// 将成功建立连接的APP的IP地址发送给ESP8266
void setESP8266DebugMode(uint8_t isOpen) {
	wifiTxData_buf[0] = 0x0E;
	wifiTxData_buf[1] = 0x02;
	wifiTxData_buf[2] = 0;
	wifiTxData_buf[3] = 0;
	wifiTxData_buf[4] = 0;
	wifiTxData_buf[5] = 0;
	wifiTxData_buf[6] = isOpen;
	delay_ms(10);
	WifiSendCmd(wifiTxData_buf);
}

// 发送厚度值数据包到APP
void SendThicknessValueCmd2App(uint32_t thicknessUm, uint8_t isValid) {
	uint8_t i;

	// 清空发送缓冲区
	for(i = 0; i < 32; i++) {
		wifiTxData_buf[i] = 0;
	}

	wifiTxData_buf[0] = 0x0A;
	wifiTxData_buf[1] = 0x30; // 使用0x30作为厚度值推送命令码
	// 将厚度值转换为32位整数（以微米为单位）
	wifiTxData_buf[2] = (thicknessUm >> 24) & 0xFF;
	wifiTxData_buf[3] = (thicknessUm >> 16) & 0xFF;
	wifiTxData_buf[4] = (thicknessUm >> 8) & 0xFF;
	wifiTxData_buf[5] = thicknessUm & 0xFF;
	// 添加厚度值有效性标志
	wifiTxData_buf[6] = isValid;
	WifiSendCmd(wifiTxData_buf);
}

























