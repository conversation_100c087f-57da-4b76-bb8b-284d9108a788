/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : app.c
  * @project : 
  * @brief   : 
  * <AUTHOR> PR Team
  * @since   : 2024/07/29
  * @version : V1.0 
  * @history :
  *	2024/07/29: V0.1
  *	  1) 首次创建;
  *
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */

#include "app.h"
#include <string.h>
#include <stdio.h>
#include "delay.h"
#include "wifi.h"
#include "thk.h"
#include "app_thickness.h"
#include "app_stressdetector.h"
#include "dac.h"
#include "emat.h"
#include "key.h"
#include "oled.h"
#include "data_persistence.h"
#include "app_version.h"
#include "bsp_zpt.h"
#include "app_verify.h"
#include "gui_lib.h"
#include "ui_draw_wave.h"
#include "app_verify.h"

DEV_T	g_Dev;
EMAT_T	g_Emat;
DAQ_T 	g_Daq;

static int16_t bigbuf1[4096 * 2];

static uint8_t RestoreToFactoryCheck(void);

/**
  * @brief  全局内存管理,用以调配全局大数组
  * @param  None
  * @retval None
  */
static void GlobalMemoryManage(void) {
	//测厚部分, 此处的分配需要参考 App_Thk_Init() 对数据长度的需求!
	g_Thk.wave.waveValid	= 0;
	g_Thk.wave.buf			= bigbuf1;
	g_Thk.wave.buflen		= THK_WAVE_LEN;
	
	g_Thk.longWave.waveValid	= 0;
	g_Thk.longWave.buf 		= MatrixMother;
	g_Thk.longWave.buflen	= THK_LONG_WAVE_LEN;
	
	//轴力部分, 此处的分配需要参考 App_StressDetector_Init() 对数据长度的需求!
//	waveSel.buf			= MatrixMother;
//	waveSel.buflen		= STRESS_SEL_WAVE_LEN;
//	
//	waveAcq.buf 		= MatrixMother + 4096;
//	waveAcq.buflen		= STRESS_ACQ_WAVE_LEN;
//	
//	waveCtrst.buf		= bigbuf1;
//	waveCtrst.buflen	= STRESS_CTRST_WAVE_LEN;

//	waveRtime.buf		= bigbuf1 + 4096;
//	waveRtime.buflen	= STRESS_RTIME_WAVE_LEN;
}

void Dev_SetFunction(uint8_t func) {
	if(func == FUNC_THICKNESS_LONG)
		g_Dev.functionMode = FUNC_THICKNESS_LONG;
	else
		g_Dev.functionMode = FUNC_THICKNESS_COMMON;

	if(DEV_FUNC_SUPPORT_LONG == 0) {
		if(func == FUNC_THICKNESS_LONG) {
			g_Dev.functionMode = FUNC_THICKNESS_COMMON;
		}
	}
	
	g_Thk.longMode = (g_Dev.functionMode == FUNC_THICKNESS_LONG);
}



/**
  * @brief  APP主任务
  * @param  None
  * @retval None
  */
void App_MainTask(void)
{
	GUI_Init();
	UiDrawWave_Init();
	App_Init();
//	AppVerify_MainTask();
//	DismantlingCheckTask(); //拆机检测
	RestoreToFactoryCheck(); //恢复出厂设置按键检测
	
	//Wifi状态
	if(g_Dev.wifiState == 0) CloseWifi();
	else OpenWifi();

	App_Thk_Init();
	
	if((g_Dev.functionMode == FUNC_THICKNESS_COMMON) || (g_Dev.functionMode == FUNC_THICKNESS_LONG))
		Thk_DispMainUI();
	
	while(1) {
		if((g_Dev.functionMode == FUNC_THICKNESS_COMMON) || (g_Dev.functionMode == FUNC_THICKNESS_LONG)) {
			g_Emat.Emit_Rep = 3;
			App_Thickness_MainTask();
		}
	}
}

/**
  * @brief  app参数初始化
  * @param  None
  * @retval None
  */
void App_Init(void)
{
	g_Dev.functionMode = FUNC_THICKNESS_COMMON;
//	g_Dev.functionMode = FUNC_STRESS_DETECTOR;
	GlobalMemoryManage();
	
	strcpy(g_Dev.deviceSN, "PREMAT3#0000");//"PREMAT3#0173";"PREMAT3#vip0";//
	g_Thk.usrSaveVelocity_1 = 3100;
	g_Thk.usrSaveVelocity_2 = 3200;
	g_Thk.usrSaveVelocity_3 = 3300;
	
	g_Daq.AGC_en					= 1;
	g_Emat.Emit_Switch				= 0;
	g_Emat.US_avgTimes_old 			= 32;
	g_Emat.fe_MHz 					= 3.85;
	g_Emat.Emit_Rep 				= 3;
	g_Emat.xcorr_en					= 0;
	g_Emat.gain_x10 				= (GAINx10_MAX + GAINx10_MIN)/2;
	Emat_SetFs(50);
	
	g_Dev.firmWareVersion			= FIRMWARE_VERSION;
	g_Dev.controlByApp				= 0;//控制权归属 0:自身；1:APP
	g_Dev.wifiState					= 0;
	g_Dev.allowOledShow 			= 1;
	g_Dev.keepConnecttedWDG_Action 	= 0;
	
	g_Dev.hmi_level 				= 0;
	g_Dev.hmi_menu_id 				= 0; 
	
	DataPersistence_ReadAllDatas();
	OLED_Brightness(g_Dev.brightness_val);
}


static uint8_t ModifyDevSn(void) {
	char str[64];
	char *p, needRefresh = 1;
	uint8_t keyId, cursorPos = 9;
	
	memset(str, 0x00, sizeof(str));
	strcpy(str, g_Dev.deviceSN);
	
	if(str[ 8] < '0' || str[ 8] > '9') str[ 8] = '0';
	if(str[ 9] < '0' || str[ 9] > '9') str[ 9] = '0';
	if(str[10] < '0' || str[10] > '9') str[10] = '0';
	if(str[11] < '0' || str[11] > '9') str[11] = '0';

	while(1) {
		keyId = KEY_Scan(0);
		if(keyId == KEY_SET_LONG_PRES) {
			//保存SN
			strcpy(g_Dev.deviceSN, str);
			DataPers_WriteSnCode(g_Dev.deviceSN);
			ClearLED(0x00);
			return 0;
		}
		
		if(keyId == KEY_SET_PRES) {
			cursorPos = (cursorPos < 12) ? (cursorPos + 1) : 9; 
			needRefresh = 1;
		}
		else if(keyId == KEY_UP_PRES) {
			p = str + cursorPos-1;
			*p = (*p < '9') ? (*p + 1) : '0';
			needRefresh = 1;
		}
		else if(keyId == KEY_DOWN_PRES) {
			p = str + cursorPos-1;
			*p = (*p > '0') ? (*p - 1) : '9';
			needRefresh = 1;
		}
		
		if(needRefresh) {
			OLED_DispSetting_withUnderline(1, str, 10, cursorPos);
			needRefresh = 0;
		}
	}
}

//恢复出厂设置
static uint8_t RestoreToFactoryCheck(void) {
	
	if(KEY_DOWN == 0) {
		delay_ms(100);
		if(KEY_DOWN == 0){
			
			Oled_ShowRestoreFactory();
			while(1){
				if(KEY_SET == 0){
					delay_ms(10);
					if(KEY_SET == 0){
						break;
					}
				}
				if(KEY_UP == 0){
					Oled_ShowRestoreFactory_Cancel();
					delay_ms(1000);
					return 1;
				}
				else if(KEY_DOWN == 0){ //修改SN码, 长按3S下键
					delay_ms(1000);
					delay_ms(1000);
					delay_ms(1000);
					if(KEY_DOWN == 0) {
						//设置SN码
						ModifyDevSn();
						return 1;
					}
				}
			}
			Oled_ShowRestoreFactory_Resetting();
			g_Thk.ultraSonic_Velocity = 3240;
			g_Dev.brightness_val = 30;
			g_Dev.lang_type = 1;
			g_Dev.unit_type = 0;
			g_Dev.wifiState = 1;
			g_Thk.caliThicknessVal = 10;
			g_Thk.usrSaveVelocity_1 = 1000;
			g_Thk.usrSaveVelocity_2 = 1000;
			g_Thk.usrSaveVelocity_3 = 1000;
			g_Thk.pre_velc_id = 0;
			g_Emat.US_avgTimes = 256;
			g_Thk.referenceBitOn = 0;
			Zpt_SetZptTimeNs(Zpt_GetFactoryZptTimeNs());//
			g_Emat.Emit_AB_phase    = 0;
			g_Emat.Emit_brakePulNum = 0;
			
			DataPersistence_SaveAllDatas(1);
			
			delay_ms(1000);
			Oled_ShowRestoreFactory_ResetOk();
			DataPersistence_ReadAllDatas();
			setESP8266_Ssid_Pwd();
			delay_ms(200);
			DataPersistence_ReadAllDatas();
		}
		else {
			return 0;
		}
	}
}


//double UnitConvert_mm2inch(double mm)
//{
//	return (mm / 25.4);
//}

//double UnitConvert_inch2mm(double inch)
//{
//	return (inch * 25.4);
//}

/******************************* end of file **********************************/
