/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : app.c                                    // 文件名：应用程序主文件
  * @project :                                          // 项目名称（未填写）
  * @brief   :                                          // 简要描述（未填写）
  * <AUTHOR> PR Team                                  // 作者：PR团队
  * @since   : 2024/07/29                               // 创建日期
  * @version : V1.0                                     // 版本号
  * @history :                                          // 版本历史记录
  *	2024/07/29: V0.1                                   // 首个版本
  *	  1) 首次创建;                                     // 版本更新说明
  *
  *
  ********************************************************************************
  * @note                                               // 注意事项（未填写）
  *
  ********************************************************************************
  * @attention                                          // 特别注意（未填写）
  *
  ********************************************************************************
  */

#include "app.h"                    // 包含应用程序主头文件
#include <string.h>                 // 包含字符串操作函数库
#include <stdio.h>                  // 包含标准输入输出函数库
#include "delay.h"                  // 包含延时函数头文件
#include "wifi.h"                   // 包含WiFi功能头文件
#include "thk.h"                    // 包含测厚功能头文件
#include "app_thickness.h"          // 包含厚度测量应用头文件
#include "app_stressdetector.h"     // 包含应力检测应用头文件
#include "dac.h"                    // 包含数模转换器头文件
#include "emat.h"                   // 包含电磁超声换能器头文件
#include "key.h"                    // 包含按键处理头文件
#include "oled.h"                   // 包含OLED显示屏头文件
#include "data_persistence.h"       // 包含数据持久化头文件
#include "app_version.h"            // 包含应用版本信息头文件
#include "bsp_zpt.h"                // 包含零点时间板级支持包头文件
#include "app_verify.h"             // 包含应用验证头文件（重复包含）
#include "gui_lib.h"                // 包含图形用户界面库头文件
#include "ui_draw_wave.h"           // 包含波形绘制界面头文件
#include "app_verify.h"             // 包含应用验证头文件（重复包含）

DEV_T	g_Dev;                      // 全局设备结构体变量，存储设备状态和配置
EMAT_T	g_Emat;                     // 全局电磁超声换能器结构体变量，存储EMAT相关参数
DAQ_T 	g_Daq;                      // 全局数据采集结构体变量，存储数据采集相关参数

static int16_t bigbuf1[4096 * 2];          // 静态大缓冲区，用于存储波形数据，大小为8192个16位整数

static uint8_t RestoreToFactoryCheck(void); // 恢复出厂设置检查函数的前向声明

/**
  * @brief  全局内存管理,用以调配全局大数组          // 函数功能：管理全局内存分配
  * @param  None                                    // 参数：无
  * @retval None                                    // 返回值：无
  */
static void GlobalMemoryManage(void) {              // 静态函数，用于全局内存管理
	//测厚部分, 此处的分配需要参考 App_Thk_Init() 对数据长度的需求!  // 注释：测厚功能的内存分配
	g_Thk.wave.waveValid	= 0;                    // 设置普通波形数据有效标志为无效
	g_Thk.wave.buf			= bigbuf1;              // 将普通波形缓冲区指向bigbuf1
	g_Thk.wave.buflen		= THK_WAVE_LEN;         // 设置普通波形缓冲区长度

	g_Thk.longWave.waveValid	= 0;                // 设置长波形数据有效标志为无效
	g_Thk.longWave.buf 		= MatrixMother;         // 将长波形缓冲区指向MatrixMother数组
	g_Thk.longWave.buflen	= THK_LONG_WAVE_LEN;    // 设置长波形缓冲区长度

	//轴力部分, 此处的分配需要参考 App_StressDetector_Init() 对数据长度的需求!  // 注释：应力检测功能的内存分配（已注释掉）
//	waveSel.buf			= MatrixMother;             // 选择波形缓冲区指向MatrixMother（已注释）
//	waveSel.buflen		= STRESS_SEL_WAVE_LEN;      // 选择波形缓冲区长度（已注释）
//	                                                // 空行（已注释）
//	waveAcq.buf 		= MatrixMother + 4096;      // 采集波形缓冲区指向MatrixMother偏移4096位置（已注释）
//	waveAcq.buflen		= STRESS_ACQ_WAVE_LEN;      // 采集波形缓冲区长度（已注释）
//	                                                // 空行（已注释）
//	waveCtrst.buf		= bigbuf1;                  // 对比波形缓冲区指向bigbuf1（已注释）
//	waveCtrst.buflen	= STRESS_CTRST_WAVE_LEN;    // 对比波形缓冲区长度（已注释）

//	waveRtime.buf		= bigbuf1 + 4096;           // 实时波形缓冲区指向bigbuf1偏移4096位置（已注释）
//	waveRtime.buflen	= STRESS_RTIME_WAVE_LEN;    // 实时波形缓冲区长度（已注释）
}

void Dev_SetFunction(uint8_t func) {                   // 设备功能设置函数，参数为功能类型
	if(func == FUNC_THICKNESS_LONG)                    // 如果传入的功能是长距离测厚模式
		g_Dev.functionMode = FUNC_THICKNESS_LONG;      // 设置设备功能模式为长距离测厚
	else                                               // 否则
		g_Dev.functionMode = FUNC_THICKNESS_COMMON;    // 设置设备功能模式为普通测厚

	if(DEV_FUNC_SUPPORT_LONG == 0) {                   // 如果设备不支持长距离测厚功能
		if(func == FUNC_THICKNESS_LONG) {              // 但是请求的是长距离测厚模式
			g_Dev.functionMode = FUNC_THICKNESS_COMMON; // 强制设置为普通测厚模式
		}
	}

	g_Thk.longMode = (g_Dev.functionMode == FUNC_THICKNESS_LONG); // 根据设备功能模式设置测厚长模式标志
}



/**
  * @brief  APP主任务                                 // 函数功能：应用程序主任务入口
  * @param  None                                      // 参数：无
  * @retval None                                      // 返回值：无
  */
void App_MainTask(void)                              // 应用程序主任务函数
{
	GUI_Init();                                      // 初始化图形用户界面
	UiDrawWave_Init();                               // 初始化波形绘制界面
	App_Init();                                      // 初始化应用程序参数
//	AppVerify_MainTask();                            // 应用验证主任务（已注释）
//	DismantlingCheckTask(); //拆机检测               // 拆机检测任务（已注释）
	RestoreToFactoryCheck(); //恢复出厂设置按键检测   // 检查是否需要恢复出厂设置

	//Wifi状态                                        // WiFi状态控制
	if(g_Dev.wifiState == 0) CloseWifi();           // 如果WiFi状态为关闭，则关闭WiFi
	else OpenWifi();                                 // 否则打开WiFi

	App_Thk_Init();                                  // 初始化测厚应用

	if((g_Dev.functionMode == FUNC_THICKNESS_COMMON) || (g_Dev.functionMode == FUNC_THICKNESS_LONG)) // 如果设备功能模式是普通测厚或长距离测厚
		Thk_DispMainUI();                            // 显示测厚主界面

	while(1) {                                       // 主循环，程序永远运行
		if((g_Dev.functionMode == FUNC_THICKNESS_COMMON) || (g_Dev.functionMode == FUNC_THICKNESS_LONG)) { // 如果是测厚模式
			g_Emat.Emit_Rep = 3;                     // 设置EMAT发射重复次数为3
			App_Thickness_MainTask();                // 执行测厚应用主任务
		}
	}
}

/**
  * @brief  app参数初始化                            // 函数功能：应用程序参数初始化
  * @param  None                                      // 参数：无
  * @retval None                                      // 返回值：无
  */
void App_Init(void)                                  // 应用程序初始化函数
{
	g_Dev.functionMode = FUNC_THICKNESS_COMMON;      // 设置设备功能模式为普通测厚
//	g_Dev.functionMode = FUNC_STRESS_DETECTOR;       // 设置设备功能模式为应力检测（已注释）
	GlobalMemoryManage();                            // 调用全局内存管理函数

	strcpy(g_Dev.deviceSN, "PREMAT3#0000");//"PREMAT3#0173";"PREMAT3#vip0";// // 设置设备序列号，注释中显示了其他可选序列号
	g_Thk.usrSaveVelocity_1 = 3100;                 // 设置用户保存的声速1为3100 m/s
	g_Thk.usrSaveVelocity_2 = 3200;                 // 设置用户保存的声速2为3200 m/s
	g_Thk.usrSaveVelocity_3 = 3300;                 // 设置用户保存的声速3为3300 m/s

	g_Daq.AGC_en					= 1;             // 启用自动增益控制（AGC）
	g_Emat.Emit_Switch				= 0;             // 设置EMAT发射开关为关闭
	g_Emat.US_avgTimes_old 			= 32;            // 设置超声平均次数（旧值）为32
	g_Emat.fe_MHz 					= 3.85;          // 设置EMAT激励频率为3.85MHz
	g_Emat.Emit_Rep 				= 3;             // 设置EMAT发射重复次数为3
	g_Emat.xcorr_en					= 0;             // 禁用互相关功能
	g_Emat.gain_x10 				= (GAINx10_MAX + GAINx10_MIN)/2; // 设置增益为最大值和最小值的中间值
	Emat_SetFs(50);                                  // 设置EMAT采样频率为50MHz

	g_Dev.firmWareVersion			= FIRMWARE_VERSION;  // 设置固件版本号
	g_Dev.controlByApp				= 0;//控制权归属 0:自身；1:APP  // 设置控制权归属，0表示设备自身控制，1表示APP控制
	g_Dev.wifiState					= 0;             // 设置WiFi状态为关闭
	g_Dev.allowOledShow 			= 1;             // 允许OLED显示
	g_Dev.keepConnecttedWDG_Action 	= 0;             // 设置保持连接看门狗动作为0

	g_Dev.hmi_level 				= 0;             // 设置人机界面层级为0
	g_Dev.hmi_menu_id 				= 0;             // 设置人机界面菜单ID为0

	DataPersistence_ReadAllDatas();                  // 从持久化存储中读取所有数据
	OLED_Brightness(g_Dev.brightness_val);           // 根据设备亮度值设置OLED亮度
}


static uint8_t ModifyDevSn(void) {                     // 修改设备序列号的静态函数
	char str[64];                                   // 定义64字节的字符串缓冲区
	char *p, needRefresh = 1;                       // 定义字符指针p和刷新标志needRefresh，初始化为1
	uint8_t keyId, cursorPos = 9;                   // 定义按键ID和光标位置，光标初始位置为9

	memset(str, 0x00, sizeof(str));                 // 将字符串缓冲区清零
	strcpy(str, g_Dev.deviceSN);                    // 将当前设备序列号复制到缓冲区

	if(str[ 8] < '0' || str[ 8] > '9') str[ 8] = '0'; // 检查第8位字符是否为数字，不是则设为'0'
	if(str[ 9] < '0' || str[ 9] > '9') str[ 9] = '0'; // 检查第9位字符是否为数字，不是则设为'0'
	if(str[10] < '0' || str[10] > '9') str[10] = '0'; // 检查第10位字符是否为数字，不是则设为'0'
	if(str[11] < '0' || str[11] > '9') str[11] = '0'; // 检查第11位字符是否为数字，不是则设为'0'

	while(1) {                                      // 无限循环，等待用户操作
		keyId = KEY_Scan(0);                        // 扫描按键状态
		if(keyId == KEY_SET_LONG_PRES) {            // 如果检测到SET键长按
			//保存SN                                 // 注释：保存序列号
			strcpy(g_Dev.deviceSN, str);            // 将修改后的序列号复制到设备序列号变量
			DataPers_WriteSnCode(g_Dev.deviceSN);   // 将序列号写入持久化存储
			ClearLED(0x00);                         // 清除LED显示
			return 0;                               // 返回0，表示成功完成
		}

		if(keyId == KEY_SET_PRES) {                 // 如果检测到SET键短按
			cursorPos = (cursorPos < 12) ? (cursorPos + 1) : 9; // 光标位置向右移动，超过12则回到9
			needRefresh = 1;                        // 设置需要刷新标志
		}
		else if(keyId == KEY_UP_PRES) {             // 如果检测到UP键按下
			p = str + cursorPos-1;                  // 指针指向当前光标位置的字符
			*p = (*p < '9') ? (*p + 1) : '0';       // 字符值加1，如果超过'9'则回到'0'
			needRefresh = 1;                        // 设置需要刷新标志
		}
		else if(keyId == KEY_DOWN_PRES) {           // 如果检测到DOWN键按下
			p = str + cursorPos-1;                  // 指针指向当前光标位置的字符
			*p = (*p > '0') ? (*p - 1) : '9';       // 字符值减1，如果小于'0'则回到'9'
			needRefresh = 1;                        // 设置需要刷新标志
		}

		if(needRefresh) {                           // 如果需要刷新显示
			OLED_DispSetting_withUnderline(1, str, 10, cursorPos); // 在OLED上显示带下划线的设置界面
			needRefresh = 0;                        // 清除刷新标志
		}
	}
}

//恢复出厂设置                                         // 注释：恢复出厂设置功能
static uint8_t RestoreToFactoryCheck(void) {           // 恢复出厂设置检查的静态函数

	if(KEY_DOWN == 0) {                                 // 如果DOWN键被按下（低电平有效）
		delay_ms(100);                                  // 延时100毫秒，消除按键抖动
		if(KEY_DOWN == 0){                              // 再次检查DOWN键是否仍被按下

			Oled_ShowRestoreFactory();                  // 在OLED上显示恢复出厂设置界面
			while(1){                                   // 无限循环，等待用户选择
				if(KEY_SET == 0){                       // 如果SET键被按下
					delay_ms(10);                       // 延时10毫秒
					if(KEY_SET == 0){                   // 再次确认SET键被按下
						break;                          // 跳出循环，继续执行恢复出厂设置
					}
				}
				if(KEY_UP == 0){                        // 如果UP键被按下
					Oled_ShowRestoreFactory_Cancel();   // 显示取消恢复出厂设置界面
					delay_ms(1000);                     // 延时1秒
					return 1;                           // 返回1，表示取消操作
				}
				else if(KEY_DOWN == 0){ //修改SN码, 长按3S下键  // 如果DOWN键被按下（用于修改序列号，需长按3秒）
					delay_ms(1000);                     // 延时1秒
					delay_ms(1000);                     // 延时1秒
					delay_ms(1000);                     // 延时1秒（总共3秒）
					if(KEY_DOWN == 0) {                 // 如果DOWN键仍被按下
						//设置SN码                       // 注释：设置序列号
						ModifyDevSn();                  // 调用修改设备序列号函数
						return 1;                       // 返回1，表示完成序列号修改
					}
				}
			}
			Oled_ShowRestoreFactory_Resetting();       // 显示正在重置界面
			g_Thk.ultraSonic_Velocity = 3240;          // 设置超声波速度为3240 m/s（出厂默认值）
			g_Dev.brightness_val = 30;                  // 设置屏幕亮度为30（出厂默认值）
			g_Dev.lang_type = 1;                        // 设置语言类型为1（出厂默认值）
			g_Dev.unit_type = 0;                        // 设置单位类型为0（出厂默认值）
			g_Dev.wifiState = 1;                        // 设置WiFi状态为开启（出厂默认值）
			g_Thk.caliThicknessVal = 10;                // 设置校准厚度值为10（出厂默认值）
			g_Thk.usrSaveVelocity_1 = 1000;            // 设置用户保存声速1为1000 m/s（出厂默认值）
			g_Thk.usrSaveVelocity_2 = 1000;            // 设置用户保存声速2为1000 m/s（出厂默认值）
			g_Thk.usrSaveVelocity_3 = 1000;            // 设置用户保存声速3为1000 m/s（出厂默认值）
			g_Thk.pre_velc_id = 0;                      // 设置预设声速ID为0（出厂默认值）
			g_Emat.US_avgTimes = 256;                   // 设置超声平均次数为256（出厂默认值）
			g_Thk.referenceBitOn = 0;                   // 设置参考位开关为关闭（出厂默认值）
			Zpt_SetZptTimeNs(Zpt_GetFactoryZptTimeNs());//  // 设置零点时间为出厂默认值
			g_Emat.Emit_AB_phase    = 0;                // 设置EMAT发射AB相位为0（出厂默认值）
			g_Emat.Emit_brakePulNum = 0;                // 设置EMAT发射制动脉冲数为0（出厂默认值）

			DataPersistence_SaveAllDatas(1);           // 保存所有数据到持久化存储，参数1表示强制保存

			delay_ms(1000);                             // 延时1秒
			Oled_ShowRestoreFactory_ResetOk();          // 显示重置完成界面
			DataPersistence_ReadAllDatas();             // 从持久化存储读取所有数据
			setESP8266_Ssid_Pwd();                      // 设置ESP8266的SSID和密码
			delay_ms(200);                              // 延时200毫秒
			DataPersistence_ReadAllDatas();             // 再次从持久化存储读取所有数据
		}
		else {                                          // 如果DOWN键没有持续按下
			return 0;                                   // 返回0，表示没有执行恢复出厂设置
		}
	}
}


//double UnitConvert_mm2inch(double mm)              // 毫米转英寸的单位转换函数（已注释）
//{                                                  // 函数开始
//	return (mm / 25.4);                             // 返回毫米除以25.4得到英寸值（1英寸=25.4毫米）
//}                                                  // 函数结束

//double UnitConvert_inch2mm(double inch)           // 英寸转毫米的单位转换函数（已注释）
//{                                                  // 函数开始
//	return (inch * 25.4);                           // 返回英寸乘以25.4得到毫米值
//}                                                  // 函数结束

/******************************* end of file **********************************/ // 文件结束标记
