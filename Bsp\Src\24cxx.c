#include "24cxx.h"
#include "delay.h"
#include "stm32f7xx_hal.h"

//IIC所有操作函数
//IO方向设置
#define AT_SDA_IN()  {GPIOC->MODER &= ~(3<<(9*2)); GPIOC-><PERSON><PERSON><PERSON> |= 0<<(9*2);}
#define AT_SDA_OUT() {GPIOC->MODER &= ~(3<<(9*2)); GPIOC->MODER |= 1<<(9*2);}
//IO操作
#define AT_IIC_SCL(n)  HAL_GPIO_WritePin(GPIOA, GPIO_PIN_8, n ? GPIO_PIN_SET : GPIO_PIN_RESET) //SCL
#define AT_IIC_SDA(n)  HAL_GPIO_WritePin(GPIOC, GPIO_PIN_9, n ? GPIO_PIN_SET : GPIO_PIN_RESET) //SDA
#define AT_READ_SDA    HAL_GPIO_ReadPin(GPIOC, GPIO_PIN_9)

static void AT_IIC_Init(void);			 
static void AT_IIC_Start(void);
static void AT_IIC_Stop(void);
static void AT_IIC_Send_Byte(uint8_t txd);
static uint8_t AT_IIC_Read_Byte(unsigned char ack);
static uint8_t AT_IIC_Wait_Ack(void);
static void AT_IIC_Ack(void);
static void AT_IIC_NAck(void);
static void AT_IIC_Write_One_Byte(uint8_t daddr,uint8_t addr,uint8_t data);
static uint8_t AT_IIC_Read_One_Byte(uint8_t daddr,uint8_t addr);	

//uint8_t test_rd[10];
//uint8_t test_wr[10]={1,2,3,4,5,6,7,8,9,10};
//int32_t tst_4B=0;

//uint8_t tst_readBuf[256];
//uint8_t tst_writeBuf[256];
//初始化IIC接口
void AT24CXX_Init(void)
{
	AT_IIC_Init();//IIC初始化
	
//	for(uint32_t i = 0; i < 256; i++) {
//		AT24CXX_WriteOneByte(i, i);
//		delay_ms(1);
//	}
//	
//	for(uint32_t i = 0; i < 256; i++) {
//		tst_readBuf[i] = AT24CXX_ReadOneByte(i);
//		delay_ms(1);
//	}

}

//在AT24CXX指定地址读出一个数据
//ReadAddr:开始读数的地址  
//返回值  :读到的数据
uint8_t AT24CXX_ReadOneByte(uint16_t ReadAddr)
{
	uint8_t temp=0;		  	    																 
	AT_IIC_Start();  
	if(EE_TYPE>AT24C16)
	{
		AT_IIC_Send_Byte(0XA0);	   //发送写命令
		AT_IIC_Wait_Ack();
		AT_IIC_Send_Byte(ReadAddr>>8);//发送高地址	    
	}else AT_IIC_Send_Byte(0XA0+((ReadAddr/256)<<1));   //发送器件地址0XA0,写数据 	   
	AT_IIC_Wait_Ack(); 
	AT_IIC_Send_Byte(ReadAddr%256);   //发送低地址
	AT_IIC_Wait_Ack();	    
	AT_IIC_Start();  	 	   
	AT_IIC_Send_Byte(0XA1);           //进入接收模式			   
	AT_IIC_Wait_Ack();	 
	temp=AT_IIC_Read_Byte(0);		   
	AT_IIC_Stop();//产生一个停止条件	    
	return temp;
}
//在AT24CXX指定地址写入一个数据
//WriteAddr  :写入数据的目的地址    
//DataToWrite:要写入的数据
void AT24CXX_WriteOneByte(uint16_t WriteAddr,uint8_t DataToWrite)
{				   	  	    																 
	AT_IIC_Start();  
	if(EE_TYPE>AT24C16)
	{
		AT_IIC_Send_Byte(0XA0);	    //发送写命令
		AT_IIC_Wait_Ack();
		AT_IIC_Send_Byte(WriteAddr>>8);//发送高地址	  
	}else AT_IIC_Send_Byte(0XA0+((WriteAddr/256)<<1));   //发送器件地址0XA0,写数据 	 
	AT_IIC_Wait_Ack();	   
	AT_IIC_Send_Byte(WriteAddr%256);   //发送低地址
	AT_IIC_Wait_Ack(); 	 										  		   
	AT_IIC_Send_Byte(DataToWrite);     //发送字节							   
	AT_IIC_Wait_Ack();  		    	   
	AT_IIC_Stop();//产生一个停止条件 
	delay_ms(5);	 
}
//在AT24CXX里面的指定地址开始写入长度为Len的数据
//该函数用于写入16bit或者32bit的数据.
//WriteAddr  :开始写入的地址  
//DataToWrite:数据数组首地址
//Len        :要写入数据的长度2,4
void AT24CXX_WriteLenByte(uint16_t WriteAddr,uint32_t DataToWrite,uint8_t Len)
{
	uint8_t t;
	for(t=0;t<Len;t++)
	{
		AT24CXX_WriteOneByte(WriteAddr+t,(DataToWrite>>(8*t))&0xff);
	}												    
}

//在AT24CXX里面的指定地址开始读出长度为Len的数据
//该函数用于读出16bit或者32bit的数据.
//ReadAddr   :开始读出的地址 
//返回值     :数据
//Len        :要读出数据的长度2,4
uint32_t AT24CXX_ReadLenByte(uint16_t ReadAddr,uint8_t Len)
{  	
	uint8_t t;
	uint32_t temp=0;
	for(t=0;t<Len;t++)
	{
		temp<<=8;
		temp+=AT24CXX_ReadOneByte(ReadAddr+Len-t-1); 	 				   
	}
	return temp;												    
}
//检查AT24CXX是否正常
//这里用了24XX的最后一个地址(255)来存储标志字.
//如果用其他24C系列,这个地址要修改
//返回1:检测失败
//返回0:检测成功
//uint8_t AT24CXX_Check(void)
//{
//	uint8_t temp;
//	temp=AT24CXX_ReadOneByte(255);//避免每次开机都写AT24CXX			   
//	if(temp==0X55)return 0;		   
//	else//排除第一次初始化的情况
//	{
//		AT24CXX_WriteOneByte(255,0X55);
//	    temp=AT24CXX_ReadOneByte(255);	  
//		if(temp==0X55)return 0;
//	}
//	return 1;											  
//}

//在AT24CXX里面的指定地址开始读出指定个数的数据
//ReadAddr :开始读出的地址 对24c02为0~255
//pBuffer  :数据数组首地址
//NumToRead:要读出数据的个数
void AT24CXX_Read(uint16_t ReadAddr,uint8_t *pBuffer,uint16_t NumToRead)
{
	while(NumToRead)
	{
		*pBuffer++=AT24CXX_ReadOneByte(ReadAddr++);	
		NumToRead--;
	}
}  
//在AT24CXX里面的指定地址开始写入指定个数的数据
//WriteAddr :开始写入的地址 对24c02为0~255
//pBuffer   :数据数组首地址
//NumToWrite:要写入数据的个数
void AT24CXX_Write(uint16_t WriteAddr,uint8_t *pBuffer,uint16_t NumToWrite)
{
	while(NumToWrite--)
	{
		AT24CXX_WriteOneByte(WriteAddr,*pBuffer);
		WriteAddr++;
		pBuffer++;
	}
}

//IIC初始化
static void AT_IIC_Init(void)
{
    GPIO_InitTypeDef GPIO_Initure = {0};
    
    __HAL_RCC_GPIOA_CLK_ENABLE();
	__HAL_RCC_GPIOC_CLK_ENABLE();
    
    GPIO_Initure.Pin = GPIO_PIN_8;
    GPIO_Initure.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_Initure.Pull = GPIO_PULLUP;
    GPIO_Initure.Speed = GPIO_SPEED_HIGH;
    HAL_GPIO_Init(GPIOA, &GPIO_Initure);
	
	GPIO_Initure.Pin=GPIO_PIN_9;
	HAL_GPIO_Init(GPIOC, &GPIO_Initure);
    
    AT_IIC_SDA(1);
    AT_IIC_SCL(1);  
}

//产生IIC起始信号
static void AT_IIC_Start(void)
{
	AT_SDA_OUT();     //sda线输出
	AT_IIC_SDA(1);	  	  
	AT_IIC_SCL(1);
	delay_us(4);
 	AT_IIC_SDA(0);//START:when CLK is high,DATA change form high to low 
	delay_us(4);
	AT_IIC_SCL(0);//钳住I2C总线，准备发送或接收数据 
	delay_us(2); 
}	  
//产生IIC停止信号
static void AT_IIC_Stop(void)
{
	AT_SDA_OUT();//sda线输出
	AT_IIC_SCL(0);
	AT_IIC_SDA(0);//STOP:when CLK is high DATA change form low to high
 	delay_us(4);
	AT_IIC_SCL(1); 
	delay_us(1);
	AT_IIC_SDA(1);//发送I2C总线结束信号
	delay_us(4);							   	
}
//等待应答信号到来
//返回值：1，接收应答失败
//        0，接收应答成功
static uint8_t AT_IIC_Wait_Ack(void)
{
	uint8_t ucErrTime=0;
	AT_SDA_IN();      //SDA设置为输入  
	AT_IIC_SDA(1);delay_us(1);	   
	AT_IIC_SCL(1);delay_us(1);	 
	while(AT_READ_SDA)
	{
		ucErrTime++;
		if(ucErrTime>250)
		{
			AT_IIC_Stop();
			return 1;
		}
	}
	AT_IIC_SCL(0);//时钟输出0
	delay_us(2); 
	return 0;  
} 
//产生ACK应答
static void AT_IIC_Ack(void)
{
	AT_IIC_SCL(0);
	AT_SDA_OUT();
	AT_IIC_SDA(0);
	delay_us(2);
	AT_IIC_SCL(1);
	delay_us(2);
	AT_IIC_SCL(0);
	delay_us(2); 
}
//不产生ACK应答		    
static void AT_IIC_NAck(void)
{
	AT_IIC_SCL(0);
	AT_SDA_OUT();
	AT_IIC_SDA(1);
	delay_us(2);
	AT_IIC_SCL(1);
	delay_us(2);
	AT_IIC_SCL(0);
	delay_us(2); 
}					 				     
//IIC发送一个字节
//返回从机有无应答
//1，有应答
//0，无应答			  
static void AT_IIC_Send_Byte(uint8_t txd)
{                        
    uint8_t t;   
	AT_SDA_OUT(); 	    
    AT_IIC_SCL(0);//拉低时钟开始数据传输
    for(t=0;t<8;t++)
    {              
        AT_IIC_SDA((txd&0x80)>>7);
        txd<<=1; 	  
		delay_us(2);   //对TEA5767这三个延时都是必须的
		AT_IIC_SCL(1);
		delay_us(2); 
		AT_IIC_SCL(0);	
		delay_us(2);
    }	 
} 	    
//读1个字节，ack=1时，发送ACK，ack=0，发送nACK   
static uint8_t AT_IIC_Read_Byte(unsigned char ack)
{
	unsigned char i,receive=0;
	AT_SDA_IN();//SDA设置为输入
    for(i=0;i<8;i++ )
	{
        AT_IIC_SCL(0); 
        delay_us(2);
		AT_IIC_SCL(1);
        receive<<=1;
        if(AT_READ_SDA)receive++;   
		delay_us(1); 
    }					 
    if (!ack)
        AT_IIC_NAck();//发送nACK
    else
        AT_IIC_Ack(); //发送ACK   
    return receive;
}

static float U8ArrayToF32(char *buf) {
	return *((float *)buf);
}

static void F32ToU8Array(char buf[4], float f) {
	buf[0] = ((char *)&f)[0];
	buf[1] = ((char *)&f)[1];
	buf[2] = ((char *)&f)[2];
	buf[3] = ((char *)&f)[3];
}

//void AT24CXX_Write4Bytes(uint16_t WriteAddr,int32_t DataToWrite)
//{
//	uint8_t t;
//	for(t=0;t<4;t++)
//	{
//		AT24CXX_WriteOneByte(WriteAddr+t,(DataToWrite>>(8*t))&0xff);
//	}												    
//}
//int32_t AT24CXX_Read4Bytes(uint16_t ReadAddr)
//{
//	uint8_t t;
//	int32_t temp=0;
//	for(t=0;t<4;t++)
//	{
//		temp<<=8;
//		temp+=AT24CXX_ReadOneByte(ReadAddr+4-t-1); 	 				   
//	}
//	return temp;												    
//}

static int32_t AT24CXX_Read4Bytes(uint16_t ReadAddr)
{
	uint8_t t;
	int32_t temp=0;
	for(t=0;t<4;t++)
	{
		temp<<=8;
		temp+=AT24CXX_ReadOneByte(ReadAddr+4-t-1); 	 				   
	}
	return temp;												    
}

uint8_t AT24CXX_Read4BytesChecked(uint16_t addr, uint32_t *retVal)
{
	uint8_t trycnt = 7;
	int32_t tmp1, tmp2;
	
	do {
		tmp1 = AT24CXX_Read4Bytes(addr);
		tmp2 = AT24CXX_Read4Bytes(addr);
		if(tmp1 == tmp2) {
			*retVal = tmp1;
			return 0;
		}
		trycnt--;
	} while (trycnt > 0);
	
	return 1;
}

static void AT24CXX_Write4Bytes(uint16_t WriteAddr, uint32_t DataToWrite)
{
	uint8_t t;
	for(t=0;t<4;t++)
	{
		AT24CXX_WriteOneByte(WriteAddr+t,(DataToWrite>>(8*t))&0xff);
	}
}

uint8_t AT24CXX_Write4BytesChecked(uint16_t addr, uint32_t data)
{
	uint8_t trycnt = 3;
	
	do {
		if(AT24CXX_Read4Bytes(addr) == data) {
			return 0; //ok
		}
		AT24CXX_Write4Bytes(addr, data);
		trycnt--;
	} while(trycnt > 0);
	return 1; //failed
}


static float AT24CXX_ReadFloat32(uint16_t ReadAddr)
{
	uint8_t t;
	uint8_t buf[4]; 
	float tmp=0;
	for(t=0;t<4;t++)
	{
		buf[3-t] = AT24CXX_ReadOneByte(ReadAddr+4-t-1); 	 				   
	}
	tmp = U8ArrayToF32((char*)buf);
	return tmp;
}

uint8_t AT24CXX_ReadFloat32Checked(uint16_t addr, float *retVal)
{
	uint8_t trycnt = 7;
	float tmp1, tmp2;
	
	do {
		tmp1 = AT24CXX_ReadFloat32(addr);
		tmp2 = AT24CXX_ReadFloat32(addr);
		if(tmp1 == tmp2) {
			*retVal = tmp1;
			return 0; //ok
		}
		trycnt--;
	} while (trycnt > 0);
	
	return 1; //failed
}

static void AT24CXX_WriteFloat32(uint16_t WriteAddr,float DataToWrite)
{
	uint8_t t;
	uint8_t buf[4]; 

	F32ToU8Array((char*)buf, DataToWrite);
	for(t=0;t<4;t++)
	{
		AT24CXX_WriteOneByte(WriteAddr+t, buf[t]);
	}
}

uint8_t AT24CXX_WriteFloat32Checked(uint16_t addr, float data)
{
	uint8_t trycnt = 7;
	
	do {
		if(AT24CXX_ReadFloat32(addr) == data) {
			return 0; //ok
		}
		AT24CXX_WriteFloat32(addr, data);
		trycnt--;
	} while(trycnt > 0);
	return 1; //failed
}



