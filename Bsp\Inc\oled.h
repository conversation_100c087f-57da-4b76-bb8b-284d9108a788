//////////////////////////////////////////////////////////////////////////////////
// 说明:
// ----------------------------------------------------------------
// GND    电源地
// VCC  接5V或3.3v电源
// SCL	接PB10（SCL）
// SDA  接PB14（SDA）
// RES  接PD12
// DC   接PD11
// CS   接PD8
//******************************************************************************/
#ifndef __OLED_H
#define __OLED_H
#include <stdint.h>
#include <stdlib.h>

#define OLED_WIDTH 			256
#define OLED_HEIGHT 		64
#define OLED_X0 			0
#define OLED_Y0 			0
#define OLED_X1 			255
#define OLED_Y1 			63

#define MENU_R_CENTER 		88
#define MENU_L_CENTER 		24
#define MENU_R_CENTER 		88
#define M_ITEM_L_X 			10
#define M_LINE_1_Y 			0
#define M_LINE_2_Y 			32
#define M_ALIGN_R			124
#define M_NAME_X 			10
	
#define ALIGN_LEFT 			0x00
#define ALIGN_CENTER 		0x01
#define ALIGN_RIGHT 		0x02

#define TA_LEFT 0
#define TA_CENTER 1
#define TA_RIGHT 2

//// OLED模式设置
//// 0:4线串行模式

//#define OLED_SCLK_Clr() GPIO_ResetBits(GPIOB, GPIO_Pin_10) // CLK
//#define OLED_SCLK_Set() GPIO_SetBits(GPIOB, GPIO_Pin_10)

//#define OLED_SDIN_Clr() GPIO_ResetBits(GPIOB, GPIO_Pin_14) // DIN
//#define OLED_SDIN_Set() GPIO_SetBits(GPIOB, GPIO_Pin_14)

//#define OLED_RST_Clr() GPIO_ResetBits(GPIOD, GPIO_Pin_12) // RES
//#define OLED_RST_Set() GPIO_SetBits(GPIOD, GPIO_Pin_12)

//#define OLED_RS_Clr() GPIO_ResetBits(GPIOD, GPIO_Pin_11) // DC
//#define OLED_RS_Set() GPIO_SetBits(GPIOD, GPIO_Pin_11)

//#define OLED_CS_Clr() GPIO_ResetBits(GPIOD, GPIO_Pin_8) // CS
//#define OLED_CS_Set() GPIO_SetBits(GPIOD, GPIO_Pin_8)

//#define TA_LEFT 0
//#define TA_CENTER 1
//#define TA_RIGHT 2

// OLED控制用函数
void OLED_Brightness(uint8_t percent);
void Oled_ClearScreen(void);
void OLED_ShowCHN(uint8_t x, uint8_t y, char chr[], uint8_t size);
void Delay(unsigned int Data);
void Write_Register(unsigned char Data);
void Write_Parameter(unsigned char Data);
void Column_Address(unsigned char a);
void Row_Address(unsigned char a);
void ClearLED(unsigned char color);
void Set_Colume_Address(unsigned char Addr1, unsigned char Addr2);
void Set_Row_Address(unsigned char Addr1, unsigned char Addr2);
void OLED_ShowChar(uint8_t x, uint8_t y, uint8_t chr);
void OLED_ShowString(uint8_t x, uint8_t y, char *chr);
uint32_t oled_pow(uint8_t m, uint8_t n);
void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, uint8_t size);
void OLED_ShowCHinese(uint8_t x, uint8_t y, uint8_t *chn);
void OLED_DrawBMP(uint8_t *BMP);
void Oled_Init(void);
void drawpoint(unsigned char x, unsigned char y, unsigned char color);
void OLED_Set_Pos(unsigned char x, unsigned char y);
void bar(void);

void OLED_ShowBigChar(uint8_t x, uint8_t y, uint8_t chr);
void OLED_ShowMenu_Content(uint8_t line, char *chr, uint8_t size);
void OLED_DispSetting_withUnderline(uint8_t line, char *chr, uint8_t x0, uint8_t underLinePos);
void Oled_ShowBattery(uint8_t Li, uint8_t isCharge);
void GUI_DispString(uint8_t x, uint8_t y, uint8_t align, char *chr);

void Oled_ShowWifiState(uint8_t state);
void GUI_DrawWifi(uint8_t state);
void GUI_DrawSnow(uint8_t state);
//void Oled_ShowWifiName(char *str);
//void Oled_ShowMainScreen(uint8_t pause, uint8_t wState);
void Oled_ShowRestoreFactory();
//uint8_t OLED_ShowLargeNumber(uint32_t num);
void OLED_ShowLargerChar(uint8_t x, uint8_t y, uint8_t chr);
void OLED_ShowMediumChar(uint8_t x, uint8_t y, uint8_t chr);
// void GUI_DispMixText(uint8_t x, uint8_t y, uint8_t align, uint8_t *chr1, uint8_t zwchr[], uint8_t *chr2, uint8_t zwSize);
void GUI_DispMixText(uint8_t x, uint8_t y, uint8_t align, char *chr1, char zwchr[], char *chr2, uint8_t zwSize);

void OLED_ShowDF_State(char *chr);
void Oled_DispInLine1(char *str, uint8_t align);
void Oled_DispInLine2(char *str, uint8_t align);

void Draw_Progressbar(uint8_t val, uint8_t total);
void Oled_ShowUnit(uint8_t unitSystem);
void Oled_ShowWifiState(uint8_t state);
//uint32_t CommonVelcocity_Show(uint8_t lang, uint8_t id, uint32_t velcity_old);

void Oled_ShowLongFlag(uint8_t longmode);
void Oled_ShowRestoreFactory_Cancel();
void Oled_ShowRestoreFactory_Resetting();
void Oled_ShowRestoreFactory_ResetOk();

void OLED_Show16x32Char(uint8_t x, uint8_t y, uint8_t chr);

void Oled_DrawPixel(int32_t x, int32_t y, uint8_t color);

void Oled_CopyDispMemToScreen(uint8_t dispMem[OLED_HEIGHT][OLED_WIDTH/2]);
void Oled_CopyDispMemToScreenRect(uint8_t dispMem[OLED_HEIGHT][OLED_WIDTH/2], int32_t x0, int32_t y0, int32_t x1, int32_t y1);

#endif
