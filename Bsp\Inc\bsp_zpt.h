#ifndef BSP_ZPT_H
#define BSP_ZPT_H

#include <stdint.h>

#define DEFAULT_ZPT_TIME_NS_NO_MKS		899 //564 //1200
//#define DEFAULT_ZPT_TIME_NS_WITH_MKS	1700 //打标、测量独立采集后该参数作废

//#define EMAT_DEFAULT_ZERO_PT_NS		1200

#define MKS_WAVE_LEN					256 //打标波形长度
//#define MKS_DEFAULT_BASE_TIME_NS		818 //mks默认基础时间(1脉冲/上升沿), 单位ns
//#define MKS_DEFAULT_BASE_TIME_NS		1240.0 //mks默认基础时间(3脉冲/上升沿), 单位ns
#define MKS_DEFAULT_BASE_TIME_NS		430.0 //mks默认基础时间(3脉冲/上升沿), 单位ns

typedef struct
{
	int32_t		zeroPoint_ns;
}ZPT_T;
extern ZPT_T g_Zpt;

//typedef struct
//{
//	uint8_t		functionAvailabled;

//	uint8_t		pulNum;
//	uint16_t	fsMhz;
//	float		feMhz;
//	float		baseTimeNs;
//	float		measTimeNs;
//	
//	uint8_t		waveValid;
//	uint32_t	waveBufLen;
//	uint32_t	waveBgnPt;
//	uint32_t	waveLenPt;
//	int16_t		waveBuf[MKS_WAVE_LEN];
//}MKS_T;
//extern MKS_T g_Mks;

//uint8_t Mks_Init(void);
//uint8_t Mks_ResetToFactory(void);
//uint8_t Mks_IsFunctionAvailabled(void);
//void Mks_SetFunctionAvailability(uint8_t isSupported);
//float Mks_CalcMksTimeNs(float baseTimeNs);
//uint8_t Mks_SetMksBaseTimeNs(float baseTimeNs);
//uint8_t Mks_SetMksMeasTimeNs(float measTimeNs);
//float Mks_GetBaseTimeNs(void);
//uint8_t Mks_GetPulNum(void);

//double Mks_GetMksMeasTimeNs(void);
//double Mks_GetZptOffsetTimeNsByMks(void);

double Zpt_GetFactoryZptTimeNs(void);
uint8_t Zpt_SetZptTimeNs(int32_t zero_ns);
uint8_t Zpt_CheckZptIsValid(int32_t zptNs);
int32_t Zpt_GetZptTimeNs(void);

#endif
