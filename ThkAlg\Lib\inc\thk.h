#ifndef __THK_H__
#define __THK_H__ 
#include <stdint.h>
//#include "stm32f7xx.h"
//#include "core_cm7.h"
//#include "stm32f7xx_hal.h"
//#include "thickness.h"

#define IS_HT300 	1
#define IS_PREMAT3 	(!IS_HT300)


typedef enum {
	ALG_ID_DEFAULT = 0, ALG_ID_MULTI_PEAKS, ALG_ID_AFEW_PEAKS, ALG_ID_XCORR, ALG_ID_SINGLE_PEAK
}THKALG_ID;

//#define ALG_ID_DEFAULT			0
//#define ALG_ID_MULTI_PEAKS		1
//#define ALG_ID_AFEW_PEAKS		2
//#define ALG_ID_XCORR			3
//#define ALG_ID_SINGLE_PEAK		4
////#define ALG_ID_AFEW_PEAKS_TYPE2	5

typedef struct
{
	uint8_t		SelPoint1st_dlyPeriod; /* 首点延时周期数 */
	uint16_t	blindPointNum; /* 设置的盲区总共的点数 */
	uint16_t	blind_LastFullVal;		/*盲区内最后一个饱和点*/ //2023/2/7
	
	uint8_t 	specifyAlgId;
	
	int32_t		inputDataMaxVal; //输入数据最大值
	
	int32_t		ThicknessArray[4];
	int32_t		Popout[2];
	
	uint8_t		presetThickRange_en;	//指定厚度范围
	uint8_t		presetThickRange_max;	//指定厚度范围上限
	uint8_t		presetThickRange_min;	//指定厚度范围下限
}THICK_T;
extern THICK_T g_thick;

#define MATRIX_MOTHER_SIZE 	32768
extern short MatrixMother[MATRIX_MOTHER_SIZE];

void THK_Init(int32_t inputDataMaxVal);
int32_t THK_GetVersion(void);
double THK_GetCaliEchoTime(void);
int32_t THK_CalcBlindPointsNum(int16_t src[], int32_t srclen, uint8_t fsMhz, float feMhz, int16_t fullVal);
int32_t THK_GetBlindPointNum(void);

double THK_CalcThickness(int16_t src[], int32_t srclen, int32_t fsMhz, float feMhz, uint8_t pulNum, int32_t zeroPt_ns, int32_t velc);
double THK_CalcThicknessBySinglePeak(int16_t src[], int32_t srclen, int32_t daqDlyPt, int32_t fsMhz, float feMhz, uint8_t pulNum, int32_t zeroPt_ns, int32_t velc);
double THK_CalcThicknessByInputXcorrFft(int16_t src[], int32_t srclen, int32_t fsMhz, float feMhz, uint8_t pulNum, int32_t zeroPt_ns, int32_t velc, int16_t *pXcorrBuf, int32_t xcorrLen);

void THK_GetCaliResParas(float *flagPeak_tof, int32_t *flagPeak_periodIdx);


//uint32_t CalcMaxIdx_s16(int16_t *src, uint32_t len);

//AI
uint8_t THK_SpecifyThkAlgId(THKALG_ID alg_id);
void THK_ClearThkAlgId(void);
uint8_t THK_GetUsedAlgId(void);
uint8_t THK_GetThkAlgTrainingData(int32_t candiThkbuf[2], int32_t srcThkbuf[4], int32_t algId[4]);

uint8_t THK_IsSinglePeakAlgOfRes(void);

extern float SPK_CalcTOF_withDestPeak(int16_t *srcbuf, int32_t srclen, int32_t peakMaxIdx, int32_t peakEchoTimes, uint8_t fs, float fe, uint8_t pulNum);

#endif
