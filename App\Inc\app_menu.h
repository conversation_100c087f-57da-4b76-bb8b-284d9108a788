#ifndef __APP_MENU_H
#define __APP_MENU_H
#include "app.h" 


#define MENU_PRE_TOTAL 		11
#define MENU_PRE_USR_MAX 	3
#define MENU_ITEM_TOTAL 	11

//#define M_ITEM_CALI 		1
//#define M_ITEM_CALI_SAMPLE 	0
//#define M_ITEM_PRE_VECL 	2
//#define M_ITEM_VELC 		3
//#define M_ITEM_WIFI 		4
//#define M_ITEM_BRIGHT 		5
//#define M_ITEM_UNIT 		6
//#define M_ITEM_LANG 		7
//#define M_ITEM_AVG 			8
//#define M_ITEM_REFBIT 		9 // 参考位,小数点后第三位

#define M_ITEM_CALI 		3
#define M_ITEM_CALI_SAMPLE 	2
#define M_ITEM_PRE_VECL 	0
#define M_ITEM_VELC 		1
#define M_ITEM_WIFI 		6
#define M_ITEM_BRIGHT 		7
#define M_ITEM_UNIT 		8
#define M_ITEM_LANG 		9
#define M_ITEM_AVG 			4
#define M_ITEM_REFBIT 		5 // 参考位,小数点后第三位
#define M_ITEM_DEV_FUNC		10

uint8_t App_Menu_MainTask();

#endif
