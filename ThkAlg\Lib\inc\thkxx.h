#ifndef __THK_H
#define __THK_H	 
#include <stdint.h>

#include "thickness.h"

#define IS_HT300 	1
#define IS_PREMAT3 	(!IS_HT300)


typedef enum {
	ALG_ID_DEFAULT = 0, ALG_ID_MULTI_PEAKS, ALG_ID_AFEW_PEAKS, ALG_ID_XCORR, ALG_ID_SINGLE_PEAK
}THKALG_ID;



#define MATRIX_MOTHER_SIZE 	32768
extern short MatrixMother[MATRIX_MOTHER_SIZE];

void THK_Init(int32_t inputDataMaxVal);
int32_t THK_GetVersion(void);
double THK_GetCaliEchoTime(void);
int32_t THK_CalcBlindPointsNum(int16_t src[], int32_t srclen, uint8_t fsMhz, float feMhz, int16_t fullVal);
int32_t THK_GetBlindPointNum(void);

double THK_CalcThickness(int16_t src[], int32_t srclen, int32_t fsMhz, float feMhz, uint8_t pulNum, int32_t zeroPt_ns, int32_t velc);
double THK_CalcThicknessBySinglePeak(int16_t src[], int32_t srclen, int32_t daqDlyPt, int32_t fsMhz, float feMhz, uint8_t pulNum, int32_t zeroPt_ns, int32_t velc);
double THK_CalcThicknessByInputXcorrFft(int16_t src[], int32_t srclen, int32_t fsMhz, float feMhz, uint8_t pulNum, int32_t zeroPt_ns, int32_t velc, int16_t *pXcorrBuf, int32_t xcorrLen);

void THK_GetCaliResParas(float *flagPeak_tof, int32_t *flagPeak_periodIdx);


//uint32_t CalcMaxIdx_s16(int16_t *src, uint32_t len);

//AI
uint8_t THK_SpecifyThkAlgId(THKALG_ID alg_id);
void THK_ClearThkAlgId(void);
uint8_t THK_GetUsedAlgId(void);
uint8_t THK_GetThkAlgTrainingData(int32_t candiThkbuf[2], int32_t srcThkbuf[4], int32_t algId[4]);

uint8_t THK_IsSinglePeakAlgOfRes(void);

#endif
