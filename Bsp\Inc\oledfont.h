#ifndef __OLEDFONT_H
#define __OLEDFONT_H 	   
//常用ASCII表
//偏移量32
//ASCII字符集
//偏移量32
//大小:12*6
/************************************6*8的点阵************************************/
static const unsigned char F6x8[][6] =		
{
0x00, 0x00, 0x00, 0x00, 0x00, 0x00,// sp
0x00, 0x00, 0x00, 0x2f, 0x00, 0x00,// !
0x00, 0x00, 0x07, 0x00, 0x07, 0x00,// "
0x00, 0x14, 0x7f, 0x14, 0x7f, 0x14,// #
0x00, 0x24, 0x2a, 0x7f, 0x2a, 0x12,// $
0x00, 0x62, 0x64, 0x08, 0x13, 0x23,// %
0x00, 0x36, 0x49, 0x55, 0x22, 0x50,// &
0x00, 0x00, 0x05, 0x03, 0x00, 0x00,// '
0x00, 0x00, 0x1c, 0x22, 0x41, 0x00,// (
0x00, 0x00, 0x41, 0x22, 0x1c, 0x00,// )
0x00, 0x14, 0x08, 0x3E, 0x08, 0x14,// *
0x00, 0x08, 0x08, 0x3E, 0x08, 0x08,// +
0x00, 0x00, 0x00, 0xA0, 0x60, 0x00,// ,
0x00, 0x08, 0x08, 0x08, 0x08, 0x08,// -
0x00, 0x00, 0x60, 0x60, 0x00, 0x00,// .
0x00, 0x20, 0x10, 0x08, 0x04, 0x02,// /
0x00, 0x3E, 0x51, 0x49, 0x45, 0x3E,// 0
0x00, 0x00, 0x42, 0x7F, 0x40, 0x00,// 1
0x00, 0x42, 0x61, 0x51, 0x49, 0x46,// 2
0x00, 0x21, 0x41, 0x45, 0x4B, 0x31,// 3
0x00, 0x18, 0x14, 0x12, 0x7F, 0x10,// 4
0x00, 0x27, 0x45, 0x45, 0x45, 0x39,// 5
0x00, 0x3C, 0x4A, 0x49, 0x49, 0x30,// 6
0x00, 0x01, 0x71, 0x09, 0x05, 0x03,// 7
0x00, 0x36, 0x49, 0x49, 0x49, 0x36,// 8
0x00, 0x06, 0x49, 0x49, 0x29, 0x1E,// 9
0x00, 0x00, 0x36, 0x36, 0x00, 0x00,// :
0x00, 0x00, 0x56, 0x36, 0x00, 0x00,// ;
0x00, 0x08, 0x14, 0x22, 0x41, 0x00,// <
0x00, 0x14, 0x14, 0x14, 0x14, 0x14,// =
0x00, 0x00, 0x41, 0x22, 0x14, 0x08,// >
0x00, 0x02, 0x01, 0x51, 0x09, 0x06,// ?
0x00, 0x32, 0x49, 0x59, 0x51, 0x3E,// @
0x00, 0x7C, 0x12, 0x11, 0x12, 0x7C,// A
0x00, 0x7F, 0x49, 0x49, 0x49, 0x36,// B
0x00, 0x3E, 0x41, 0x41, 0x41, 0x22,// C
0x00, 0x7F, 0x41, 0x41, 0x22, 0x1C,// D
0x00, 0x7F, 0x49, 0x49, 0x49, 0x41,// E
0x00, 0x7F, 0x09, 0x09, 0x09, 0x01,// F
0x00, 0x3E, 0x41, 0x49, 0x49, 0x7A,// G
0x00, 0x7F, 0x08, 0x08, 0x08, 0x7F,// H
0x00, 0x00, 0x41, 0x7F, 0x41, 0x00,// I
0x00, 0x20, 0x40, 0x41, 0x3F, 0x01,// J
0x00, 0x7F, 0x08, 0x14, 0x22, 0x41,// K
0x00, 0x7F, 0x40, 0x40, 0x40, 0x40,// L
0x00, 0x7F, 0x02, 0x0C, 0x02, 0x7F,// M
0x00, 0x7F, 0x04, 0x08, 0x10, 0x7F,// N
0x00, 0x3E, 0x41, 0x41, 0x41, 0x3E,// O
0x00, 0x7F, 0x09, 0x09, 0x09, 0x06,// P
0x00, 0x3E, 0x41, 0x51, 0x21, 0x5E,// Q
0x00, 0x7F, 0x09, 0x19, 0x29, 0x46,// R
0x00, 0x46, 0x49, 0x49, 0x49, 0x31,// S
0x00, 0x01, 0x01, 0x7F, 0x01, 0x01,// T
0x00, 0x3F, 0x40, 0x40, 0x40, 0x3F,// U
0x00, 0x1F, 0x20, 0x40, 0x20, 0x1F,// V
0x00, 0x3F, 0x40, 0x38, 0x40, 0x3F,// W
0x00, 0x63, 0x14, 0x08, 0x14, 0x63,// X
0x00, 0x07, 0x08, 0x70, 0x08, 0x07,// Y
0x00, 0x61, 0x51, 0x49, 0x45, 0x43,// Z
0x00, 0x00, 0x7F, 0x41, 0x41, 0x00,// [
0x00, 0x55, 0x2A, 0x55, 0x2A, 0x55,// 55
0x00, 0x00, 0x41, 0x41, 0x7F, 0x00,// ]
0x00, 0x04, 0x02, 0x01, 0x02, 0x04,// ^
0x00, 0x40, 0x40, 0x40, 0x40, 0x40,// _
0x00, 0x00, 0x01, 0x02, 0x04, 0x00,// '
0x00, 0x20, 0x54, 0x54, 0x54, 0x78,// a
0x00, 0x7F, 0x48, 0x44, 0x44, 0x38,// b
0x00, 0x38, 0x44, 0x44, 0x44, 0x20,// c
0x00, 0x38, 0x44, 0x44, 0x48, 0x7F,// d
0x00, 0x38, 0x54, 0x54, 0x54, 0x18,// e
0x00, 0x08, 0x7E, 0x09, 0x01, 0x02,// f
0x00, 0x18, 0xA4, 0xA4, 0xA4, 0x7C,// g
0x00, 0x7F, 0x08, 0x04, 0x04, 0x78,// h
0x00, 0x00, 0x44, 0x7D, 0x40, 0x00,// i
0x00, 0x40, 0x80, 0x84, 0x7D, 0x00,// j
0x00, 0x7F, 0x10, 0x28, 0x44, 0x00,// k
0x00, 0x00, 0x41, 0x7F, 0x40, 0x00,// l
0x00, 0x7C, 0x04, 0x18, 0x04, 0x78,// m
0x00, 0x7C, 0x08, 0x04, 0x04, 0x78,// n
0x00, 0x38, 0x44, 0x44, 0x44, 0x38,// o
0x00, 0xFC, 0x24, 0x24, 0x24, 0x18,// p
0x00, 0x18, 0x24, 0x24, 0x18, 0xFC,// q
0x00, 0x7C, 0x08, 0x04, 0x04, 0x08,// r
0x00, 0x48, 0x54, 0x54, 0x54, 0x20,// s
0x00, 0x04, 0x3F, 0x44, 0x40, 0x20,// t
0x00, 0x3C, 0x40, 0x40, 0x20, 0x7C,// u
0x00, 0x1C, 0x20, 0x40, 0x20, 0x1C,// v
0x00, 0x3C, 0x40, 0x30, 0x40, 0x3C,// w
0x00, 0x44, 0x28, 0x10, 0x28, 0x44,// x
0x00, 0x1C, 0xA0, 0xA0, 0xA0, 0x7C,// y
0x00, 0x44, 0x64, 0x54, 0x4C, 0x44,// z
0x14, 0x14, 0x14, 0x14, 0x14, 0x14,// horiz lines
};
/****************************************8*16的点阵************************************/
static const unsigned char F8X16[]=	  
{
  0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,// 0
  0x00,0x00,0x00,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x33,0x30,0x00,0x00,0x00,//! 1
  0x00,0x10,0x0C,0x06,0x10,0x0C,0x06,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//" 2
  0x40,0xC0,0x78,0x40,0xC0,0x78,0x40,0x00,0x04,0x3F,0x04,0x04,0x3F,0x04,0x04,0x00,//# 3
  0x00,0x70,0x88,0xFC,0x08,0x30,0x00,0x00,0x00,0x18,0x20,0xFF,0x21,0x1E,0x00,0x00,//$ 4
  0xF0,0x08,0xF0,0x00,0xE0,0x18,0x00,0x00,0x00,0x21,0x1C,0x03,0x1E,0x21,0x1E,0x00,//% 5
  0x00,0xF0,0x08,0x88,0x70,0x00,0x00,0x00,0x1E,0x21,0x23,0x24,0x19,0x27,0x21,0x10,//& 6
  0x10,0x16,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//' 7
  0x00,0x00,0x00,0xE0,0x18,0x04,0x02,0x00,0x00,0x00,0x00,0x07,0x18,0x20,0x40,0x00,//( 8
  0x00,0x02,0x04,0x18,0xE0,0x00,0x00,0x00,0x00,0x40,0x20,0x18,0x07,0x00,0x00,0x00,//) 9
  0x40,0x40,0x80,0xF0,0x80,0x40,0x40,0x00,0x02,0x02,0x01,0x0F,0x01,0x02,0x02,0x00,//* 10
  0x00,0x00,0x00,0xF0,0x00,0x00,0x00,0x00,0x01,0x01,0x01,0x1F,0x01,0x01,0x01,0x00,//+ 11
  0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xB0,0x70,0x00,0x00,0x00,0x00,0x00,//, 12
  0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x01,//- 13
  0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,0x00,0x00,//. 14
  0x00,0x00,0x00,0x00,0x80,0x60,0x18,0x04,0x00,0x60,0x18,0x06,0x01,0x00,0x00,0x00,/// 15
  0x00,0xE0,0x10,0x08,0x08,0x10,0xE0,0x00,0x00,0x0F,0x10,0x20,0x20,0x10,0x0F,0x00,//0 16
  0x00,0x10,0x10,0xF8,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x3F,0x20,0x20,0x00,0x00,//1 17
  0x00,0x70,0x08,0x08,0x08,0x88,0x70,0x00,0x00,0x30,0x28,0x24,0x22,0x21,0x30,0x00,//2 18
  0x00,0x30,0x08,0x88,0x88,0x48,0x30,0x00,0x00,0x18,0x20,0x20,0x20,0x11,0x0E,0x00,//3 19
  0x00,0x00,0xC0,0x20,0x10,0xF8,0x00,0x00,0x00,0x07,0x04,0x24,0x24,0x3F,0x24,0x00,//4 20
  0x00,0xF8,0x08,0x88,0x88,0x08,0x08,0x00,0x00,0x19,0x21,0x20,0x20,0x11,0x0E,0x00,//5 21
  0x00,0xE0,0x10,0x88,0x88,0x18,0x00,0x00,0x00,0x0F,0x11,0x20,0x20,0x11,0x0E,0x00,//6 22
  0x00,0x38,0x08,0x08,0xC8,0x38,0x08,0x00,0x00,0x00,0x00,0x3F,0x00,0x00,0x00,0x00,//7 23
  0x00,0x70,0x88,0x08,0x08,0x88,0x70,0x00,0x00,0x1C,0x22,0x21,0x21,0x22,0x1C,0x00,//8 24
  0x00,0xE0,0x10,0x08,0x08,0x10,0xE0,0x00,0x00,0x00,0x31,0x22,0x22,0x11,0x0F,0x00,//9 25
  0x00,0x00,0x00,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x30,0x00,0x00,0x00,//: 26
  0x00,0x00,0x00,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x60,0x00,0x00,0x00,0x00,//; 27
  0x00,0x00,0x80,0x40,0x20,0x10,0x08,0x00,0x00,0x01,0x02,0x04,0x08,0x10,0x20,0x00,//< 28
  0x40,0x40,0x40,0x40,0x40,0x40,0x40,0x00,0x04,0x04,0x04,0x04,0x04,0x04,0x04,0x00,//= 29
  0x00,0x08,0x10,0x20,0x40,0x80,0x00,0x00,0x00,0x20,0x10,0x08,0x04,0x02,0x01,0x00,//> 30
  0x00,0x70,0x48,0x08,0x08,0x08,0xF0,0x00,0x00,0x00,0x00,0x30,0x36,0x01,0x00,0x00,//? 31
  0xC0,0x30,0xC8,0x28,0xE8,0x10,0xE0,0x00,0x07,0x18,0x27,0x24,0x23,0x14,0x0B,0x00,//@ 32
  0x00,0x00,0xC0,0x38,0xE0,0x00,0x00,0x00,0x20,0x3C,0x23,0x02,0x02,0x27,0x38,0x20,//A 33
  0x08,0xF8,0x88,0x88,0x88,0x70,0x00,0x00,0x20,0x3F,0x20,0x20,0x20,0x11,0x0E,0x00,//B 34
  0xC0,0x30,0x08,0x08,0x08,0x08,0x38,0x00,0x07,0x18,0x20,0x20,0x20,0x10,0x08,0x00,//C 35
  0x08,0xF8,0x08,0x08,0x08,0x10,0xE0,0x00,0x20,0x3F,0x20,0x20,0x20,0x10,0x0F,0x00,//D 36
  0x08,0xF8,0x88,0x88,0xE8,0x08,0x10,0x00,0x20,0x3F,0x20,0x20,0x23,0x20,0x18,0x00,//E 37
  0x08,0xF8,0x88,0x88,0xE8,0x08,0x10,0x00,0x20,0x3F,0x20,0x00,0x03,0x00,0x00,0x00,//F 38
  0xC0,0x30,0x08,0x08,0x08,0x38,0x00,0x00,0x07,0x18,0x20,0x20,0x22,0x1E,0x02,0x00,//G 39
  0x08,0xF8,0x08,0x00,0x00,0x08,0xF8,0x08,0x20,0x3F,0x21,0x01,0x01,0x21,0x3F,0x20,//H 40
  0x00,0x08,0x08,0xF8,0x08,0x08,0x00,0x00,0x00,0x20,0x20,0x3F,0x20,0x20,0x00,0x00,//I 41
  0x00,0x00,0x08,0x08,0xF8,0x08,0x08,0x00,0xC0,0x80,0x80,0x80,0x7F,0x00,0x00,0x00,//J 42
  0x08,0xF8,0x88,0xC0,0x28,0x18,0x08,0x00,0x20,0x3F,0x20,0x01,0x26,0x38,0x20,0x00,//K 43
  0x08,0xF8,0x08,0x00,0x00,0x00,0x00,0x00,0x20,0x3F,0x20,0x20,0x20,0x20,0x30,0x00,//L 44
  0x08,0xF8,0xF8,0x00,0xF8,0xF8,0x08,0x00,0x20,0x3F,0x00,0x3F,0x00,0x3F,0x20,0x00,//M 45
  0x08,0xF8,0x30,0xC0,0x00,0x08,0xF8,0x08,0x20,0x3F,0x20,0x00,0x07,0x18,0x3F,0x00,//N 46
  0xE0,0x10,0x08,0x08,0x08,0x10,0xE0,0x00,0x0F,0x10,0x20,0x20,0x20,0x10,0x0F,0x00,//O 47
  0x08,0xF8,0x08,0x08,0x08,0x08,0xF0,0x00,0x20,0x3F,0x21,0x01,0x01,0x01,0x00,0x00,//P 48
  0xE0,0x10,0x08,0x08,0x08,0x10,0xE0,0x00,0x0F,0x18,0x24,0x24,0x38,0x50,0x4F,0x00,//Q 49
  0x08,0xF8,0x88,0x88,0x88,0x88,0x70,0x00,0x20,0x3F,0x20,0x00,0x03,0x0C,0x30,0x20,//R 50
  0x00,0x70,0x88,0x08,0x08,0x08,0x38,0x00,0x00,0x38,0x20,0x21,0x21,0x22,0x1C,0x00,//S 51
  0x18,0x08,0x08,0xF8,0x08,0x08,0x18,0x00,0x00,0x00,0x20,0x3F,0x20,0x00,0x00,0x00,//T 52
  0x08,0xF8,0x08,0x00,0x00,0x08,0xF8,0x08,0x00,0x1F,0x20,0x20,0x20,0x20,0x1F,0x00,//U 53
  0x08,0x78,0x88,0x00,0x00,0xC8,0x38,0x08,0x00,0x00,0x07,0x38,0x0E,0x01,0x00,0x00,//V 54
  0xF8,0x08,0x00,0xF8,0x00,0x08,0xF8,0x00,0x03,0x3C,0x07,0x00,0x07,0x3C,0x03,0x00,//W 55
  0x08,0x18,0x68,0x80,0x80,0x68,0x18,0x08,0x20,0x30,0x2C,0x03,0x03,0x2C,0x30,0x20,//X 56
  0x08,0x38,0xC8,0x00,0xC8,0x38,0x08,0x00,0x00,0x00,0x20,0x3F,0x20,0x00,0x00,0x00,//Y 57
  0x10,0x08,0x08,0x08,0xC8,0x38,0x08,0x00,0x20,0x38,0x26,0x21,0x20,0x20,0x18,0x00,//Z 58
  0x00,0x00,0x00,0xFE,0x02,0x02,0x02,0x00,0x00,0x00,0x00,0x7F,0x40,0x40,0x40,0x00,//[ 59
  0x00,0x0C,0x30,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x06,0x38,0xC0,0x00,//\ 60
  0x00,0x02,0x02,0x02,0xFE,0x00,0x00,0x00,0x00,0x40,0x40,0x40,0x7F,0x00,0x00,0x00,//] 61
  0x00,0x00,0x04,0x02,0x02,0x02,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//^ 62
  0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,//_ 63
  0x00,0x02,0x02,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//` 64
  0x00,0x00,0x80,0x80,0x80,0x80,0x00,0x00,0x00,0x19,0x24,0x22,0x22,0x22,0x3F,0x20,//a 65
  0x08,0xF8,0x00,0x80,0x80,0x00,0x00,0x00,0x00,0x3F,0x11,0x20,0x20,0x11,0x0E,0x00,//b 66
  0x00,0x00,0x00,0x80,0x80,0x80,0x00,0x00,0x00,0x0E,0x11,0x20,0x20,0x20,0x11,0x00,//c 67
  0x00,0x00,0x00,0x80,0x80,0x88,0xF8,0x00,0x00,0x0E,0x11,0x20,0x20,0x10,0x3F,0x20,//d 68
  0x00,0x00,0x80,0x80,0x80,0x80,0x00,0x00,0x00,0x1F,0x22,0x22,0x22,0x22,0x13,0x00,//e 69
  0x00,0x80,0x80,0xF0,0x88,0x88,0x88,0x18,0x00,0x20,0x20,0x3F,0x20,0x20,0x00,0x00,//f 70
  0x00,0x00,0x80,0x80,0x80,0x80,0x80,0x00,0x00,0x6B,0x94,0x94,0x94,0x93,0x60,0x00,//g 71
  0x08,0xF8,0x00,0x80,0x80,0x80,0x00,0x00,0x20,0x3F,0x21,0x00,0x00,0x20,0x3F,0x20,//h 72
  0x00,0x80,0x98,0x98,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x3F,0x20,0x20,0x00,0x00,//i 73
  0x00,0x00,0x00,0x80,0x98,0x98,0x00,0x00,0x00,0xC0,0x80,0x80,0x80,0x7F,0x00,0x00,//j 74
  0x08,0xF8,0x00,0x00,0x80,0x80,0x80,0x00,0x20,0x3F,0x24,0x02,0x2D,0x30,0x20,0x00,//k 75
  0x00,0x08,0x08,0xF8,0x00,0x00,0x00,0x00,0x00,0x20,0x20,0x3F,0x20,0x20,0x00,0x00,//l 76
  0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x00,0x20,0x3F,0x20,0x00,0x3F,0x20,0x00,0x3F,//m 77
  0x80,0x80,0x00,0x80,0x80,0x80,0x00,0x00,0x20,0x3F,0x21,0x00,0x00,0x20,0x3F,0x20,//n 78
  0x00,0x00,0x80,0x80,0x80,0x80,0x00,0x00,0x00,0x1F,0x20,0x20,0x20,0x20,0x1F,0x00,//o 79
  0x80,0x80,0x00,0x80,0x80,0x00,0x00,0x00,0x80,0xFF,0xA1,0x20,0x20,0x11,0x0E,0x00,//p 80
  0x00,0x00,0x00,0x80,0x80,0x80,0x80,0x00,0x00,0x0E,0x11,0x20,0x20,0xA0,0xFF,0x80,//q 81
  0x80,0x80,0x80,0x00,0x80,0x80,0x80,0x00,0x20,0x20,0x3F,0x21,0x20,0x00,0x01,0x00,//r 82
  0x00,0x00,0x80,0x80,0x80,0x80,0x80,0x00,0x00,0x33,0x24,0x24,0x24,0x24,0x19,0x00,//s 83
  0x00,0x80,0x80,0xE0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x1F,0x20,0x20,0x00,0x00,//t 84
  0x80,0x80,0x00,0x00,0x00,0x80,0x80,0x00,0x00,0x1F,0x20,0x20,0x20,0x10,0x3F,0x20,//u 85
  0x80,0x80,0x80,0x00,0x00,0x80,0x80,0x80,0x00,0x01,0x0E,0x30,0x08,0x06,0x01,0x00,//v 86
  0x80,0x80,0x00,0x80,0x00,0x80,0x80,0x80,0x0F,0x30,0x0C,0x03,0x0C,0x30,0x0F,0x00,//w 87
  0x00,0x80,0x80,0x00,0x80,0x80,0x80,0x00,0x00,0x20,0x31,0x2E,0x0E,0x31,0x20,0x00,//x 88
  0x80,0x80,0x80,0x00,0x00,0x80,0x80,0x80,0x80,0x81,0x8E,0x70,0x18,0x06,0x01,0x00,//y 89
  0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x00,0x00,0x21,0x30,0x2C,0x22,0x21,0x30,0x00,//z 90
  0x00,0x00,0x00,0x00,0x80,0x7C,0x02,0x02,0x00,0x00,0x00,0x00,0x00,0x3F,0x40,0x40,//{ 91
  0x00,0x00,0x00,0x00,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x00,0x00,0x00,//| 92
  0x00,0x02,0x02,0x7C,0x80,0x00,0x00,0x00,0x00,0x40,0x40,0x3F,0x00,0x00,0x00,0x00,//} 93
  0x00,0x06,0x01,0x01,0x02,0x02,0x04,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//~ 94
};

/*  (0) !(1) "(2) #(3) $(4) %(5) &(6) '(7) ((8) )(9) *(10) +(11) ,(12) -(13) .(14) /(15)
 0(16) 1(17) 2(18) 3(19) 4(20) 5(21) 6(22) 7(23) 8(24) 9(25) :(26) ;(27) <(28) =(29) >(30) ?(31)
 @(32) A(33) B(34) C(35) D(36) E(37) F(38) G(39) H(40) I(41) J(42) K(43) L(44) M(45) N(46) O(47)
 P(48) Q(49) R(50) S(51) T(52) U(53) V(54) W(55) X(56) Y(57) Z(58) [(59) \(60) ](61) ^(62) _(63)
 '(64) a(65) b(66) c(67) d(68) e(69) f(70) g(71) h(72) i(73) j(74) k(75) l(76) m(77) n(78) o(79)
 p(80) q(81) r(82) s(83) t(84) u(85) v(86) w(87) x(88) y(89) z(90) {(91) |(92) }(93) ~(94)*/
static const unsigned char F12X24[]=
{
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//" ",0
0x00,0x00,0x00,0x00,0xF0,0xF0,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x01,0x7F,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x1C,0x1C,0x00,//
0x00,0x00,0x00,0x00,//"!",1
0x00,0x80,0x60,0x30,0x1C,0x8C,0x60,0x30,0x1C,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//""",2
0x00,0x00,0xE0,0x00,0x00,0x00,0x00,0x00,0xE0,0x00,0x00,0x00,0x86,0xE6,0x9F,0x86,//
0x86,0x86,0x86,0xE6,0x9F,0x86,0x00,0x00,0x01,0x1F,0x01,0x01,0x01,0x01,0x01,0x1F,//
0x01,0x01,0x00,0x00,//"#",3
0x00,0x80,0xC0,0x60,0x20,0xF8,0x20,0xE0,0xC0,0x00,0x00,0x00,0x00,0x03,0x07,0x0C,//
0x18,0xFF,0x70,0xE1,0x81,0x00,0x00,0x00,0x00,0x07,0x0F,0x10,0x10,0x7F,0x10,0x0F,//
0x07,0x00,0x00,0x00,//"$",4
0x80,0x60,0x20,0x60,0x80,0x00,0x00,0x00,0xE0,0x20,0x00,0x00,0x0F,0x30,0x20,0x30,
0x9F,0x70,0xDC,0x37,0x10,0x30,0xC0,0x00,0x00,0x00,0x10,0x0E,0x03,0x00,0x07,0x18,
0x10,0x18,0x07,0x00,//"%",5
0x00,0xC0,0x20,0x20,0xE0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0x1F,0x38,0xE8,//
0x87,0x03,0xC4,0x3C,0x04,0x00,0x00,0x00,0x0F,0x18,0x10,0x10,0x0B,0x07,0x0D,0x10,//
0x10,0x08,0x00,0x00,//"&",6
0x80,0x8C,0x4C,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//"'",7
0x00,0x00,0x00,0x00,0x00,0x80,0xE0,0x30,0x08,0x04,0x00,0x00,0x00,0x00,0x00,0x00,//
0xFE,0xFF,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x0F,0x18,//
0x20,0x40,0x00,0x00,//"(",8
0x04,0x08,0x30,0xE0,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,//
0xFF,0xFE,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x20,0x18,0x0F,0x03,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//")",9
0x00,0x00,0x00,0x00,0x00,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x42,0x66,0x66,0x3C,//
0x18,0xFF,0x18,0x3C,0x66,0x66,0x42,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x00,//
0x00,0x00,0x00,0x00,//"*",10
0x00,0x00,0x00,0x00,0x00,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x10,0x10,//
0x10,0xFF,0x10,0x10,0x10,0x10,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x00,0x00,//
0x00,0x00,0x00,0x00,//"+",11
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x8C,0x4C,0x38,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//",",12
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x10,0x10,//
0x10,0x10,0x10,0x10,0x10,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//"-",13
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x1C,0x1C,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//".",14
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0x38,0x0C,0x00,0x00,0x00,0x00,0x00,0x80,//
0x70,0x1C,0x03,0x00,0x00,0x00,0x00,0x00,0x60,0x38,0x0E,0x01,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//"/",15
0x00,0x80,0xC0,0x60,0x20,0x20,0x60,0xC0,0x80,0x00,0x00,0x00,0xFE,0xFF,0x01,0x00,//
0x00,0x00,0x00,0x01,0xFF,0xFE,0x00,0x00,0x01,0x07,0x0E,0x18,0x10,0x10,0x18,0x0E,//
0x07,0x01,0x00,0x00,//"0",16
0x00,0x80,0x80,0x80,0xC0,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x10,0x1F,0x1F,0x10,0x10,//
0x10,0x00,0x00,0x00,//"1",17
0x80,0x40,0x20,0x20,0x20,0x20,0x60,0xC0,0x80,0x00,0x00,0x00,0x03,0x03,0x00,0x80,//
0x40,0x20,0x38,0x1F,0x07,0x00,0x00,0x00,0x1C,0x1A,0x19,0x18,0x18,0x18,0x18,0x18,//
0x1F,0x00,0x00,0x00,//"2",18
0x80,0xC0,0x20,0x20,0x20,0x60,0xC0,0x80,0x00,0x00,0x00,0x00,0x03,0x03,0x00,0x10,//
0x10,0x18,0x2F,0xE7,0x80,0x00,0x00,0x00,0x07,0x0F,0x10,0x10,0x10,0x10,0x18,0x0F,//
0x07,0x00,0x00,0x00,//"3",19
0x00,0x00,0x00,0x00,0x00,0xC0,0xE0,0xF0,0x00,0x00,0x00,0x00,0xC0,0xB0,0x88,0x86,//
0x81,0x80,0xFF,0xFF,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x1F,0x1F,//
0x10,0x10,0x00,0x00,//"4",20
0x00,0xE0,0x60,0x60,0x60,0x60,0x60,0x60,0x60,0x00,0x00,0x00,0x00,0x3F,0x10,0x08,//
0x08,0x08,0x18,0xF0,0xE0,0x00,0x00,0x00,0x07,0x0B,0x10,0x10,0x10,0x10,0x1C,0x0F,//
0x03,0x00,0x00,0x00,//"5",21
0x00,0x80,0xC0,0x40,0x20,0x20,0x20,0xE0,0xC0,0x00,0x00,0x00,0xFC,0xFF,0x21,0x10,//
0x08,0x08,0x08,0x18,0xF0,0xE0,0x00,0x00,0x01,0x07,0x0C,0x18,0x10,0x10,0x10,0x08,//
0x0F,0x03,0x00,0x00,//"6",22
0x00,0xC0,0xE0,0x60,0x60,0x60,0x60,0x60,0xE0,0x60,0x00,0x00,0x00,0x03,0x00,0x00,//
0x00,0xE0,0x18,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x1F,0x00,0x00,//
0x00,0x00,0x00,0x00,//"7",23
0x80,0xC0,0x60,0x20,0x20,0x20,0x20,0x60,0xC0,0x80,0x00,0x00,0x87,0xEF,0x2C,0x18,//
0x18,0x30,0x30,0x68,0xCF,0x83,0x00,0x00,0x07,0x0F,0x08,0x10,0x10,0x10,0x10,0x18,//
0x0F,0x07,0x00,0x00,//"8",24
0x00,0xC0,0xC0,0x20,0x20,0x20,0x20,0xC0,0x80,0x00,0x00,0x00,0x1F,0x3F,0x60,0x40,//
0x40,0x40,0x20,0x10,0xFF,0xFE,0x00,0x00,0x00,0x0C,0x1C,0x10,0x10,0x10,0x08,0x0F,//
0x03,0x00,0x00,0x00,//"9",25
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x0E,0x0E,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x1C,0x1C,0x00,//
0x00,0x00,0x00,0x00,//":",26
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x0C,0x0C,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x58,0x38,0x00,0x00,//
0x00,0x00,0x00,0x00,//";",27
0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x40,0x20,0x10,0x00,0x00,0x00,0x10,0x28,0x44,//
0x82,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x02,0x04,//
0x08,0x10,0x00,0x00,//"<",28
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x84,0x84,0x84,0x84,//
0x84,0x84,0x84,0x84,0x84,0x84,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//"=",29
0x00,0x10,0x20,0x40,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x01,0x82,0x44,0x28,0x10,0x00,0x00,0x00,0x10,0x08,0x04,0x02,0x01,0x00,0x00,//
0x00,0x00,0x00,0x00,//">",30
0xC0,0x20,0x20,0x10,0x10,0x10,0x10,0x30,0xE0,0xC0,0x00,0x00,0x03,0x03,0x00,0x00,//
0xF0,0x10,0x08,0x0C,0x07,0x03,0x00,0x00,0x00,0x00,0x00,0x1C,0x1C,0x1C,0x00,0x00,//
0x00,0x00,0x00,0x00,//"?",31
0x00,0x00,0xC0,0x40,0x60,0x20,0x20,0x20,0x40,0xC0,0x00,0x00,0xFC,0xFF,0x01,0xF0,//
0x0E,0x03,0xC1,0xFE,0x03,0x80,0x7F,0x00,0x01,0x07,0x0E,0x08,0x11,0x11,0x10,0x11,//
0x09,0x04,0x02,0x00,//"@",32
0x00,0x00,0x00,0x80,0xE0,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x7C,0x43,//
0x40,0x47,0x7F,0xF8,0x80,0x00,0x00,0x00,0x18,0x1F,0x10,0x00,0x00,0x00,0x00,0x13,//
0x1F,0x1C,0x10,0x00,//"A",33
0xE0,0xE0,0x20,0x20,0x20,0x20,0x60,0xC0,0x80,0x00,0x00,0x00,0xFF,0xFF,0x10,0x10,//
0x10,0x10,0x18,0x2F,0xE7,0x80,0x00,0x00,0x1F,0x1F,0x10,0x10,0x10,0x10,0x10,0x18,//
0x0F,0x07,0x00,0x00,//"B",34
0x00,0x80,0xC0,0x40,0x20,0x20,0x20,0x20,0x60,0xE0,0x00,0x00,0xFC,0xFF,0x01,0x00,//
0x00,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x01,0x07,0x0E,0x18,0x10,0x10,0x10,0x08,//
0x04,0x03,0x00,0x00,//"C",35
0xE0,0xE0,0x20,0x20,0x20,0x20,0x40,0xC0,0x80,0x00,0x00,0x00,0xFF,0xFF,0x00,0x00,//
0x00,0x00,0x00,0x01,0xFF,0xFE,0x00,0x00,0x1F,0x1F,0x10,0x10,0x10,0x18,0x08,0x0E,//
0x07,0x01,0x00,0x00,//"D",36
0xE0,0xE0,0x20,0x20,0x20,0x20,0x20,0x20,0x60,0x80,0x00,0x00,0xFF,0xFF,0x10,0x10,//
0x10,0x10,0x7C,0x00,0x00,0x00,0x00,0x00,0x1F,0x1F,0x10,0x10,0x10,0x10,0x10,0x10,//
0x18,0x06,0x00,0x00,//"E",37
0xE0,0xE0,0x20,0x20,0x20,0x20,0x20,0x60,0x60,0x80,0x00,0x00,0xFF,0xFF,0x10,0x10,//
0x10,0x10,0x7C,0x00,0x00,0x01,0x00,0x00,0x1F,0x1F,0x10,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//"F",38
0x00,0x80,0xC0,0x60,0x20,0x20,0x20,0x40,0xE0,0x00,0x00,0x00,0xFC,0xFF,0x01,0x00,//
0x00,0x40,0x40,0xC0,0xC1,0x40,0x40,0x00,0x01,0x07,0x0E,0x18,0x10,0x10,0x10,0x0F,//
0x0F,0x00,0x00,0x00,//"G",39
0xE0,0xE0,0x20,0x00,0x00,0x00,0x00,0x20,0xE0,0xE0,0x20,0x00,0xFF,0xFF,0x10,0x10,//
0x10,0x10,0x10,0x10,0xFF,0xFF,0x00,0x00,0x1F,0x1F,0x10,0x00,0x00,0x00,0x00,0x10,//
0x1F,0x1F,0x10,0x00,//"H",40
0x00,0x20,0x20,0x20,0xE0,0xE0,0x20,0x20,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x10,0x1F,0x1F,0x10,0x10,//
0x10,0x00,0x00,0x00,//"I",41
0x00,0x00,0x00,0x20,0x20,0x20,0xE0,0xE0,0x20,0x20,0x20,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0xFF,0xFF,0x00,0x00,0x00,0x00,0x60,0xE0,0x80,0x80,0x80,0xC0,0x7F,0x3F,//
0x00,0x00,0x00,0x00,//"J",42
0xE0,0xE0,0x20,0x00,0x00,0x20,0xA0,0x60,0x20,0x20,0x00,0x00,0xFF,0xFF,0x30,0x18,//
0x7C,0xE3,0xC0,0x00,0x00,0x00,0x00,0x00,0x1F,0x1F,0x10,0x00,0x00,0x01,0x13,0x1F,//
0x1C,0x18,0x10,0x00,//"K",43
0xE0,0xE0,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1F,0x1F,0x10,0x10,0x10,0x10,0x10,0x10,//
0x18,0x06,0x00,0x00,//"L",44
0xE0,0xE0,0xE0,0x00,0x00,0x00,0x00,0xE0,0xE0,0xE0,0x20,0x00,0xFF,0x01,0x3F,0xFE,//
0xC0,0xE0,0x1E,0x01,0xFF,0xFF,0x00,0x00,0x1F,0x10,0x00,0x03,0x1F,0x03,0x00,0x10,//
0x1F,0x1F,0x10,0x00,//"M",45
0xE0,0xE0,0xC0,0x00,0x00,0x00,0x00,0x00,0x20,0xE0,0x20,0x00,0xFF,0x00,0x03,0x07,//
0x1C,0x78,0xE0,0x80,0x00,0xFF,0x00,0x00,0x1F,0x10,0x00,0x00,0x00,0x00,0x00,0x03,//
0x0F,0x1F,0x00,0x00,//"N",46
0x00,0x80,0xC0,0x60,0x20,0x20,0x60,0xC0,0x80,0x00,0x00,0x00,0xFE,0xFF,0x01,0x00,//
0x00,0x00,0x00,0x00,0xFF,0xFE,0x00,0x00,0x01,0x07,0x0E,0x18,0x10,0x10,0x18,0x0C,//
0x07,0x01,0x00,0x00,//"O",47
0xE0,0xE0,0x20,0x20,0x20,0x20,0x20,0x60,0xC0,0x80,0x00,0x00,0xFF,0xFF,0x20,0x20,//
0x20,0x20,0x20,0x30,0x1F,0x0F,0x00,0x00,0x1F,0x1F,0x10,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//"P",48
0x00,0x80,0xC0,0x60,0x20,0x20,0x60,0xC0,0x80,0x00,0x00,0x00,0xFE,0xFF,0x01,0x00,//
0x00,0x00,0x00,0x00,0xFF,0xFE,0x00,0x00,0x01,0x07,0x0E,0x11,0x11,0x13,0x3C,0x7C,//
0x67,0x21,0x00,0x00,//"Q",49
0xE0,0xE0,0x20,0x20,0x20,0x20,0x20,0x60,0xC0,0x80,0x00,0x00,0xFF,0xFF,0x10,0x10,//
0x30,0xF0,0xD0,0x08,0x0F,0x07,0x00,0x00,0x1F,0x1F,0x10,0x00,0x00,0x00,0x03,0x0F,//
0x1C,0x10,0x10,0x00,//"R",50
0x80,0xC0,0x60,0x20,0x20,0x20,0x20,0x40,0x40,0xE0,0x00,0x00,0x07,0x0F,0x0C,0x18,//
0x18,0x30,0x30,0x60,0xE0,0x81,0x00,0x00,0x1F,0x0C,0x08,0x10,0x10,0x10,0x10,0x18,//
0x0F,0x07,0x00,0x00,//"S",51
0x60,0x20,0x20,0x20,0xE0,0xE0,0x20,0x20,0x20,0x60,0x80,0x00,0x00,0x00,0x00,0x00,//
0xFF,0xFF,0x00,0x00,0x00,0x00,0x01,0x00,0x00,0x00,0x00,0x10,0x1F,0x1F,0x10,0x00,//
0x00,0x00,0x00,0x00,//"T",52
0xE0,0xE0,0x20,0x00,0x00,0x00,0x00,0x00,0x20,0xE0,0x20,0x00,0xFF,0xFF,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0xFF,0x00,0x00,0x07,0x0F,0x18,0x10,0x10,0x10,0x10,0x10,//
0x08,0x07,0x00,0x00,//"U",53
0x60,0xE0,0xE0,0x20,0x00,0x00,0x00,0x20,0xE0,0x60,0x20,0x00,0x00,0x07,0x7F,0xF8,//
0x80,0x00,0x80,0x7C,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x1F,0x1C,0x07,0x00,//
0x00,0x00,0x00,0x00,//"V",54
0xE0,0xE0,0x20,0x00,0xE0,0xE0,0x20,0x00,0x20,0xE0,0x20,0x00,0x07,0xFF,0xF8,0xE0,//
0x1F,0xFF,0xFC,0xE0,0x1F,0x00,0x00,0x00,0x00,0x03,0x1F,0x03,0x00,0x01,0x1F,0x03,//
0x00,0x00,0x00,0x00,//"W",55
0x20,0x60,0xE0,0xA0,0x00,0x00,0x20,0xE0,0x60,0x20,0x00,0x00,0x00,0x00,0x03,0x8F,//
0x7C,0xF8,0xC6,0x01,0x00,0x00,0x00,0x00,0x10,0x18,0x1E,0x13,0x00,0x01,0x17,0x1F,//
0x18,0x10,0x00,0x00,//"X",56
0x60,0xE0,0xE0,0x20,0x00,0x00,0x00,0x20,0xE0,0x60,0x20,0x00,0x00,0x01,0x07,0x3E,//
0xF8,0xE0,0x18,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x1F,0x1F,0x10,0x10,//
0x00,0x00,0x00,0x00,//"Y",57
0x80,0x60,0x20,0x20,0x20,0x20,0xA0,0xE0,0xE0,0x20,0x00,0x00,0x00,0x00,0x00,0xC0,//
0xF0,0x3E,0x0F,0x03,0x00,0x00,0x00,0x00,0x10,0x1C,0x1F,0x17,0x10,0x10,0x10,0x10,//
0x18,0x06,0x00,0x00,//"Z",58
0x00,0x00,0x00,0x00,0xFC,0x04,0x04,0x04,0x04,0x04,0x00,0x00,0x00,0x00,0x00,0x00,//
0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x7F,0x40,0x40,0x40,//
0x40,0x40,0x00,0x00,//"[",59
0x00,0x10,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,//
0x1C,0x60,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x0C,//
0x70,0x80,0x00,0x00,//"\",60
0x00,0x04,0x04,0x04,0x04,0x04,0xFC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x40,0x40,0x40,0x40,0x7F,0x00,//
0x00,0x00,0x00,0x00,//"]",61
0x00,0x00,0x10,0x08,0x0C,0x04,0x0C,0x08,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//"^",62
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,//
0x80,0x80,0x80,0x00,//"_",63
0x80,0x8C,0x4C,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//"'",64
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x98,0xD8,0x44,//
0x64,0x24,0x24,0xFC,0xF8,0x00,0x00,0x00,0x0F,0x1F,0x18,0x10,0x10,0x10,0x08,0x1F,//
0x1F,0x10,0x18,0x00,//"a",65
0x20,0xE0,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0x18,//
0x08,0x04,0x04,0x0C,0xF8,0xF0,0x00,0x00,0x00,0x1F,0x0F,0x18,0x10,0x10,0x10,0x18,//
0x0F,0x03,0x00,0x00,//"b",66
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0xF8,0x18,0x04,//
0x04,0x04,0x3C,0x38,0x00,0x00,0x00,0x00,0x03,0x0F,0x0C,0x10,0x10,0x10,0x10,0x08,//
0x06,0x00,0x00,0x00,//"c",67
0x00,0x00,0x00,0x00,0x00,0x00,0x20,0xE0,0xF0,0x00,0x00,0x00,0xE0,0xF8,0x1C,0x04,//
0x04,0x04,0x08,0xFF,0xFF,0x00,0x00,0x00,0x03,0x0F,0x18,0x10,0x10,0x10,0x08,0x1F,//
0x0F,0x08,0x00,0x00,//"d",68
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0xF8,0x48,//
0x44,0x44,0x44,0x4C,0x78,0x70,0x00,0x00,0x00,0x03,0x0F,0x0C,0x18,0x10,0x10,0x10,//
0x08,0x04,0x00,0x00,//"e",69
0x00,0x00,0x00,0x80,0xC0,0x60,0x20,0x20,0xE0,0xC0,0x00,0x00,0x04,0x04,0x04,0xFF,//
0xFF,0x04,0x04,0x04,0x04,0x00,0x00,0x00,0x00,0x10,0x10,0x1F,0x1F,0x10,0x10,0x10,//
0x00,0x00,0x00,0x00,//"f",70
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x70,0xF8,0x8C,//
0x04,0x04,0x8C,0xF8,0x74,0x04,0x0C,0x00,0x70,0x76,0xCF,0x8D,0x8D,0x8D,0x89,0xC8,//
0x78,0x70,0x00,0x00,//"g",71
0x20,0xE0,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0x08,//
0x04,0x04,0x04,0xFC,0xF8,0x00,0x00,0x00,0x10,0x1F,0x1F,0x10,0x00,0x00,0x10,0x1F,//
0x1F,0x10,0x00,0x00,//"h",72
0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x04,0x04,//
0xFC,0xFC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x10,0x1F,0x1F,0x10,0x10,//
0x10,0x00,0x00,0x00,//"i",73
0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,//
0x04,0x04,0xFC,0xFC,0x00,0x00,0x00,0x00,0x00,0xC0,0xC0,0x80,0x80,0xC0,0x7F,0x3F,//
0x00,0x00,0x00,0x00,//"j",74
0x20,0xE0,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0x80,//
0xC0,0xF4,0x1C,0x04,0x04,0x00,0x00,0x00,0x10,0x1F,0x1F,0x11,0x00,0x03,0x1F,0x1C,//
0x10,0x10,0x00,0x00,//"k",75
0x00,0x20,0x20,0x20,0xE0,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x10,0x1F,0x1F,0x10,0x10,//
0x10,0x00,0x00,0x00,//"l",76
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFE,0xF8,0x06,//
0x02,0xFE,0xFC,0x06,0x02,0xFE,0xFE,0x00,0x00,0x07,0x07,0x00,0x00,0x07,0x07,0x00,//
0x00,0x07,0x07,0x00,//"m",77
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0xFC,0xFC,0x08,//
0x08,0x04,0x04,0xFC,0xF8,0x00,0x00,0x00,0x10,0x1F,0x1F,0x10,0x00,0x00,0x10,0x1F,//
0x1F,0x10,0x00,0x00,//"n",78
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0xF0,0x18,0x0C,//
0x04,0x04,0x0C,0x18,0xF0,0xE0,0x00,0x00,0x03,0x0F,0x0C,0x10,0x10,0x10,0x10,0x0C,//
0x0F,0x03,0x00,0x00,//"o",79
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0xFC,0xFC,0x08,//
0x04,0x04,0x04,0x0C,0xF8,0xF0,0x00,0x00,0x80,0xFF,0xFF,0x88,0x90,0x10,0x10,0x1C,//
0x0F,0x03,0x00,0x00,//"p",80
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0xF8,0x1C,0x04,//
0x04,0x04,0x08,0xF8,0xFC,0x00,0x00,0x00,0x03,0x0F,0x18,0x10,0x10,0x90,0x88,0xFF,//
0xFF,0x80,0x00,0x00,//"q",81
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x04,0xFC,0xFC,//
0x10,0x08,0x04,0x04,0x0C,0x0C,0x00,0x00,0x10,0x10,0x1F,0x1F,0x10,0x10,0x10,0x00,//
0x00,0x00,0x00,0x00,//"r",82
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x78,0xCC,//
0xC4,0x84,0x84,0x84,0x0C,0x1C,0x00,0x00,0x00,0x1E,0x18,0x10,0x10,0x10,0x11,0x19,//
0x0F,0x06,0x00,0x00,//"s",83
0x00,0x00,0x00,0x00,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x04,0x04,0xFF,//
0xFF,0x04,0x04,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x1F,0x10,0x10,0x10,//
0x0C,0x00,0x00,0x00,//"t",84
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0xFC,0xFE,0x00,//
0x00,0x00,0x04,0xFC,0xFE,0x00,0x00,0x00,0x00,0x0F,0x1F,0x18,0x10,0x10,0x08,0x1F,//
0x0F,0x08,0x00,0x00,//"u",85
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x0C,0x3C,0xFC,//
0xC4,0x00,0x00,0xC4,0x3C,0x0C,0x04,0x00,0x00,0x00,0x00,0x01,0x0F,0x1E,0x0E,0x01,//
0x00,0x00,0x00,0x00,//"v",86
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3C,0xFC,0xC4,0x00,//
0xE4,0x7C,0xFC,0x84,0x80,0x7C,0x04,0x00,0x00,0x07,0x1F,0x07,0x00,0x00,0x07,0x1F,//
0x07,0x00,0x00,0x00,//"w",87
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x04,0x1C,0x7C,//
0xE4,0xC0,0x34,0x1C,0x04,0x04,0x00,0x00,0x10,0x10,0x1C,0x16,0x01,0x13,0x1F,0x1C,//
0x18,0x10,0x00,0x00,//"x",88
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x0C,0x3C,0xFC,//
0xC4,0x00,0xC4,0x3C,0x04,0x04,0x00,0x00,0x00,0xC0,0x80,0xC1,0x37,0x0E,0x01,0x00,//
0x00,0x00,0x00,0x00,//"y",89
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x1C,0x04,0x04,//
0xC4,0xF4,0x7C,0x1C,0x04,0x00,0x00,0x00,0x00,0x10,0x1C,0x1F,0x17,0x11,0x10,0x10,//
0x18,0x0E,0x00,0x00,//"z",90
0x00,0x00,0x00,0x00,0x00,0x00,0xF8,0x0C,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x10,0x28,0xEF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x3F,0x60,//
0x40,0x00,0x00,0x00,//"{",91
0x00,0x00,0x00,0x00,0x00,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x00,0x00,//
0x00,0x00,0x00,0x00,//"|",92
0x00,0x04,0x0C,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xEF,//
0x28,0x10,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x40,0x60,0x3F,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//"}",93
0x18,0x06,0x02,0x02,0x04,0x08,0x10,0x20,0x20,0x30,0x08,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//
0x00,0x00,0x00,0x00,//"~",94
0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0xFF,0xFF,
0xFF,0xFE,0xFE,0x7C,0x7C,0x38,0x38,0x10,0x07,0x03,0x03,0x01,0x01,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,//"?",95,三角
0x00,0x00,0x00,0x00,0xC0,0xF0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x04,0x03,
0x01,0xFF,0x01,0x03,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0x00,0x00,
0x00,0x00,0x00,0x00,//"↑",96,上箭头
0x00,0x00,0x00,0x00,0x00,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0xC0,
0x80,0xFF,0x80,0xC0,0x20,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x0F,0x03,0x00,
0x00,0x00,0x00,0x00,//"↓",97,下箭头
};
// Gadugi
static const unsigned char F16X32[]=
{
0x00,0x00,0x00,0x00,0x80,0xC0,0xC0,0xC0,0xC0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,
0x00,0xF8,0xFE,0x07,0x01,0x01,0x00,0x00,0x00,0x00,0x01,0x07,0xFF,0xF8,0x00,0x00,
0x00,0x0F,0x3F,0xF0,0xC0,0x80,0x80,0x80,0x80,0x80,0xC0,0x70,0x3F,0x0F,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,//"0",0
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x80,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x06,0x07,0x03,0x03,0x01,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,//"1",1
0x00,0x00,0x80,0x80,0xC0,0xC0,0xC0,0xC0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x01,0x01,0x00,0x00,0x00,0x00,0x80,0xE1,0x7F,0x3E,0x00,0x00,0x00,0x00,
0x00,0xE0,0xF0,0x98,0x8C,0x86,0x82,0x83,0x81,0x80,0x80,0x80,0x80,0x00,0x00,0x00,
0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x00,0x00,0x00,//"2",2
0x00,0x00,0x00,0x80,0xC0,0xC0,0xC0,0xC0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x01,0xC0,0xC0,0xC0,0xC0,0xE0,0xA1,0xBF,0x1F,0x00,0x00,0x00,0x00,
0x00,0x00,0xE0,0xC0,0x80,0x80,0x80,0x80,0x80,0xC1,0xE3,0x7F,0x3E,0x00,0x00,0x00,
0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"3",3
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x80,0xC0,0x70,0x38,0x0E,0x07,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,
0x18,0x1E,0x1F,0x1B,0x19,0x18,0x18,0x18,0x18,0xFF,0xFF,0x18,0x18,0x18,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x01,0x00,0x00,0x00,0x00,0x00,//"4",4
0x00,0x00,0x00,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0x00,0x00,0x00,0x00,
0x00,0x00,0x7F,0x7F,0x60,0x60,0x60,0x60,0x60,0xC0,0xC0,0x80,0x00,0x00,0x00,0x00,
0x00,0x00,0xC0,0x80,0x80,0x80,0x80,0x80,0x80,0xC0,0xE1,0x7F,0x1F,0x00,0x00,0x00,
0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"5",5
0x00,0x00,0x00,0x00,0x00,0x80,0x80,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0x00,0x00,0x00,
0x00,0x00,0xF0,0xFC,0x8F,0xC3,0x61,0x60,0x60,0x60,0xE0,0xC0,0x81,0x00,0x00,0x00,
0x00,0x00,0x0F,0x7F,0xF1,0xC0,0x80,0x80,0x80,0x80,0xC0,0xE1,0x7F,0x1F,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x00,0x00,0x00,0x00,0x00,//"6",6
0x00,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xE0,0x78,0x1E,0x07,0x03,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x80,0xF8,0x7E,0x07,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"7",7
0x00,0x00,0x00,0x80,0x80,0xC0,0xC0,0xC0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x1E,0xBF,0xE1,0xC0,0xC0,0xC0,0xC0,0xE1,0xBF,0x1E,0x00,0x00,0x00,0x00,
0x00,0x3C,0x7F,0xE3,0xC1,0x80,0x80,0x80,0x80,0xC1,0xE3,0x7F,0x3C,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,//"8",8
0x00,0x00,0x00,0x80,0x80,0xC0,0xC0,0xC0,0xC0,0xC0,0x80,0x00,0x00,0x00,0x00,0x00,
0x00,0x7C,0xFF,0xC3,0x81,0x00,0x00,0x00,0x00,0x81,0xC3,0xFF,0xFC,0x00,0x00,0x00,
0x00,0x00,0xC0,0x81,0x83,0x83,0x83,0x83,0xC3,0xE1,0x78,0x1F,0x07,0x00,0x00,0x00,
0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"9",9
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//".",10
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//" ",11
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"-",12
};
/*
 0(0) 1(1) 2(2) 3(3) 4(4) 5(5) 6(6) 7(7) 8(8) 9(9) c(10) h(11) i(12) m(13) n(14)  (15)
 .(16) */
// Consol
// Gagudi 64x64 鍒楄寮忓彇妯?
static unsigned char F32X64[]=
{

0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xC0,0xC0,0xC0,0xE0,0xE0,0xE0,0xE0,
0xE0,0xE0,0xE0,0xC0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0xC0,0xF0,0xFC,0xFE,0xFF,0x3F,0x0F,0x07,0x03,0x03,0x01,0x01,0x01,
0x01,0x01,0x03,0x07,0x0F,0x3F,0xFF,0xFF,0xFE,0xF8,0xE0,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0xFC,0xFF,0xFF,0xFF,0xFF,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x03,0xFF,0xFF,0xFF,0xFF,0xFE,0x00,0x00,0x00,0x00,
0x00,0x00,0xFF,0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xFF,0xFF,0xFF,0xFF,0x7F,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x07,0x3F,0x7F,0xFF,0xFF,0xF8,0xE0,0xC0,0x80,0x00,0x00,0x00,0x00,
0x00,0x00,0x80,0xC0,0xE0,0xF8,0xFF,0xFF,0x7F,0x1F,0x07,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x03,0x03,0x07,0x07,0x0F,0x0F,0x0F,0x0F,0x0F,
0x0F,0x0F,0x07,0x07,0x07,0x03,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"0",0
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x80,
0xC0,0xE0,0xE0,0xE0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x78,0x7C,0x3C,0x3C,0x1E,0x1E,0x0F,0x0F,0x07,0xFF,
0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,
0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,
0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,
0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,
0x07,0x07,0x07,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"1",1
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x80,0xC0,0xC0,0xE0,0xE0,0xE0,0xE0,0xE0,0xE0,
0xE0,0xE0,0xE0,0xC0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x1F,0x0F,0x07,0x07,0x03,0x03,0x01,0x01,0x01,0x01,0x01,
0x01,0x03,0x03,0x07,0x1F,0xFF,0xFF,0xFF,0xFC,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x80,0xC0,0xE0,0xF8,0xFF,0xFF,0x7F,0x1F,0x07,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xC0,0xE0,0xE0,0xF0,0xF8,0x7C,0x3C,0x3E,
0x1F,0x0F,0x0F,0x07,0x03,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0xE0,0xF8,0xFE,0xFF,0xFF,0x8F,0x87,0x81,0x81,0x80,0x80,0x80,0x80,
0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x80,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x07,
0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x07,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"2",2
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x80,0xC0,0xC0,0xC0,0xE0,0xE0,0xE0,0xE0,0xE0,0xE0,0xE0,
0xE0,0xE0,0xC0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x07,0x07,0x03,0x03,0x01,0x01,0x01,0x01,0x01,0x01,0x01,
0x03,0x03,0x07,0x0F,0xFF,0xFF,0xFF,0xFE,0xF8,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xE0,
0xE0,0xF0,0x78,0x7C,0x3F,0x3F,0x1F,0x0F,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x07,
0x07,0x07,0x0F,0x1F,0x3E,0xFE,0xFC,0xF8,0xF0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0xE0,0xC0,0x80,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x80,0x80,0xC0,0xE0,0xF0,0xFF,0xFF,0xFF,0x3F,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x03,0x07,0x07,0x07,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,
0x0F,0x07,0x07,0x07,0x03,0x01,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"3",3
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x80,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xC0,0xF0,0xF8,
0x7E,0x3F,0x0F,0xFF,0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xC0,0xE0,0xF8,0xFC,0x3E,0x1F,0x0F,0x03,0x01,
0x00,0x00,0x00,0xFF,0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0xC0,0xE0,0xF0,0xFC,0xFE,0xFF,0xCF,0xC7,0xC3,0xC1,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,
0xC0,0xC0,0xC0,0xFF,0xFF,0xFF,0xFF,0xFF,0xC0,0xC0,0xC0,0xC0,0xC0,0x00,0x00,0x00,
0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,
0x03,0x03,0x03,0xFF,0xFF,0xFF,0xFF,0xFF,0x03,0x03,0x03,0x03,0x03,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x07,0x07,0x07,0x07,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"4",4
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,
0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0xFF,0xFF,0xFF,0x3F,0x03,0x03,0x03,0x03,0x03,
0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0xFC,0xFF,0xFF,0xFF,0xFF,0xE0,0xE0,0xE0,0xE0,0xE0,0xE0,
0xE0,0xE0,0xC0,0xC0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,
0x03,0x03,0x03,0x07,0x0F,0x3F,0xFF,0xFF,0xFE,0xFC,0xE0,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xC0,0x80,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x80,0x80,0x80,0xC0,0xE0,0xF8,0xFF,0xFF,0x7F,0x3F,0x0F,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x07,0x07,0x07,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,
0x0F,0x07,0x07,0x07,0x03,0x03,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"5",5
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x80,0xC0,0xC0,0xE0,
0xE0,0xE0,0xE0,0xE0,0xE0,0xE0,0xE0,0xE0,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0xC0,0xF0,0xF8,0xFC,0xFE,0x3F,0x1F,0x0F,0x07,0x03,0x03,
0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x01,0x03,0x03,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0xE0,0xFE,0xFF,0xFF,0xFF,0x07,0x80,0xC0,0xE0,0xE0,0xF0,0xF0,0xF0,
0xF0,0xF0,0xF0,0xF0,0xF0,0xE0,0xE0,0xC0,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0xFF,0xFF,0xFF,0xFF,0xFF,0x1F,0x07,0x03,0x01,0x01,0x00,0x00,0x00,
0x00,0x00,0x00,0x01,0x03,0x07,0x1F,0xFF,0xFF,0xFF,0xFE,0xF0,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x0F,0x3F,0xFF,0xFF,0xFC,0xF0,0xC0,0x80,0x80,0x00,0x00,0x00,
0x00,0x00,0x00,0x80,0xC0,0xE0,0xF8,0xFF,0xFF,0x7F,0x3F,0x07,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0x03,0x03,0x07,0x07,0x0F,0x0F,0x0F,0x0F,
0x0F,0x0F,0x0F,0x07,0x07,0x07,0x03,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"6",6
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,
0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0xC0,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,0x03,
0x03,0x03,0x03,0x83,0xE3,0xFB,0xFF,0x7F,0x1F,0x07,0x01,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,
0xF0,0xFC,0xFF,0xFF,0x3F,0x07,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0xF8,0xFE,0xFF,
0xFF,0x1F,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0xFE,0xFF,0xFF,0xFF,0x07,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x07,0x07,0x07,0x07,0x07,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"7",7
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x80,0xC0,0xC0,0xE0,0xE0,0xE0,0xE0,0xE0,
0xE0,0xE0,0xE0,0xC0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0xF0,0xFC,0xFF,0xFF,0xFF,0x1F,0x07,0x03,0x01,0x01,0x01,0x01,
0x01,0x03,0x03,0x07,0x1F,0xFF,0xFF,0xFF,0xFC,0xF0,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x01,0x07,0x1F,0x1F,0xBF,0xFE,0xF8,0xF0,0xF0,0xE0,0xE0,0xE0,
0xE0,0xF0,0xF0,0xF8,0xFE,0xBF,0x1F,0x1F,0x07,0x01,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0xC0,0xF0,0xFC,0xFE,0xFF,0x3F,0x0F,0x07,0x03,0x03,0x01,0x01,0x01,0x01,
0x01,0x01,0x03,0x03,0x07,0x0F,0x3F,0xFF,0xFE,0xFC,0xF0,0xC0,0x00,0x00,0x00,0x00,
0x00,0x00,0x0F,0x3F,0xFF,0xFF,0xFF,0xF8,0xE0,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,
0x00,0x00,0x80,0x80,0xC0,0xE0,0xF8,0xFF,0xFF,0xFF,0x3F,0x0F,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x01,0x03,0x03,0x07,0x07,0x07,0x0F,0x0F,0x0F,0x0F,0x0F,
0x0F,0x0F,0x0F,0x07,0x07,0x07,0x03,0x03,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"8",8
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0xC0,0xC0,0xC0,0xE0,0xE0,0xE0,0xE0,
0xE0,0xE0,0xE0,0xC0,0xC0,0xC0,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0xC0,0xF8,0xFC,0xFE,0xFF,0x3F,0x0F,0x07,0x03,0x03,0x01,0x01,0x01,
0x01,0x01,0x03,0x03,0x07,0x1F,0x7F,0xFF,0xFE,0xF8,0xE0,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x1F,0xFF,0xFF,0xFF,0xFF,0xF0,0xC0,0x80,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x80,0xC0,0xF0,0xFF,0xFF,0xFF,0xFF,0xFF,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x01,0x03,0x07,0x0F,0x0F,0x0F,0x1F,0x1F,0x1E,0x1E,0x1E,
0x1E,0x1E,0x0F,0x0F,0x07,0x03,0xC1,0xFF,0xFF,0xFF,0xFF,0x1F,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0xC0,0x80,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,
0x80,0xC0,0xC0,0xE0,0xF8,0xFC,0xFF,0x7F,0x1F,0x07,0x01,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x07,0x07,0x07,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,0x0F,
0x07,0x07,0x07,0x03,0x03,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"9",9
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x80,0xC0,0xC0,0xC0,0xC0,0x80,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x07,0x0F,0x0F,0x0F,0x0F,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//".",10
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//" ",11
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x38,0x38,0x38,0x38,0x38,0x38,0x38,0x38,0x38,0x38,0x38,0x38,
0x38,0x38,0x38,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,//"-",12
};

/*
鏍″噯鎸夐璁惧０閫熸棤绾夸寒搴﹀崟浣嶅浗闄呰嫳鍒惰瑷€涓枃宸插紑鍚叧闂垚鍔熷け璐ュ綋鍓嶉搧涓嶉攬閽㈤摑閾滈晬鍚堥噾纰抽摳宸ヤ笟绾挍鐧介敯搴烽粍璇曞潡鍘氬害璇烽敭浣庡瓨鍌ㄤ负鍙傛暟鏄惁纭鍙栨秷骞冲潎闈掔传鏉愭枡闆剁偣
 鏍?0) 鍑?1) 鎸?2) 棰?3) 璁?4) 澹?5) 閫?6) 鏃?7) 绾?8) 浜?9) 搴?10) 鍗?11) 浣?12) 鍥?13) 闄?14) 鑻?15)
 鍒?16) 璇?17) 瑷€(18) 涓?19) 鏂?20) 宸?21) 寮€(22) 鍚?23) 鍏?24) 闂?25) 鎴?26) 鍔?27) 澶?28) 璐?29) 褰?30) 鍓?31)
 閾?32) 涓?33) 閿?34) 閽?35) 閾?36) 閾?37) 闀?38) 鍚?39) 閲?40) 闆?78) 鐐?79)
*/
// 榛戜綋 24x24 閫愯寮忓彇妯?
static unsigned char  Hzk[]={
0x00,0x00,0x00,0x00,0x80,0x00,0x60,0x80,0x01,0x20,0x80,0x01,0x20,0x80,0x00,0x20,//
0xFE,0x7F,0x20,0x06,0x40,0xFE,0x01,0x00,0x30,0x60,0x0C,0x30,0x30,0x1C,0x30,0x18,//
0x38,0xB8,0x0C,0x30,0xF8,0x09,0x00,0xAC,0x31,0x0C,0x2C,0x31,0x0E,0x2E,0x60,0x06,//
0x26,0x60,0x03,0x24,0xC0,0x03,0x20,0xC0,0x01,0x20,0xE0,0x03,0x20,0x70,0x0F,0x60,//
0x1E,0x7C,0x60,0x0E,0x30,0x00,0x00,0x00,//"鏍?,0
0x00,0x00,0x00,0x00,0x80,0x00,0x00,0xDC,0x01,0x04,0x8C,0x03,0x0E,0x0C,0x01,0x1C,//
0x06,0x00,0x38,0xFE,0x3F,0x30,0x87,0x01,0x00,0x87,0x01,0x80,0x87,0x01,0xC0,0xFF,//
0x1F,0x80,0xFE,0x1F,0x00,0x86,0x01,0x30,0x86,0x01,0x30,0x86,0x01,0x18,0xFE,0x1F,//
0x18,0x86,0x01,0x18,0x86,0x01,0x0C,0x86,0x01,0x0E,0x86,0x01,0x06,0xFE,0x7F,0x00,//
0x06,0x00,0x00,0x06,0x00,0x00,0x00,0x00,//"鍑?,1
0x00,0x00,0x00,0x00,0x00,0x00,0x30,0xC0,0x00,0x30,0x80,0x01,0x30,0x80,0x00,0x30,//
0xFC,0x3F,0x32,0xFC,0x3F,0xFE,0x04,0x30,0x30,0xC4,0x30,0x30,0xC4,0x00,0x30,0x60,//
0x00,0x30,0x60,0x00,0xF0,0xFF,0x7F,0x70,0x30,0x0C,0x3E,0x30,0x0C,0x36,0x18,0x04,//
0x30,0x18,0x06,0x30,0x78,0x03,0x30,0xC0,0x03,0x30,0xC0,0x07,0x30,0xF0,0x1E,0x3C,//
0x3F,0x38,0x38,0x0F,0x30,0x00,0x00,0x00,//"鎸?,2
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,0xFB,0x3F,0xFC,0x83,0x01,0x80,//
0x81,0x01,0xC0,0x80,0x00,0x78,0xF0,0x1F,0x70,0xF0,0x1F,0x60,0x10,0x10,0xFE,0x17,//
0x13,0xFE,0x13,0x11,0x60,0x13,0x11,0x60,0x13,0x11,0x60,0x90,0x11,0x60,0x90,0x11,//
0x60,0x90,0x11,0x60,0x90,0x11,0x60,0xC0,0x00,0x60,0xC0,0x06,0x60,0x60,0x1C,0x3C,//
0x38,0x38,0x18,0x1C,0x20,0x00,0x00,0x00,//"棰?,3
0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x38,0xF8,0x03,0x70,0x0C,0x03,0x60,//
0x0C,0x03,0x00,0x0C,0x03,0x00,0x0C,0x03,0x00,0x0E,0x03,0x3E,0x07,0x3F,0x3E,0x03,//
0x3E,0x30,0x00,0x00,0x30,0xFE,0x0F,0x30,0x0C,0x0C,0x30,0x08,0x06,0x30,0x18,0x06,//
0x30,0x18,0x03,0xB0,0xB1,0x03,0xF0,0xE1,0x01,0xF0,0xE0,0x00,0x70,0xF8,0x03,0x30,//
0x1E,0x7E,0x80,0x07,0x38,0x00,0x00,0x00,//"璁?,4
0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0xFC,0xFF,0x3F,0xFC,0xFF,0x3F,0x00,//
0x18,0x00,0x00,0x18,0x00,0xF0,0xFF,0x1F,0xF0,0xFF,0x1F,0x00,0x00,0x00,0xF0,0xFF,//
0x1F,0xF0,0xFF,0x1F,0x30,0x18,0x18,0x30,0x18,0x18,0x30,0x18,0x18,0x30,0x18,0x18,//
0xF0,0xFF,0x1F,0x30,0x00,0x18,0x30,0x00,0x00,0x18,0x00,0x00,0x1C,0x00,0x00,0x0C,//
0x00,0x00,0x0C,0x00,0x00,0x00,0x00,0x00,//"澹?,5
0x00,0x00,0x00,0x00,0xC0,0x00,0x00,0xC0,0x00,0x18,0xC0,0x00,0x98,0xFF,0x3F,0x38,//
0xC0,0x00,0x30,0xC0,0x00,0x00,0xFF,0x1F,0x00,0xFF,0x1F,0x00,0xC3,0x18,0x3E,0xC3,//
0x18,0x3E,0xFF,0x1F,0x30,0xFF,0x1F,0x30,0xF0,0x01,0x30,0xD8,0x03,0x30,0xDC,0x0E,//
0x30,0xCE,0x1C,0x30,0xC7,0x38,0x30,0xC3,0x10,0x30,0xC0,0x00,0xFC,0x01,0x00,0x8E,//
0xFF,0x7F,0x04,0xFC,0x7F,0x00,0x00,0x00,//"閫?,6
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0xFF,0x0F,0xF0,0xFF,0x0F,0x00,//
0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0xFC,0xFF,//
0x3F,0xFC,0xFF,0x3F,0x00,0x64,0x00,0x00,0x66,0x00,0x00,0x66,0x00,0x00,0x66,0x00,//
0x00,0x63,0x00,0x80,0x63,0x00,0xC0,0x61,0x30,0xE0,0x60,0x30,0x70,0x60,0x30,0x3C,//
0xC0,0x3F,0x1C,0x00,0x00,0x00,0x00,0x00,//"鏃?,7
0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x70,0x60,0x07,0x30,0x60,0x06,0x18,//
0x60,0x04,0x98,0x61,0x10,0x8C,0xE1,0x1F,0xC6,0xFE,0x01,0xFE,0x7C,0x00,0x66,0x60,//
0x20,0x20,0x60,0x7F,0x30,0xFE,0x07,0x18,0x5C,0x08,0x9C,0xC1,0x1C,0xFC,0xC1,0x0C,//
0x04,0xC0,0x07,0x00,0x80,0x03,0xC0,0xC3,0x03,0xFE,0xE1,0x63,0x0C,0x38,0x66,0x00,//
0x1E,0x3C,0x00,0x04,0x38,0x00,0x00,0x00,//"绾?,8
0x00,0x00,0x00,0x00,0x08,0x00,0x00,0x18,0x00,0xFC,0xFF,0x3F,0xFC,0xFF,0x3F,0x00,//
0x00,0x00,0x00,0x00,0x00,0xE0,0xFF,0x07,0xC0,0x00,0x06,0xC0,0x00,0x06,0xE0,0xFF,//
0x07,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,0xFF,0x3F,0x0C,0x00,0x30,0x0C,0x00,0x30,//
0x00,0xFF,0x00,0x00,0xC3,0x00,0x00,0xC3,0x00,0x80,0xC1,0x00,0xC0,0xC1,0x70,0xF8,//
0xC0,0x3F,0x78,0x80,0x1F,0x00,0x00,0x00,//"浜?,9
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x00,0x30,0x00,0xF8,0xFF,0x1F,0xF8,//
0xFF,0x1F,0x18,0x06,0x03,0x18,0x06,0x03,0x18,0x06,0x03,0xD8,0xFF,0x1F,0x18,0x06,//
0x03,0x18,0x06,0x03,0x18,0xFE,0x03,0x18,0xFE,0x03,0x18,0x00,0x00,0x98,0xFF,0x07,//
0x18,0x06,0x03,0x18,0x0C,0x03,0x08,0x98,0x01,0x0C,0xF8,0x00,0x0C,0xF0,0x00,0x06,//
0xDF,0x1F,0xE4,0x07,0x3E,0x00,0x00,0x00,//"搴?,10
0x00,0x00,0x00,0x00,0x80,0x00,0x80,0x81,0x01,0x80,0xC1,0x01,0x00,0xC3,0x00,0x80,//
0xC0,0x00,0xF0,0xFF,0x0F,0x30,0x18,0x0C,0x30,0x18,0x0C,0x30,0x18,0x0C,0xF0,0xFF,//
0x0F,0x30,0x18,0x0C,0x30,0x18,0x0C,0x30,0x18,0x0C,0xF0,0xFF,0x0F,0x00,0x18,0x00,//
0x00,0x18,0x00,0x04,0x18,0x60,0xFC,0xFF,0x7F,0x00,0x18,0x00,0x00,0x18,0x00,0x00,//
0x18,0x00,0x00,0x18,0x00,0x00,0x00,0x00,//"鍗?,11
0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0x60,0x00,0x60,0xE0,0x00,0x60,0xC0,0x00,0x70,//
0x40,0x00,0x30,0xFE,0x3F,0x38,0xFE,0x3F,0x38,0x00,0x00,0x3C,0x00,0x04,0x3C,0x0C,//
0x0E,0x36,0x0C,0x06,0x36,0x0C,0x06,0x30,0x18,0x06,0x30,0x18,0x06,0x30,0x18,0x03,//
0x30,0x30,0x03,0x30,0x30,0x03,0x30,0x10,0x03,0x30,0x80,0x01,0x30,0xFF,0x7F,0x30,//
0xFF,0x7F,0x30,0x00,0x00,0x00,0x00,0x00,//"浣?,12
0x00,0x00,0x00,0x00,0x00,0x00,0xFC,0xFF,0x3F,0xFC,0xFF,0x3F,0x0C,0x00,0x30,0x0C,//
0x00,0x30,0xCC,0xFF,0x33,0xCC,0xFF,0x33,0x0C,0x18,0x30,0x0C,0x18,0x30,0x0C,0x18,//
0x30,0x8C,0xFF,0x31,0x0C,0x18,0x30,0x0C,0x98,0x30,0x0C,0xD8,0x31,0x0C,0x98,0x30,//
0x0C,0x18,0x30,0xCC,0xFF,0x33,0x0C,0x00,0x30,0x0C,0x00,0x30,0x0C,0x00,0x30,0xFC,//
0xFF,0x3F,0x0C,0x00,0x30,0x00,0x00,0x00,//"鍥?,13
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,0xF9,0x1F,0x8C,0xF9,0x1F,0x8C,//
0x00,0x00,0xCC,0x00,0x00,0xCC,0x00,0x00,0xCC,0x00,0x00,0x6C,0xFE,0x7F,0x6C,0x80,//
0x01,0xCC,0x80,0x01,0xCC,0x80,0x01,0x8C,0x99,0x09,0x8C,0x99,0x1D,0x8C,0x8D,0x19,//
0xFC,0x8D,0x39,0xEC,0x8C,0x31,0x0C,0x86,0x71,0x0C,0x86,0x61,0x0C,0x82,0x21,0x0C,//
0xE0,0x01,0x0C,0xE0,0x00,0x00,0x00,0x00,//"闄?,14
0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x81,0x01,0x80,0x81,0x01,0xFC,0xFF,0x3F,0xFC,//
0xFF,0x3F,0x80,0x81,0x01,0x80,0x99,0x01,0x00,0x18,0x00,0x00,0x18,0x00,0xE0,0xFF,//
0x07,0x60,0x18,0x06,0x60,0x18,0x06,0x60,0x18,0x06,0x60,0x18,0x06,0xFE,0xFF,0x3F,//
0x00,0x3C,0x00,0x00,0x6C,0x00,0x00,0xC6,0x00,0x00,0x83,0x03,0xC0,0x01,0x0F,0xFC,//
0x00,0x3E,0x38,0x00,0x18,0x00,0x00,0x00,//"鑻?,15
0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x01,0x30,0x88,0x01,0x30,0x8C,0x01,0x30,0xFC,//
0x3F,0x31,0xFC,0x3F,0x31,0x86,0x01,0x31,0x84,0x01,0x31,0x80,0x01,0x31,0xFE,0x3F,//
0x31,0x80,0x01,0x31,0x80,0x01,0x31,0x80,0x01,0x31,0xFC,0x3F,0x31,0x8C,0x31,0x31,//
0x8C,0x31,0x31,0x8C,0x31,0x33,0x8C,0x31,0x30,0x8C,0x3D,0x30,0x8C,0x1D,0x30,0x80,//
0x01,0x3C,0x80,0x01,0x1C,0x00,0x00,0x00,//"鍒?,16
0x00,0x00,0x00,0x00,0x00,0x00,0x18,0xFF,0x3F,0x38,0x40,0x00,0x30,0x60,0x00,0x20,//
0x60,0x00,0x00,0xFE,0x1F,0x00,0xE2,0x1F,0x00,0x20,0x18,0x3E,0x30,0x0C,0x30,0x30,//
0x0C,0x30,0xFF,0x7F,0x30,0xFF,0x7F,0x30,0x00,0x00,0x30,0x00,0x00,0x30,0xFC,0x1F,//
0xB0,0x0C,0x18,0xF0,0x0D,0x18,0xF0,0x0C,0x18,0x70,0x0C,0x18,0x30,0xFC,0x1F,0x30,//
0xFC,0x1F,0x00,0x0C,0x18,0x00,0x00,0x00,//"璇?,17
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x00,0x1C,0x00,0xFC,0xFF,0x3F,0xFC,//
0xFF,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0xFF,0x07,0xF0,0xFF,0x07,0x00,0x00,//
0x00,0x00,0x00,0x00,0xF0,0xFF,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0xFF,0x07,//
0xE0,0xFF,0x07,0x20,0x00,0x06,0x20,0x00,0x06,0x20,0x00,0x06,0xE0,0xFF,0x07,0xE0,//
0xFF,0x07,0x20,0x00,0x06,0x00,0x00,0x00,//"瑷€",18
0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,//
0x18,0x00,0xF8,0xFF,0x1F,0xF8,0xFF,0x1F,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,//
0x18,0x18,0x18,0x18,0x18,0x18,0x18,0xF8,0xFF,0x1F,0xF8,0xFF,0x1F,0x18,0x18,0x18,//
0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,//
0x18,0x00,0x00,0x18,0x00,0x00,0x00,0x00,//"涓?,19
0x00,0x00,0x00,0x00,0x04,0x00,0x00,0x06,0x00,0x00,0x0C,0x00,0x00,0x1C,0x00,0x00,//
0x18,0x00,0xFE,0xFF,0x7F,0xFE,0xFF,0x7F,0xC0,0x80,0x01,0xC0,0x80,0x01,0xC0,0x80,//
0x01,0x80,0x80,0x01,0x80,0xC1,0x00,0x80,0xC1,0x00,0x00,0x63,0x00,0x00,0x76,0x00,//
0x00,0x3E,0x00,0x00,0x1C,0x00,0x00,0x3E,0x00,0x00,0xE7,0x00,0xC0,0xC1,0x07,0xFC,//
0x00,0x7F,0x3C,0x00,0x3C,0x00,0x00,0x00,//"鏂?,20
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,0xFF,0x0F,0xFC,0xFF,0x0F,0x00,//
0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x38,0x00,0x0C,0x18,0x00,0x0C,0x18,0x00,//
0x0C,0xF8,0xFF,0x0F,0x18,0x00,0x0C,0x18,0x00,0x0C,0x18,0x00,0x00,0x18,0x00,0x00,//
0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x30,0x18,0x00,0x30,0x38,0x00,0x38,0xF0,//
0xFF,0x1F,0xE0,0xFF,0x0F,0x00,0x00,0x00,//"宸?,21
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF8,0xFF,0x1F,0x00,0x83,0x01,0x00,//
0x83,0x01,0x00,0x83,0x01,0x00,0x83,0x01,0x00,0x83,0x01,0x00,0x83,0x01,0x00,0x83,//
0x01,0xFE,0xFF,0x7F,0xFE,0xFF,0x7F,0x00,0x83,0x01,0x00,0x83,0x01,0x00,0x83,0x01,//
0x80,0x81,0x01,0x80,0x81,0x01,0xC0,0x80,0x01,0xE0,0x80,0x01,0x70,0x80,0x01,0x3C,//
0x80,0x01,0x18,0x80,0x01,0x00,0x00,0x00,//"寮€",22
0x00,0x00,0x00,0x00,0x10,0x00,0x00,0x38,0x00,0x00,0x30,0x00,0xE0,0xFF,0x0F,0xE0,//
0xFF,0x0F,0x20,0x00,0x0C,0x20,0x00,0x0C,0x20,0x00,0x0C,0x20,0x00,0x0C,0xE0,0xFF,//
0x0F,0x20,0x00,0x00,0x30,0x00,0x00,0x30,0x00,0x00,0xB0,0xFF,0x1F,0xB0,0xFF,0x1F,//
0xB0,0x01,0x18,0x98,0x01,0x18,0x98,0x01,0x18,0x98,0x01,0x18,0x8C,0xFF,0x1F,0x8E,//
0x01,0x18,0x84,0x01,0x18,0x00,0x00,0x00,//"鍚?,23
0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x81,0x01,0x80,0x81,0x01,0x00,0xC3,0x00,0x00,//
0xC3,0x00,0x00,0x60,0x00,0xF8,0xFF,0x1F,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,//
0x00,0x00,0x18,0x00,0x00,0x18,0x00,0xFC,0xFF,0x3F,0xFC,0xFF,0x3F,0x00,0x3C,0x00,//
0x00,0x7C,0x00,0x00,0xE6,0x00,0x00,0xC7,0x01,0x80,0x83,0x07,0xE0,0x00,0x3F,0x7C,//
0x00,0x3C,0x18,0x00,0x10,0x00,0x00,0x00,//"鍏?,24
0x00,0x00,0x00,0x20,0x00,0x00,0x70,0x0C,0x00,0x60,0xFC,0x3F,0xC0,0x00,0x30,0x00,//
0x00,0x30,0x0C,0x60,0x30,0x0C,0x60,0x30,0x0C,0x60,0x30,0xEC,0xFF,0x33,0xEC,0xFF,//
0x33,0x0C,0x78,0x30,0x0C,0x78,0x30,0x0C,0x6C,0x30,0x0C,0x66,0x30,0x0C,0x67,0x30,//
0x8C,0x63,0x30,0xCC,0x61,0x30,0xFC,0x60,0x30,0x0C,0x70,0x30,0x0C,0x7C,0x30,0x0C,//
0x18,0x3E,0x0C,0x00,0x1C,0x00,0x00,0x00,//"闂?,25
0x00,0x00,0x00,0x00,0x20,0x00,0x00,0x60,0x06,0x00,0x60,0x0C,0x00,0x60,0x18,0x00,//
0x60,0x08,0xF8,0xFF,0x3F,0xF8,0xFF,0x3F,0x18,0x60,0x00,0x18,0x60,0x08,0x18,0x40,//
0x18,0xF8,0x47,0x1C,0xF8,0xC7,0x0C,0x18,0xC6,0x06,0x18,0xC6,0x06,0x18,0x86,0x03,//
0x18,0x86,0x03,0x18,0x86,0x01,0x18,0xC6,0x23,0xCC,0x63,0x67,0x8C,0x39,0x6E,0x0E,//
0x1C,0x3C,0x04,0x0E,0x38,0x00,0x00,0x00,//"鎴?,26
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x00,0x00,0xC0,0x00,0x00,0xC0,0x00,0xFC,//
0xC3,0x00,0x60,0xC0,0x00,0x60,0xFC,0x3F,0x60,0xFC,0x3F,0x60,0xC0,0x30,0x60,0xC0,//
0x30,0x60,0xC0,0x30,0x60,0xC0,0x30,0x60,0xC0,0x30,0x60,0x40,0x30,0x60,0x60,0x30,//
0xE0,0x67,0x10,0xFC,0x33,0x10,0x3E,0x30,0x18,0x00,0x18,0x18,0x00,0x0C,0x1C,0x00,//
0x0E,0x1F,0x00,0x06,0x02,0x00,0x00,0x00,//"鍔?,27
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0xE0,0x18,0x00,0x60,//
0x18,0x00,0x20,0x18,0x00,0xF0,0xFF,0x0F,0xF0,0xFF,0x0F,0x18,0x18,0x00,0x0C,0x18,//
0x00,0x08,0x18,0x00,0x00,0x18,0x00,0xFC,0xFF,0x3F,0x00,0x1C,0x00,0x00,0x3C,0x00,//
0x00,0x66,0x00,0x00,0xE6,0x00,0x00,0xC3,0x01,0xC0,0x81,0x07,0xE0,0x00,0x3E,0x7C,//
0x00,0x3C,0x1C,0x00,0x00,0x00,0x00,0x00,//"澶?,28
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x01,0x00,0x80,0x01,0xFC,0x8F,0x00,0x0C,//
0xCC,0x00,0x0C,0xCC,0x7F,0xCC,0xCC,0x7F,0xCC,0x6C,0x18,0xCC,0x6C,0x18,0xCC,0x7C,//
0x08,0xCC,0x7C,0x08,0xCC,0xCC,0x08,0xCC,0xCC,0x0C,0xCC,0x8C,0x0C,0x4C,0x8C,0x05,//
0x4C,0x8C,0x07,0x60,0x01,0x07,0xA0,0x03,0x03,0x30,0x86,0x07,0x18,0xCC,0x0C,0x0E,//
0x74,0x38,0x04,0x38,0x30,0x00,0x00,0x00,//"璐?,29
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x10,0x18,0x08,0x38,0x18,0x1C,0x70,//
0x18,0x0C,0xE0,0x18,0x06,0xC0,0x19,0x07,0x40,0x18,0x02,0x00,0x18,0x00,0xF8,0xFF,//
0x1F,0xF8,0xFF,0x1F,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0xF8,0xFF,0x1F,//
0xF8,0xFF,0x1F,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0xF8,0xFF,0x1F,0x08,//
0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x00,//"褰?,30
0x00,0x00,0x00,0x00,0x01,0x01,0x80,0x01,0x03,0x00,0x83,0x01,0x00,0x81,0x01,0xFE,//
0xFF,0x7F,0xFE,0xFF,0x7F,0x00,0x00,0x00,0x00,0x00,0x18,0xF8,0xCF,0x18,0x18,0xCC,//
0x18,0x18,0xCC,0x18,0x18,0xCC,0x18,0xF8,0xCF,0x18,0x18,0xCC,0x18,0x18,0xCC,0x18,//
0xF8,0xCF,0x18,0xF8,0xCF,0x18,0x18,0xCC,0x18,0x18,0x0C,0x18,0x18,0x0C,0x18,0x18,//
0x0F,0x1E,0x18,0x07,0x0E,0x00,0x00,0x00,//"鍓?,31
0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x80,0x01,0x30,0x80,0x01,0x10,0x90,0x01,0xF8,//
0x9B,0x01,0xFC,0xFB,0x1F,0x0C,0xF8,0x1F,0x06,0x8C,0x01,0xFE,0x8D,0x01,0xFC,0x81,//
0x01,0x30,0x80,0x01,0x30,0xFC,0x3F,0xFC,0xFD,0x3F,0xFC,0x81,0x03,0x30,0xC0,0x03,//
0x30,0xC0,0x06,0x30,0x61,0x06,0xB0,0x63,0x0C,0xF0,0x31,0x1C,0x70,0x18,0x38,0x30,//
0x0E,0x70,0x00,0x06,0x20,0x00,0x00,0x00,//"閾?,32
0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x20,0xFC,0xFF,0x3F,0x00,0x30,0x00,0x00,//
0x18,0x00,0x00,0x18,0x00,0x00,0x0C,0x00,0x00,0x0E,0x00,0x00,0xCF,0x00,0x00,0x8F,//
0x01,0x80,0x0D,0x03,0xE0,0x0C,0x06,0x70,0x0C,0x0C,0x38,0x0C,0x1C,0x1E,0x0C,0x38,//
0x04,0x0C,0x10,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,//
0x0C,0x00,0x00,0x0C,0x00,0x00,0x00,0x00,//"涓?,33
0x00,0x00,0x00,0x00,0x00,0x08,0x70,0x80,0x1F,0x30,0xF8,0x07,0x10,0x88,0x01,0xF8,//
0x81,0x01,0xF8,0xFD,0x3F,0x0C,0xFC,0x3F,0x0C,0xA0,0x07,0xFE,0xB1,0x07,0xF8,0x99,//
0x0D,0x30,0x8C,0x39,0x30,0x86,0x71,0x30,0xF9,0x07,0xFC,0xF9,0x07,0x30,0x60,0x06,//
0x30,0x60,0x3E,0x30,0x20,0x3E,0x30,0x31,0x10,0xF0,0x31,0x10,0xF0,0x18,0x18,0x30,//
0x0C,0x1C,0x00,0x0E,0x0E,0x00,0x00,0x02,//"閿?,34
0x00,0x00,0x00,0x20,0x00,0x00,0x60,0x00,0x00,0x30,0xF8,0x3F,0x30,0xF8,0x3F,0xF8,//
0x0B,0x30,0x18,0x08,0x30,0x0C,0x48,0x3C,0x0E,0x68,0x36,0xFE,0xC9,0x36,0xF8,0xC9,//
0x32,0x60,0x88,0x33,0x60,0x08,0x33,0xFC,0x8B,0x33,0xFC,0x8B,0x37,0x60,0xC8,0x36,//
0x60,0xE8,0x3C,0x60,0x68,0x34,0x60,0x0B,0x30,0xE0,0x09,0x30,0xF0,0x08,0x30,0x70,//
0x08,0x3C,0x20,0x08,0x38,0x00,0x00,0x00,//"閽?,35
0x00,0x00,0x00,0x00,0x00,0x00,0x70,0xF8,0x1F,0x30,0xF8,0x1F,0x30,0x18,0x18,0xF8,//
0x1B,0x18,0x18,0x1B,0x18,0x0C,0x18,0x18,0x0E,0x18,0x18,0xFE,0xF9,0x1F,0x60,0x00,//
0x00,0x60,0x00,0x00,0x60,0x00,0x00,0xFC,0xFF,0x3F,0xFC,0x0F,0x30,0x60,0x0C,0x30,//
0x60,0x0C,0x30,0x60,0x0C,0x30,0x60,0x0F,0x30,0xE0,0x0D,0x30,0xE0,0xFC,0x3F,0x70,//
0x0C,0x30,0x20,0x0C,0x30,0x00,0x00,0x00,//"閾?,36
0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x00,0x00,0x30,0xFC,0x3F,0x10,0x0C,0x30,0xF8,//
0x0C,0x30,0xF8,0x0C,0x30,0x0C,0xEC,0x37,0x06,0xEC,0x37,0xFE,0x0C,0x30,0xF8,0x0C,//
0x30,0x30,0xEC,0x33,0x30,0x6C,0x32,0x30,0x6C,0x32,0xFE,0x6D,0x32,0x30,0x6C,0x32,//
0x30,0x6C,0x32,0x30,0xEC,0x33,0xB0,0x0C,0x30,0xF0,0x0C,0x30,0xF0,0x0C,0x30,0x30,//
0x0C,0x3C,0x10,0x0C,0x18,0x00,0x0C,0x00,//"閾?,37
0x00,0x00,0x00,0x00,0x40,0x00,0x30,0xC0,0x00,0x30,0x40,0x00,0x18,0xF8,0x1F,0xF8,//
0x09,0x18,0x0C,0x08,0x18,0x06,0xF8,0x1F,0x02,0x08,0x18,0xFC,0xF8,0x1F,0x30,0xF8,//
0x1F,0x30,0x08,0x18,0x30,0xF8,0x1F,0xFC,0x81,0x01,0xFC,0x81,0x01,0x30,0xFE,0x3F,//
0x30,0xE0,0x03,0x30,0xE0,0x07,0xB0,0xB0,0x0F,0xF0,0x98,0x1D,0x70,0x8C,0x79,0x30,//
0x87,0x21,0x10,0x80,0x01,0x00,0x00,0x00,//"闀?,38
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x1C,0x00,0x00,0x3E,0x00,0x00,//
0x66,0x00,0x00,0xC3,0x00,0x80,0x81,0x01,0xC0,0x00,0x07,0x70,0x00,0x0E,0xF8,0xFF,//
0x7D,0xDE,0xFF,0x31,0x04,0x00,0x00,0x00,0x00,0x00,0xE0,0xFF,0x07,0xE0,0xFF,0x07,//
0x60,0x00,0x06,0x60,0x00,0x06,0x60,0x00,0x06,0x60,0x00,0x06,0xE0,0xFF,0x07,0x60,//
0x00,0x06,0x60,0x00,0x06,0x00,0x00,0x00,//"鍚?,39
0x00,0x00,0x00,0x00,0x08,0x00,0x00,0x18,0x00,0x00,0x1C,0x00,0x00,0x36,0x00,0x00,//
0x67,0x00,0x80,0xC3,0x00,0xC0,0x81,0x03,0xE0,0x00,0x0F,0x38,0x00,0x7C,0xDE,0xFF,//
0x33,0x0C,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0xF0,0xFF,0x0F,0xF0,0xFF,0x0F,//
0x40,0x18,0x01,0xE0,0x18,0x07,0xC0,0x18,0x03,0x80,0x99,0x01,0x80,0x99,0x01,0xF8,//
0xFF,0x3F,0xF8,0xFF,0x3F,0x00,0x00,0x00,//"閲?,40
0x00,0x00,0x00,0x00,0x80,0x01,0x00,0x88,0x31,0xFE,0x89,0x11,0xFE,0x89,0x11,0x30,//
0x88,0x11,0x30,0xF8,0x3F,0x10,0x60,0x00,0x18,0x60,0x00,0xF8,0x21,0x00,0xF8,0xFF,//
0x7F,0x9C,0x21,0x00,0x9C,0x31,0x01,0x9E,0x31,0x33,0x98,0x59,0x33,0x98,0x79,0x1B,//
0x98,0x6D,0x13,0xF8,0x0D,0x03,0xF8,0x87,0x07,0x98,0xC1,0x0C,0x18,0xE0,0x1C,0x18,//
0x70,0x70,0x00,0x38,0x20,0x00,0x00,0x00,//"纰?,41
0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x80,0x01,0x30,0x80,0x00,0x30,0x80,0x00,0xF8,//
0xFD,0x3F,0xF8,0x81,0x00,0x0C,0x80,0x00,0x0E,0xF8,0x3F,0xFE,0xF8,0x3F,0xF8,0xC0,//
0x00,0x20,0xC0,0x00,0x20,0xFC,0x7F,0xFC,0x41,0x08,0xFC,0x61,0x08,0x20,0xE0,0x7F,//
0x20,0xE0,0x7F,0x20,0x31,0x08,0xA0,0xD9,0x08,0xE0,0x9C,0x08,0x70,0x0E,0x08,0x30,//
0x06,0x0F,0x00,0x00,0x0E,0x00,0x00,0x00,//"閾?,42
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF8,0xFF,0x1F,0xF8,0xFF,0x1F,0x00,//
0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,//
0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,//
0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0xFE,0xFF,0x7F,0xFE,//
0xFF,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,//"宸?,43
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE7,0x00,0x00,0xE7,0x00,0x00,0x63,0x00,0x00,//
0x63,0x00,0x00,0x63,0x00,0x0C,0x63,0x38,0x0C,0x63,0x38,0x18,0x63,0x18,0x18,0x63,//
0x18,0x38,0x63,0x0C,0x30,0x63,0x0C,0x30,0x63,0x0C,0x70,0x63,0x06,0x20,0x63,0x06,//
0x00,0x63,0x00,0x00,0x63,0x00,0x00,0x63,0x00,0x00,0x63,0x00,0xFE,0xFF,0x7F,0xFE,//
0xFF,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,//"涓?,44
0x00,0x00,0x00,0x00,0x00,0x00,0x20,0x80,0x00,0x70,0x80,0x00,0x30,0x80,0x00,0x10,//
0x82,0x20,0x18,0xFE,0x3F,0x88,0x80,0x00,0x8C,0x81,0x00,0xFE,0x8C,0x18,0x7E,0x8C,//
0x18,0x60,0x8C,0x18,0x30,0x8C,0x18,0x18,0x8C,0x18,0x0C,0x8C,0x18,0xFC,0xFD,0x1F,//
0x1C,0x80,0x18,0x00,0x80,0x00,0x00,0x80,0x00,0xE0,0x81,0x60,0x7E,0x80,0x60,0x0E,//
0x80,0x3F,0x00,0x00,0x1F,0x00,0x00,0x00,//"绾?,45
0x00,0x00,0x00,0x00,0x00,0x00,0x70,0x80,0x01,0x30,0x80,0x01,0x30,0x80,0x01,0xF8,//
0x83,0x01,0x18,0x80,0x01,0x0C,0xF8,0x3F,0x0C,0xF8,0x3F,0xFE,0x83,0x01,0xF8,0x83,//
0x01,0x60,0xC0,0x01,0x60,0xC0,0x01,0x60,0xC0,0x03,0xFC,0x43,0x02,0x60,0x60,0x06,//
0x60,0x60,0x06,0x60,0x30,0x0C,0x60,0xB7,0x18,0xE0,0xDB,0x39,0xE0,0x9D,0x73,0x70,//
0x0E,0x23,0x20,0x00,0x00,0x00,0x00,0x00,//"閽?,46
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x00,0x00,0x06,0x00,0x00,0x02,0x00,0xF0,//
0xFF,0x1F,0xF0,0xFF,0x1F,0x30,0x00,0x18,0x30,0x00,0x18,0x30,0x00,0x18,0x30,0x00,//
0x18,0x30,0x00,0x18,0xF0,0xFF,0x1F,0xF0,0xFF,0x1F,0x30,0x00,0x18,0x30,0x00,0x18,//
0x30,0x00,0x18,0x30,0x00,0x18,0x30,0x00,0x18,0xF0,0xFF,0x1F,0xF0,0xFF,0x1F,0x30,//
0x00,0x18,0x30,0x00,0x18,0x00,0x00,0x00,//"鐧?,47
0x00,0x00,0x00,0x10,0x00,0x00,0x70,0x00,0x00,0x30,0xFC,0x1F,0x10,0xFC,0x1F,0xF8,//
0x00,0x07,0xF8,0x80,0x01,0x0C,0x80,0x00,0x06,0xFE,0x7F,0xFE,0x82,0x60,0xFC,0x80,//
0x00,0x30,0xC0,0x00,0x30,0xE0,0x00,0xFE,0x21,0x00,0xFE,0xFD,0x1F,0x30,0xFC,0x1F,//
0x30,0x64,0x13,0x30,0x64,0x13,0xB0,0x64,0x13,0xF0,0x64,0x13,0x70,0x64,0x13,0x38,//
0xFF,0x7F,0x10,0xFF,0x7F,0x00,0x00,0x00,//"閿?,48
0x00,0x00,0x00,0x00,0x10,0x00,0x00,0x30,0x00,0x00,0x30,0x00,0xF0,0xFF,0x3F,0x30,//
0x30,0x00,0x30,0x30,0x00,0xB0,0xFF,0x0F,0x30,0x30,0x0C,0x30,0x30,0x0C,0xF0,0xFF,//
0x3F,0x30,0x30,0x0C,0x30,0x30,0x0C,0xB0,0xFF,0x0F,0x10,0x70,0x00,0x90,0x70,0x08,//
0x98,0xF1,0x0C,0x18,0xBF,0x07,0x18,0x3E,0x03,0x8C,0x33,0x0E,0xCE,0x31,0x3C,0xC6,//
0x3C,0x38,0x04,0x1C,0x00,0x00,0x00,0x00,//"搴?,49
0x00,0x00,0x00,0x00,0x83,0x01,0x00,0x83,0x01,0xF8,0xFF,0x3F,0xF8,0xFF,0x3F,0x00,//
0x83,0x01,0x00,0x83,0x01,0xFE,0xFF,0x7F,0xFE,0xFF,0x7F,0x00,0x18,0x00,0xF0,0xFF,//
0x0F,0xF0,0xFF,0x0F,0x30,0x18,0x0C,0x30,0x18,0x0C,0xF0,0xFF,0x0F,0x30,0x18,0x0C,//
0x30,0x18,0x0C,0xF0,0xFF,0x0F,0x00,0x81,0x00,0x80,0x83,0x03,0xE0,0x01,0x0F,0x78,//
0x00,0x3C,0x1C,0x00,0x30,0x00,0x00,0x00,//"榛?,50
0x00,0x00,0x00,0x00,0x00,0x00,0x08,0x80,0x0D,0x1C,0x80,0x19,0x18,0x80,0x39,0x30,//
0x80,0x11,0x00,0x80,0x01,0x80,0xFF,0x3F,0x00,0x80,0x01,0x3E,0x80,0x01,0x3E,0x80,//
0x01,0x30,0xBF,0x01,0x30,0xBF,0x01,0x30,0x0C,0x01,0x30,0x0C,0x03,0x30,0x0C,0x03,//
0x30,0x0C,0x03,0xB0,0x0D,0x03,0xF0,0x6C,0x26,0x70,0x7C,0x66,0x38,0x0F,0x6C,0x10,//
0x01,0x3C,0x00,0x00,0x38,0x00,0x00,0x00,//"璇?,51
0x00,0x00,0x00,0x00,0x00,0x00,0x30,0x60,0x00,0x30,0x60,0x00,0x30,0x60,0x00,0x30,//
0x60,0x00,0x30,0xFE,0x0F,0x30,0x60,0x0C,0xFE,0x61,0x0C,0x30,0x60,0x0C,0x30,0x60,//
0x0C,0x30,0x60,0x0C,0x30,0x60,0x0C,0x30,0xFF,0x7F,0x30,0xE0,0x00,0x30,0xE0,0x01,//
0xF0,0xB1,0x01,0x7C,0x30,0x03,0x1E,0x18,0x07,0x00,0x1C,0x0E,0x00,0x0E,0x1C,0x00,//
0x07,0x38,0x80,0x03,0x30,0x00,0x00,0x00,//"鍧?,52
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x20,0xF0,0xFF,0x3F,0x10,0x00,0x00,0x10,//
0x00,0x00,0x10,0xFF,0x0F,0x10,0x01,0x08,0x10,0xFF,0x0F,0x10,0xFF,0x0F,0x10,0x01,//
0x08,0x10,0xFF,0x0F,0x10,0x00,0x00,0x10,0x00,0x00,0x10,0xFF,0x1F,0x18,0x00,0x0F,//
0x18,0xC0,0x01,0x58,0x40,0x60,0xD8,0xFF,0x7F,0x0C,0x40,0x00,0x0C,0x40,0x00,0x0E,//
0x78,0x00,0x04,0x70,0x00,0x00,0x00,0x00,//"鍘?,53
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x38,0x00,0x00,0x30,0x00,0xF8,0xFF,0x1F,0xF8,//
0xFF,0x1F,0x18,0x06,0x03,0x18,0x06,0x03,0x18,0x06,0x03,0xD8,0xFF,0x1F,0x18,0x06,//
0x03,0x18,0x06,0x03,0x18,0xFE,0x03,0x18,0xFE,0x03,0x18,0x00,0x00,0x98,0xFF,0x07,//
0x18,0x06,0x03,0x18,0x0C,0x03,0x08,0x98,0x01,0x0C,0xF8,0x00,0x0C,0xF0,0x00,0x06,//
0xDF,0x1F,0xE4,0x07,0x3E,0x00,0x00,0x00,//"搴?,54
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x00,0x18,0xC2,0x20,0x30,0xFE,0x3F,0x60,//
0xC0,0x00,0x00,0xFE,0x3F,0x00,0xFE,0x3F,0x00,0xC0,0x00,0x3E,0xFF,0x7F,0x3E,0xFF,//
0x7F,0x30,0x00,0x00,0x30,0xFC,0x1F,0x30,0x0C,0x18,0x30,0x0C,0x18,0x30,0xFC,0x1F,//
0x30,0x0C,0x18,0xB0,0x0D,0x18,0xF0,0xFC,0x1F,0x70,0x0C,0x18,0x38,0x0C,0x18,0x10,//
0x0C,0x18,0x00,0x0C,0x1C,0x00,0x00,0x00,//"璇?,55
0x00,0x00,0x00,0x00,0x00,0x03,0x30,0x00,0x03,0x10,0x00,0x03,0x18,0xCF,0x1F,0xFC,//
0x0C,0x13,0xCC,0x0C,0x13,0x06,0xE4,0x7F,0x02,0x06,0x53,0xF8,0x06,0x13,0x18,0xC3,//
0x1F,0x10,0xDF,0x1F,0x10,0x08,0x03,0x7C,0x08,0x03,0x7C,0xEA,0x1F,0x10,0x0B,0x03,//
0x10,0x0B,0x03,0x10,0xEE,0x3F,0xD0,0x0E,0x03,0x70,0x0C,0x03,0x38,0x3E,0x03,0x18,//
0xF3,0x7F,0x80,0x81,0x7F,0x00,0x00,0x00,//"閿?,56
0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x08,0x60,0x80,0x1F,0x60,0xFE,0x07,0x30,//
0x82,0x00,0x30,0x82,0x00,0x38,0x82,0x00,0x38,0x82,0x00,0x3C,0x82,0x00,0x3C,0xFE,//
0x3F,0x36,0xFE,0x3F,0x34,0x82,0x01,0x30,0x82,0x01,0x30,0x82,0x01,0x30,0x02,0x01,//
0x30,0x22,0x03,0x30,0x32,0x03,0x30,0x3A,0x23,0x30,0x1E,0x66,0x30,0x37,0x6C,0x30,//
0x62,0x3C,0x30,0x40,0x38,0x00,0x00,0x00,//"浣?,57
0x00,0x00,0x00,0x00,0x04,0x00,0x00,0x0C,0x00,0x00,0x06,0x00,0x00,0x06,0x00,0xF8,//
0xFF,0x3F,0x00,0x03,0x00,0x00,0x03,0x00,0x80,0x01,0x00,0xE0,0xFC,0x1F,0xE0,0xFC,//
0x1F,0x60,0x00,0x0E,0x70,0x00,0x07,0x78,0x80,0x01,0x6E,0x80,0x01,0x64,0xFF,0x7F,//
0x60,0x80,0x01,0x60,0x80,0x01,0x60,0x80,0x01,0x60,0x80,0x01,0x60,0x80,0x01,0x60,//
0xF0,0x01,0x60,0xE0,0x00,0x00,0x00,0x00,//"瀛?,58
0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x80,0x01,0x30,0x82,0x01,0x30,0xE3,0x37,0x30,//
0xE6,0x37,0x18,0x86,0x19,0x18,0x82,0x09,0x1C,0x80,0x0D,0xDC,0xF3,0x7F,0xD6,0xF3,//
0x7F,0x16,0x03,0x03,0x10,0x83,0x01,0x10,0xC3,0x1F,0x10,0x73,0x10,0x10,0x7B,0x10,//
0x10,0xD3,0x1F,0x10,0xCB,0x1F,0x10,0x4F,0x10,0x10,0x47,0x10,0x10,0xC3,0x1F,0x10,//
0x40,0x10,0x10,0x40,0x10,0x00,0x00,0x00,//"鍌?,59
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x18,0x00,0x60,0x18,0x00,0xE0,0x18,0x00,0xC0,//
0x18,0x00,0x80,0x09,0x00,0x80,0x08,0x00,0xF8,0xFF,0x3F,0xF8,0xFF,0x3F,0x00,0x0C,//
0x30,0x00,0x0C,0x30,0x00,0x06,0x30,0x00,0x26,0x18,0x00,0xF6,0x18,0x00,0xC3,0x19,//
0x80,0x83,0x19,0x80,0x01,0x19,0xC0,0x00,0x18,0x60,0x00,0x18,0x38,0x00,0x1C,0x1C,//
0xC0,0x0F,0x08,0x80,0x07,0x00,0x00,0x00,//"涓?,60
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x00,0x46,0x00,0x00,0xC3,0x01,0x80,//
0x81,0x03,0xE0,0xFF,0x07,0xE0,0x3F,0x0E,0x00,0x04,0x04,0xFE,0xFF,0x3F,0xFE,0xFF,//
0x3F,0x80,0x83,0x01,0xC0,0x30,0x03,0x70,0x3C,0x0E,0x1E,0x8F,0x7C,0xEC,0xE3,0x31,//
0x00,0x78,0x04,0x00,0x1E,0x0E,0xE0,0x87,0x07,0x40,0xE0,0x01,0x00,0x78,0x00,0xE0,//
0x0F,0x00,0xF0,0x01,0x00,0x00,0x00,0x00,//"鍙?,61
0x00,0x00,0x00,0x00,0x00,0x00,0x88,0x80,0x01,0x9C,0x8C,0x01,0x98,0xCC,0x00,0x90,//
0xC4,0x00,0xFC,0xDF,0x00,0xFC,0xDF,0x3F,0xE0,0x62,0x18,0xB0,0x66,0x18,0x9C,0x7C,//
0x08,0x8C,0x74,0x08,0xC0,0xD8,0x08,0x60,0xD0,0x0C,0xFC,0xCF,0x0C,0x20,0x8C,0x0D,//
0x30,0x86,0x07,0x30,0x06,0x07,0xE0,0x03,0x03,0x80,0x9F,0x07,0xC0,0xC8,0x0C,0x78,//
0x70,0x38,0x1C,0x38,0x30,0x00,0x00,0x00,//"鏁?,62
0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0xFF,0x07,0xE0,0xFF,0x07,0x60,0x00,0x06,0x60,//
0x00,0x06,0xE0,0xFF,0x07,0x60,0x00,0x06,0x60,0x00,0x06,0xE0,0xFF,0x07,0xE0,0xFF,//
0x07,0x00,0x00,0x00,0xFC,0xFF,0x7F,0xFC,0xFF,0x7F,0x00,0x18,0x00,0xC0,0x18,0x00,//
0xC0,0xF8,0x0F,0x60,0xF8,0x0F,0xE0,0x18,0x00,0xB0,0x1B,0x00,0x38,0x1F,0x00,0x1C,//
0xFC,0x7F,0x0C,0xE0,0x3F,0x00,0x00,0x00,//"鏄?,63
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF8,0xFF,0x3F,0xF8,0xFF,0x3F,0x00,//
0x18,0x00,0x00,0x1E,0x00,0x00,0x9F,0x00,0x80,0xDF,0x03,0xE0,0x1D,0x0F,0x78,0x1C,//
0x3C,0x1E,0x1C,0x78,0x04,0x1C,0x20,0x00,0x1C,0x00,0xE0,0xFF,0x07,0xE0,0xFF,0x07,//
0x60,0x00,0x06,0x60,0x00,0x06,0x60,0x00,0x06,0x60,0x00,0x06,0xE0,0xFF,0x07,0x60,//
0x00,0x06,0x60,0x00,0x06,0x00,0x00,0x00,//"鍚?,64
0x00,0x00,0x00,0x00,0x60,0x00,0x00,0x60,0x00,0x00,0x70,0x00,0xFC,0xF3,0x0F,0x60,//
0x18,0x06,0x20,0x0C,0x06,0x30,0x0E,0x03,0x30,0xF4,0x3F,0x10,0x10,0x21,0xF8,0x11,//
0x21,0x1C,0x11,0x21,0x1C,0xF1,0x3F,0x1E,0xF1,0x3F,0x1C,0x11,0x21,0x18,0x19,0x21,//
0x18,0xF9,0x3F,0xF8,0x19,0x21,0xF8,0x19,0x21,0x18,0x19,0x21,0x18,0x0D,0x21,0x18,//
0x0E,0x31,0x00,0x04,0x39,0x00,0x00,0x08,//"纭?,65
0x00,0x00,0x00,0x00,0x00,0x00,0x20,0xC0,0x00,0x70,0xC0,0x00,0xE0,0xC0,0x00,0xC0,//
0xC1,0x00,0x00,0xC0,0x00,0x00,0xC0,0x00,0x00,0xC0,0x00,0x3E,0xC0,0x00,0x3E,0xC0,//
0x00,0x30,0xE0,0x00,0x30,0xE0,0x01,0x30,0xE0,0x01,0x30,0x20,0x01,0x30,0x33,0x03,//
0xB0,0x33,0x03,0xF0,0x19,0x06,0xF0,0x1C,0x06,0x70,0x0E,0x0C,0x30,0x06,0x1C,0x80,//
0x03,0x78,0x00,0x01,0x30,0x00,0x00,0x00,//"璁?,66
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,0x3F,0x00,0x18,0xE6,0x3F,0x18,//
0xC6,0x30,0x18,0xC6,0x10,0x18,0xC6,0x10,0xF8,0xC7,0x18,0x18,0xC6,0x18,0x18,0xC6,//
0x18,0x18,0x86,0x18,0x18,0x86,0x0C,0xF8,0x87,0x0D,0x18,0x86,0x0D,0x18,0x06,0x07,//
0x18,0x36,0x07,0x18,0x3F,0x07,0xF8,0x87,0x0D,0x3C,0xC6,0x1D,0x04,0xE6,0x38,0x00,//
0x76,0x70,0x00,0x26,0x20,0x00,0x06,0x00,//"鍙?,67
0x00,0x00,0x00,0x00,0xC0,0x01,0x08,0xC0,0x01,0x1C,0xC7,0x71,0x38,0xC6,0x31,0x70,//
0xCC,0x19,0x20,0xCC,0x19,0x00,0xC0,0x01,0x00,0xFE,0x3F,0x0E,0xFE,0x3F,0x3C,0x06,//
0x30,0x30,0x06,0x30,0x00,0x06,0x30,0x00,0xFE,0x3F,0x00,0x06,0x30,0x30,0x06,0x30,//
0x30,0x06,0x30,0x30,0xFE,0x3F,0x18,0x06,0x30,0x18,0x06,0x30,0x1C,0x06,0x30,0x0E,//
0x06,0x3C,0x08,0x06,0x1C,0x00,0x00,0x00,//"娑?,68
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF8,0xFF,0x0F,0xF8,0xFF,0x0F,0x00,//
0x18,0x00,0x20,0x18,0x0C,0x70,0x18,0x0E,0xE0,0x18,0x06,0xC0,0x18,0x03,0xC0,0x99,//
0x03,0x80,0x98,0x01,0x00,0x18,0x00,0xFE,0xFF,0x7F,0xFE,0xFF,0x7F,0x00,0x18,0x00,//
0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0x00,//
0x18,0x00,0x00,0x18,0x00,0x00,0x00,0x00,//"骞?,69
0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x60,0x00,0x60,0x60,0x00,0x60,0x30,0x00,0x60,//
0x30,0x00,0x60,0xF0,0x3F,0xFE,0x19,0x30,0xFE,0x19,0x30,0x60,0x0C,0x30,0x60,0x6C,//
0x30,0x60,0xC4,0x30,0x60,0x80,0x31,0x60,0x00,0x30,0x60,0x00,0x33,0x60,0x80,0x33,//
0x60,0xE1,0x30,0xF0,0x71,0x30,0x7E,0x38,0x30,0x0E,0x00,0x30,0x00,0x00,0x30,0x00,//
0x00,0x1F,0x00,0x00,0x1F,0x00,0x00,0x00,//"鍧?,70
0x00,0x00,0x00,0x00,0x18,0x00,0x00,0x18,0x00,0xF8,0xFF,0x1F,0xF8,0xFF,0x1F,0x00,//
0x18,0x00,0xF0,0xFF,0x0F,0xF0,0xFF,0x0F,0x00,0x18,0x00,0xFC,0xFF,0x7F,0xFC,0xFF,//
0x7F,0x00,0x00,0x00,0xE0,0xFF,0x0F,0xE0,0xFF,0x0F,0x20,0x00,0x0C,0xE0,0xFF,0x0F,//
0xE0,0xFF,0x0F,0x20,0x00,0x0C,0xE0,0xFF,0x0F,0xE0,0xFF,0x0F,0x20,0x00,0x0C,0x20,//
0x80,0x0F,0x20,0x00,0x07,0x00,0x00,0x00,//"闈?,71
0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x61,0x00,0x98,0x61,0x1C,0x98,0x6F,0x0F,0x98,//
0xEF,0x03,0x98,0x61,0x00,0x98,0x61,0x60,0xD8,0xEF,0x30,0xFC,0xC3,0x3F,0x04,0x06,//
0x00,0x00,0x83,0x01,0xC0,0xE1,0x00,0xE0,0x3F,0x00,0x20,0x0C,0x06,0x00,0x07,0x0E,//
0xF0,0xFF,0x1F,0xF0,0x3F,0x38,0x40,0x10,0x01,0xC0,0x10,0x07,0xF0,0x18,0x1E,0x3C,//
0x1E,0x38,0x0C,0x0E,0x30,0x00,0x00,0x00,//"绱?,72
0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x06,0x60,0x00,0x04,0x60,0x00,0x04,0x60,//
0x00,0x04,0x60,0x00,0x04,0xFC,0xFB,0x7F,0xFC,0xFB,0x7F,0x60,0x00,0x07,0x60,0x00,//
0x07,0x70,0x81,0x05,0xF0,0x81,0x05,0x78,0xC3,0x04,0x78,0x62,0x04,0x6C,0x60,0x04,//
0x66,0x30,0x04,0x66,0x18,0x04,0x60,0x0E,0x04,0x60,0x04,0x04,0x60,0x00,0x06,0x60,//
0x80,0x07,0x60,0x00,0x03,0x00,0x00,0x00,//"鏉?,73
0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x00,0x0C,0x60,0x00,0x0C,0x66,0x26,0x0C,0x6C,//
0x66,0x0C,0x6C,0xC3,0x0C,0x78,0xC3,0x0D,0xE0,0x81,0x0C,0xFE,0x17,0x0C,0xFE,0x3F,//
0x0C,0x60,0x60,0x0C,0x70,0xC1,0x0C,0xF0,0x83,0x0C,0x78,0x06,0x6C,0x78,0x06,0x7F,//
0x6C,0xFC,0x0F,0x66,0x3C,0x0C,0x66,0x00,0x0C,0x60,0x00,0x0C,0x60,0x00,0x0C,0x60,//
0x00,0x0C,0x60,0x00,0x0C,0x00,0x00,0x00,//"鏂?,74
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0C,0x00,0x00,0x46,0x00,0x00,0xC3,0x01,0x80,//
0x81,0x03,0xE0,0xFF,0x07,0xE0,0x3F,0x0E,0x00,0x04,0x04,0xFE,0xFF,0x3F,0xFE,0xFF,//
0x3F,0x80,0x83,0x01,0xC0,0x30,0x03,0x70,0x3C,0x0E,0x1E,0x8F,0x7C,0xEC,0xE3,0x31,//
0x00,0x78,0x04,0x00,0x1E,0x0E,0xE0,0x87,0x07,0x40,0xE0,0x01,0x00,0x78,0x00,0xE0,//
0x0F,0x00,0xF0,0x01,0x00,0x00,0x00,0x00,//"鍙?,75
0x00,0x00,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x08,0xE0,0xFF,0x0C,0x20,//
0x8C,0x06,0x00,0x0C,0x03,0x00,0x8C,0x01,0xFC,0xFF,0x7F,0xFC,0xFF,0x7F,0x00,0x18,//
0x00,0x00,0x0E,0x00,0x00,0xFF,0x0F,0xC0,0xFF,0x0F,0x78,0x0C,0x00,0x1E,0x04,0x00,//
0x04,0xFF,0x07,0x00,0xFE,0x07,0x00,0x00,0x06,0x00,0x00,0x06,0x00,0x00,0x06,0x00,//
0xF8,0x03,0x00,0xF0,0x01,0x00,0x00,0x00,//"鑰?,76
0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0x60,0x00,0x60,0xE0,0x00,0x60,0xC0,0x00,0x70,//
0x40,0x00,0x30,0xFE,0x3F,0x38,0xFE,0x3F,0x38,0x00,0x00,0x3C,0x00,0x04,0x3C,0x0C,//
0x0E,0x36,0x0C,0x06,0x36,0x0C,0x06,0x30,0x18,0x06,0x30,0x18,0x06,0x30,0x18,0x03,//
0x30,0x30,0x03,0x30,0x30,0x03,0x30,0x10,0x03,0x30,0x80,0x01,0x30,0xFF,0x7F,0x30,//
0xFF,0x7F,0x30,0x00,0x00,0x00,0x00,0x00,//"浣?,77
0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0xFF,0x0F,0x00,0x18,0x00,0x00,0x18,0x00,0xFC,
0xFF,0x3F,0x0C,0x18,0x30,0xEC,0xD9,0x37,0xEC,0xD9,0x37,0x00,0x18,0x00,0xE0,0xC9,
0x07,0x00,0x1C,0x00,0x00,0x3E,0x00,0x00,0xE3,0x00,0xC0,0xCD,0x03,0x78,0x18,0x3F,
0x3C,0x04,0x3C,0xC4,0xFF,0x03,0x00,0xC0,0x00,0x00,0x63,0x00,0x00,0x3F,0x00,0x00,
0x7C,0x00,0x00,0xE0,0x00,0x00,0x80,0x00,//闆?78
0x00,0x00,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,
0xFC,0x1F,0x00,0x0C,0x00,0x00,0x0C,0x00,0x00,0x0C,0x00,0xF0,0xFF,0x0F,0xF0,0xFF,
0x0F,0x30,0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,0x0C,0x30,0x00,0x0C,0xF0,0xFF,0x0F,
0xF0,0xFF,0x0F,0x00,0x00,0x00,0x18,0x61,0x08,0x18,0x63,0x18,0x1C,0xC3,0x38,0x0C,
0xC7,0x30,0x0E,0x82,0x20,0x00,0x00,0x00,//鐐?79
};

//char Hzk[][32]={

//{0x00,0x00,0xF0,0x10,0x10,0x10,0x10,0xFF,0x10,0x10,0x10,0x10,0xF0,0x00,0x00,0x00},
//{0x00,0x00,0x0F,0x04,0x04,0x04,0x04,0xFF,0x04,0x04,0x04,0x04,0x0F,0x00,0x00,0x00},/*"中",0*/

//{0x40,0x40,0x40,0x5F,0x55,0x55,0x55,0x75,0x55,0x55,0x55,0x5F,0x40,0x40,0x40,0x00},
//{0x00,0x40,0x20,0x0F,0x09,0x49,0x89,0x79,0x09,0x09,0x09,0x0F,0x20,0x40,0x00,0x00},/*"景",1*/

//{0x00,0xFE,0x02,0x42,0x4A,0xCA,0x4A,0x4A,0xCA,0x4A,0x4A,0x42,0x02,0xFE,0x00,0x00},
//{0x00,0xFF,0x40,0x50,0x4C,0x43,0x40,0x40,0x4F,0x50,0x50,0x5C,0x40,0xFF,0x00,0x00},/*"园",2*/

//{0x00,0x00,0xF8,0x88,0x88,0x88,0x88,0xFF,0x88,0x88,0x88,0x88,0xF8,0x00,0x00,0x00},
//{0x00,0x00,0x1F,0x08,0x08,0x08,0x08,0x7F,0x88,0x88,0x88,0x88,0x9F,0x80,0xF0,0x00},/*"电",3*/

//{0x80,0x82,0x82,0x82,0x82,0x82,0x82,0xE2,0xA2,0x92,0x8A,0x86,0x82,0x80,0x80,0x00},
//{0x00,0x00,0x00,0x00,0x00,0x40,0x80,0x7F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*"子",4*/

//{0x24,0x24,0xA4,0xFE,0xA3,0x22,0x00,0x22,0xCC,0x00,0x00,0xFF,0x00,0x00,0x00,0x00},
//{0x08,0x06,0x01,0xFF,0x00,0x01,0x04,0x04,0x04,0x04,0x04,0xFF,0x02,0x02,0x02,0x00},/*"科",5*/

//{0x10,0x10,0x10,0xFF,0x10,0x90,0x08,0x88,0x88,0x88,0xFF,0x88,0x88,0x88,0x08,0x00},
//{0x04,0x44,0x82,0x7F,0x01,0x80,0x80,0x40,0x43,0x2C,0x10,0x28,0x46,0x81,0x80,0x00},/*"技",6*/

//};

static unsigned char  ChnTab[]={
	
0x00,0x00,0xF0,0x10,0x10,0x10,0x10,0xFF,0x10,0x10,0x10,0x10,0xF0,0x00,0x00,0x00,
0x00,0x00,0x0F,0x04,0x04,0x04,0x04,0xFF,0x04,0x04,0x04,0x04,0x0F,0x00,0x00,0x00,/*"?",0*/
/* (16 X 16 , ?? )*/

0x00,0xFE,0x02,0x12,0x92,0x92,0x92,0xF2,0x92,0x92,0x92,0x12,0x02,0xFE,0x00,0x00,
0x00,0xFF,0x40,0x48,0x48,0x48,0x48,0x4F,0x48,0x4A,0x4C,0x48,0x40,0xFF,0x00,0x00,/*"?",1*/
/* (16 X 16 , ?? )*/

0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x3F,0xC0,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x80,0x40,0x20,0x10,0x0C,0x03,0x00,0x00,0x00,0x03,0x0C,0x10,0x20,0x40,0x80,0x00,/*"?",2*/
/* (16 X 16 , ?? )*/

0x00,0x00,0xFE,0x22,0x22,0x22,0x22,0x22,0xE2,0x22,0x22,0x22,0x3E,0x00,0x00,0x00,
0x00,0x00,0xFF,0x41,0x21,0x11,0x01,0x01,0x03,0x0D,0x11,0x21,0x41,0xF1,0x00,0x00,/*"?",3*/
/* (16 X 16 , ?? )*/

};


#endif


