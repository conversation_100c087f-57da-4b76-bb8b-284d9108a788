//#ifndef __APP_StressDetector_H
//#define __APP_StressDetector_H
//#include "app.h" 


//#define	FD_SELWAVE_PAUSED		0
//#define	FD_SELWAVE_ING			1
//#define	FD_SELWAVE_DONE			2
//#define	FD_DETECT_UNSTART		3
//#define	FD_DETECT_ING			4
//#define	FD_DETECT_PAUSED		5


//#define STRESS_SEL_WAVE_LEN			4096*4
//#define STRESS_ACQ_WAVE_LEN			4096
//#define STRESS_CTRST_WAVE_LEN		4096
//#define STRESS_RTIME_WAVE_LEN		4096

//typedef struct
//{
//	uint8_t		lost;		//跟丢了
//	float		init_pos;	//初始位置, 点, 绝对位置
//	float		last_pos;	//上次位置
//	float		curr_pos;	//当前位置
//	float		delta_pos;	//位置变化量
//}TRACKER_T;

//typedef struct
//{
//	uint8_t 	state;
//	uint8_t		hasCtrstWave;
//	uint32_t	thickness_um;
//	float		measurementCount;
//	float		tof_sel_ns;
//	float		tof_ctrst_ns;
//	float		tof_rtime_ns;
//	float		tof_delta_ns;
//	float		tof_delta_xcorr_ns;
//	float		tof_delta_tracker_ns;
//	TRACKER_T	tracker;
//}FLAW_DETECTOR_FUNCTION_T;

//extern FLAW_DETECTOR_FUNCTION_T g_StressDetector;


//extern WAVE_T waveSel;
//extern WAVE_T waveAcq;
//extern WAVE_T waveCtrst;
//extern WAVE_T waveRtime;

//void StressDetector_DispMainUI(uint8_t animation);
//void App_StressDetector_Init();
//void App_StressDetector_MainTask();


//#endif
