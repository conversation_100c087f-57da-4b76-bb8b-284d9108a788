/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : bsp_led.c
  * @project : 
  * @brief   : 
  * <AUTHOR> PR Team
  * @since   : 2024/07/22
  * @version : V1.0 
  * @history :
  *	2024/07/22: V0.1
  *	  1) 首次创建;
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */

#include "bsp_led.h"
#include "stm32f7xx_hal.h"



/** Configure pins
     PA11   ------> OUT
*/
void BspLed_Init(void)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};

	__HAL_RCC_GPIOA_CLK_ENABLE();

	HAL_GPIO_WritePin(GPIOA, GPIO_PIN_11, GPIO_PIN_RESET);

	GPIO_InitStruct.Pin = GPIO_PIN_11;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_NOPULL;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_HIGH;
	HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

	BspLed(1);
	
//	while(1) {
//		HAL_GPIO_WritePin(GPIOA, GPIO_PIN_11, GPIO_PIN_SET);
//		HAL_GPIO_WritePin(GPIOA, GPIO_PIN_11, GPIO_PIN_RESET);
//	}
}


/******************************* end of file **********************************/
