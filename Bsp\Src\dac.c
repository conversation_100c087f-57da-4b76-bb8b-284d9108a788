#include "dac.h"
#include "stm32f7xx_hal.h"
#include "bsp.h"
#include <math.h>
#include "fpga.h"
#include "delay.h"

#define DAC_REF_VOL_MV 		2500 // mV

static DAC_HandleTypeDef hdac;
//DAC通道1输出初始化
void Dac1_Init(void)
{  
	DAC_ChannelConfTypeDef sConfig = {0};
	hdac.Instance = DAC;
	if (HAL_DAC_Init(&hdac) != HAL_OK)
	{
		Bsp_ErrorHandler();
	}

	/** DAC channel OUT1 config
	*/
	sConfig.DAC_Trigger = DAC_TRIGGER_NONE;
	sConfig.DAC_OutputBuffer = DAC_OUTPUTBUFFER_DISABLE;
	if (HAL_DAC_ConfigChannel(&hdac, &sConfig, DAC_CHANNEL_1) != HAL_OK)
	{
		Bsp_ErrorHandler();
	}
	
	HAL_DAC_Start(&hdac, DAC_CHANNEL_1);
//	
//	delay_ms(1000);
//	int32_t dlyMs = 100;
//	while(1) {
//		SetGain(15*10);
//		delay_ms(dlyMs);
//		
//		SetGain(21*10);
//		delay_ms(dlyMs);
//		
//		SetGain(27*10);
//		delay_ms(dlyMs);
//		
//		SetGain(33*10);
//		delay_ms(dlyMs);
//		
//		SetGain(39*10);
//		delay_ms(dlyMs);
//		
//		SetGain(45*10);
//		delay_ms(dlyMs);
//		
//		SetGain(51*10);
//		delay_ms(dlyMs);
//		
//		SetGain(57*10);
//		delay_ms(dlyMs);
//		
//		SetGain(63*10);
//		delay_ms(dlyMs);
//		
//		SetGain(69*10);
//		delay_ms(dlyMs);
//		
//		SetGain(75*10);
//		delay_ms(dlyMs);
//		
//		SetGain(81*10);
//		delay_ms(dlyMs);
//		
//		SetGain(87*10);
//		delay_ms(dlyMs);
//		
//		SetGain(93*10);
//		delay_ms(dlyMs);
//		
//		SetGain(99*10);
//		delay_ms(dlyMs);
//		
//		SetGain(105*10);
//		delay_ms(dlyMs);
//		
//		SetGain(111*10);
//		delay_ms(dlyMs);
//	}
}


void HAL_DAC_MspInit(DAC_HandleTypeDef* dacHandle)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	if(dacHandle->Instance==DAC)
	{
		__HAL_RCC_DAC_CLK_ENABLE();
		__HAL_RCC_GPIOA_CLK_ENABLE();
		/**DAC GPIO Configuration
		PA4     ------> DAC_OUT1
		*/
		GPIO_InitStruct.Pin = GPIO_PIN_4;
		GPIO_InitStruct.Mode = GPIO_MODE_ANALOG;
		GPIO_InitStruct.Pull = GPIO_NOPULL;
		HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);
	}
}


//设置通道1输出电压
//vol:0~3300,代表0~3.3V
static void Dac1_Set_Vol(uint32_t volMv)
{
	uint32_t tmp = (uint32_t)volMv * 4096 / DAC_REF_VOL_MV; // 基准源2.5V
//	double temp = volMv;
//	temp /= 1000;
//	temp = temp * 4096 / 2.5;	// 基准源2.5V
//	tmp = (uint32_t)volMv * 4096 / 2500; // 基准源2.5V
	
//	DAC_SetChannel1Data(DAC_Align_12b_R,tmp);//12位右对齐数据格式设置DAC值
	HAL_DAC_SetValue(&hdac,DAC_CHANNEL_1, DAC_ALIGN_12B_R, tmp);
}


// AD8332,单路增益7.5dB~55.5dB,对应电压40mV~1V. 
// 采用两路级联，级联后15dB~111dB,对应电压40mV~1V.
// 40mV ~ 1V
static uint32_t CalcGainVolMv(int32_t gainX10) { //dB*10, dB = 7.5dB ~ 55.5dB; 15dB~111dB
	uint32_t volMv;

	if(gainX10 < GAINx10_MIN) {
		gainX10 = GAINx10_MIN;
	}
	else if(gainX10 > GAINx10_MAX) {
		gainX10 = GAINx10_MAX;
	}
	
//	volMv = gainX10 - 110; //volMv = 10.0 * (gain - 11); //Gain(dB) = 50(dB/V) * Vgain(V) + 5.5(dB)
	volMv = (gainX10/10.0 - 2*5.5) * 1000 / 50 / 2;

	return volMv; // mV
}

void SetGain(uint32_t dBx10) {
	static uint32_t dBx10Last = 99999;
	if(dBx10Last == dBx10) return;
	
	Dac1_Set_Vol(CalcGainVolMv(dBx10));
	
	dBx10Last = dBx10;
}

/**
  * @brief     自动增益判断增益值否需要调整, 若需要调整则返回调整后的值
  * @param[i]  max_val: 存放波形数组
  * @param[io] gain_x10: 指向增益(增益已x10)
  * @retval    AGC完成状态
  *            - 0: 未完
  *            - 1: 完成，幅值达到预期
  *            - 2: 完成，幅值低于预期
  */
uint8_t Gain_AGC_isDone(int32_t max_val, int32_t *gain_x10) {
	
	if(max_val <= 0) {
		*gain_x10 = GAINx10_MAX;
		return 0; //not end
	}
	
	if((max_val >= (ACG_VAL - ACG_VAL_ERR)) && (max_val <= (ACG_VAL + 2*ACG_VAL_ERR))) {
		return 1; //done, fit
	}
	else if((max_val < (ACG_VAL - ACG_VAL_ERR)) && (*gain_x10 == GAINx10_MAX)) { //最大增益时，信号仍然较小,则结束ACG
		return 2; //done, but weak!
	}
	else {
		if(max_val >= Fpga_GetAdcMaxVal()) { //2047
			*gain_x10 = *gain_x10/1.3;
		}
		else {
			*gain_x10 = *gain_x10 + 200.0*log10(ACG_VAL/(double)max_val);
//			*gain_x10 = *gain_x10 + 100.0*log10(ACG_VAL/(double)max_val);
			if(*gain_x10 > GAINx10_MAX){
				*gain_x10 = GAINx10_MAX;
			}
			else if(*gain_x10 < GAINx10_MIN){
				*gain_x10 = GAINx10_MIN;
			}
		}
		return 0; //not end
	}
}









































