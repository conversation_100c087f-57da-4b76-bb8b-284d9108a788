#ifndef __KEY_H
#define __KEY_H	 
#include <stdint.h>
#include "stm32f7xx_hal.h"

#define KEY_DOWN_PIN    GPIO_PIN_13
#define KEY_UP_PIN      GPIO_PIN_14
#define KEY_SET_PIN     GPIO_PIN_15

/*下面的方式是通过直接操作库函数方式读取IO*/
#define KEY_DOWN 	HAL_GPIO_ReadPin(GPIOC, KEY_DOWN_PIN) 
#define KEY_UP 		HAL_GPIO_ReadPin(GPIOC, KEY_UP_PIN)
#define KEY_SET		HAL_GPIO_ReadPin(GPIOC, KEY_SET_PIN)


#define KEY_NULL_PRES 			0
#define KEY_SET_PRES 			1
#define KEY_UP_PRES				2
#define KEY_DOWN_PRES			3
#define KEY_SET_LONG_PRES 		5
#define KEY_UP_LONG_PRES		6
#define KEY_DOWN_LONG_PRES		7

#define KEY_NULL	 			0


#define KEY_M_PRES 				KEY_SET_PRES
#define KEY_M_LONG_PRES 		KEY_SET_LONG_PRES



void KEY_Init(void);
uint8_t KEY_Scan(uint8_t mode);
void Key_SetMKeyPressed(void);
void Key_SetUpKeyPressed(void);
void Key_SetDownKeyPressed(void);

#endif
