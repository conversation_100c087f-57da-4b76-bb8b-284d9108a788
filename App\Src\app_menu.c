#include "app_menu.h"
#include <stdio.h>
#include <string.h>
#include "key.h"
#include "oled.h"
#include "app_thickness.h"
#include "app_stressdetector.h"
#include "delay.h"
#include "wifi.h"
#include "adc.h"
#include "app_cali.h"
#include "fpga.h"
#include "data_persistence.h"
#include "bsp_zpt.h"
#include "unit_utils.h"
#include "ui_factory.h"

static uint16_t vel_bak;

static void DrawFocusedTriangle(uint8_t line) {
	OLED_ShowMediumChar(2, (line == 1) ? M_LINE_2_Y : M_LINE_1_Y, ' ');
	OLED_ShowMediumChar(2, (line == 1) ? M_LINE_1_Y : M_LINE_2_Y, 0x7F);
}

static void Menu_CaliSample(uint8_t keyId) {
	char hanzi[20], str[64];
	uint32_t i, dly_ms = 160;
	if(keyId == KEY_UP_PRES) {
		if(g_Dev.unit_type==UNIT_SI) g_Thk.caliThicknessVal = g_Thk.caliThicknessVal + 0.01;
		else g_Thk.caliThicknessVal = g_Thk.caliThicknessVal + UnitConvert_inch2mm(0.001);
	}
	else if(keyId == KEY_DOWN_PRES) {
		if(g_Dev.unit_type==UNIT_SI) g_Thk.caliThicknessVal = g_Thk.caliThicknessVal - 0.01;
		else g_Thk.caliThicknessVal = g_Thk.caliThicknessVal - UnitConvert_inch2mm(0.001);
	}
	else if((keyId == KEY_UP_LONG_PRES) || (keyId == KEY_DOWN_LONG_PRES)) {
		i = 0;
		while((KEY_UP == 0) || (KEY_DOWN == 0)) {
			delay_ms(dly_ms);
			if(i == 6) {
				dly_ms = 100;
				i++;
			}
			else if(i == 20) {
				dly_ms = 30;
				i++;
			}
			else if(i == 60) {
				dly_ms = 1;
			}
			else {
				i++;
			}
			
			if(g_Dev.unit_type==UNIT_SI) g_Thk.caliThicknessVal = (keyId == KEY_UP_LONG_PRES) ? (g_Thk.caliThicknessVal + 0.03) : (g_Thk.caliThicknessVal - 0.03);
			else g_Thk.caliThicknessVal = (keyId == KEY_UP_LONG_PRES) ? (g_Thk.caliThicknessVal + UnitConvert_inch2mm(0.03)) : (g_Thk.caliThicknessVal - UnitConvert_inch2mm(0.03));
			if(g_Thk.caliThicknessVal > CALI_THICKNESS_MAX) g_Thk.caliThicknessVal = CALI_THICKNESS_MAX;
			else if(g_Thk.caliThicknessVal < CALI_THICKNESS_MIN) g_Thk.caliThicknessVal = CALI_THICKNESS_MIN;
			if(g_Dev.unit_type==UNIT_SI) sprintf(str, "%.2f mm", g_Thk.caliThicknessVal);
			else sprintf(str, "%.3f in.", UnitConvert_mm2inch(g_Thk.caliThicknessVal));
			GUI_DispMixText(M_ALIGN_R, 0, 2, "  ", hanzi, str, 0);
		}
	}
	if(g_Thk.caliThicknessVal > CALI_THICKNESS_MAX) g_Thk.caliThicknessVal = CALI_THICKNESS_MAX;
	else if(g_Thk.caliThicknessVal < CALI_THICKNESS_MIN) g_Thk.caliThicknessVal = CALI_THICKNESS_MIN;
	DrawFocusedTriangle(1);
}

static void Menu_Cali(uint8_t keyId) {
	char hanzi[20], str[64];

	if(keyId == KEY_UP_PRES) { //校准声速
		ClearLED(0x00);
		if(g_Dev.lang_type == 0) {
			GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "Calibrating", 0);
			if( Calibrate_Velocity((uint32_t)(g_Thk.caliThicknessVal*1000)) == 0) {
				ClearLED(0x00);
				GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "Done!", 0);
				if(g_Dev.unit_type==UNIT_SI) sprintf(str, " %d m/s", g_Thk.ultraSonic_Velocity);
				else sprintf(str, "%.4fin/us", UnitConvert_mm2inch((float)g_Thk.ultraSonic_Velocity*1000)/1000000.0);
				GUI_DispMixText(64, M_LINE_2_Y, 1, "Vel. ", hanzi, str, 0);	
				g_Thk.pre_velc_id = 0;											
			}
			else {
				ClearLED(0x00);
				GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "Failed!", 0);
			}
				
		}
		else {
			hanzi[0] = 0; hanzi[1] = 1; hanzi[2] = 19;// "校准中"
			GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "\0", 3);
			if( Calibrate_Velocity((uint32_t)(g_Thk.caliThicknessVal*1000)) == 0) {
				ClearLED(0x00);
				hanzi[0] = 0; hanzi[1] = 1; hanzi[2] = 26; hanzi[3] = 27; 	// "校准成功"
				GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "\0", 4);
				hanzi[0] = 5; hanzi[1] = 6;									// "声速"
				if(g_Dev.unit_type==UNIT_SI) sprintf(str, " %d m/s", g_Thk.ultraSonic_Velocity);
				else sprintf(str, "%.4fin/us", UnitConvert_mm2inch((float)g_Thk.ultraSonic_Velocity*1000)/1000000.0);
				GUI_DispMixText(64, M_LINE_2_Y, 1, "\0", hanzi, str, 2);
				g_Thk.pre_velc_id = 0;												
			}
			else {
				ClearLED(0x00);
				hanzi[0] = 0; hanzi[1] = 1; hanzi[2] = 28; hanzi[3] = 29; 	// "校准失败"
				GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "\0", 4);
			}
		}
		SendParameter2FPGA(g_Daq.fs_MHz, g_Emat.US_avgTimes); //恢复校准前的参数
		delay_ms(1000);delay_ms(800);ClearLED(0x00);
		DrawFocusedTriangle(2);
	}
	else if(keyId == KEY_DOWN_LONG_PRES) { //下箭头键, 校准零点
		delay_ms(1000);
		if((KEY_SET == 0) && (KEY_DOWN == 0))  //长按下 和 M键
		{
			
			ClearLED(0x00);
			sprintf(str, "FW:%d", g_Dev.firmWareVersion);
			GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, str, 0);
			hanzi[0] = 78; hanzi[1] = 79;// "零点"
			sprintf(str, " %d ns", g_Zpt.zeroPoint_ns);
			GUI_DispMixText(64, M_LINE_2_Y, 1, "\0", hanzi, str, 2);
			delay_ms(1000);
			if(KEY_DOWN == 1) {//下键已释放,只显示零点
				while(1) {
					if(KEY_UP == 0) {
						delay_ms(10);
						if(KEY_UP == 0) 
							g_Zpt.zeroPoint_ns = g_Zpt.zeroPoint_ns + 1;
					}
					else if(KEY_DOWN == 0) {
						delay_ms(10);
						if(KEY_DOWN == 0)
							g_Zpt.zeroPoint_ns = g_Zpt.zeroPoint_ns - 1;
					}
					hanzi[0] = 78; hanzi[1] = 79;// "零点"
					sprintf(str, " %d ns", g_Zpt.zeroPoint_ns);
					GUI_DispMixText(64, M_LINE_2_Y, 1, "\0", hanzi, str, 2);
					if(KEY_SET == 0) {
						delay_ms(10);
						if(KEY_SET == 0) {
							DataPersistence_SaveAllDatas(1);
							break;
						}
					}
					delay_ms(100);
				}
				ClearLED(0x00);
				DrawFocusedTriangle(2);
			}
			else if(KEY_DOWN == 0) {
				ClearLED(0x00);
				hanzi[0]=0; hanzi[1]=1; hanzi[2]=78; hanzi[3]=79; hanzi[4]=19; //"校准零点中"
				GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi,  g_Dev.lang_type?"\0":"Calibrating Zero", g_Dev.lang_type?5:0);
				
				if(Calibrate_ZeroPoint((uint32_t)(g_Thk.caliThicknessVal*1000)) == 0) {
					ClearLED(0x00);
					hanzi[0] = 0; hanzi[1] = 1; hanzi[2] = 26; hanzi[3] = 27; str[0] = 0x00; //"校准成功"
					GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, g_Dev.lang_type?"\0":"Done!", g_Dev.lang_type?4:0);
					
					hanzi[0] = 78; hanzi[1] = 79; str[0] = 0x00; //""
					sprintf(str, " %d ns", g_Zpt.zeroPoint_ns);
					GUI_DispMixText(64, M_LINE_2_Y, 1, g_Dev.lang_type?"\0":"Zero. ", hanzi, str, g_Dev.lang_type?2:0);	
					g_Thk.pre_velc_id = 0;											
				}
				else {
					ClearLED(0x00);
					hanzi[0] = 0; hanzi[1] = 1; hanzi[2] = 28; hanzi[3] = 29; //"校准失败"
					GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, g_Dev.lang_type?"\0":"Failed!", g_Dev.lang_type?4:0);
				}

				DataPersistence_SaveAllDatas(1);
				SendParameter2FPGA(g_Daq.fs_MHz, g_Emat.US_avgTimes); //恢复校准前的参数
				delay_ms(1000);delay_ms(800);
				ClearLED(0x00);
				DrawFocusedTriangle(2);
			}
		}
	}
	else {
		DrawFocusedTriangle(2);
	}
}

static void Menu_MaterialVel(uint8_t keyId) {
	char hanzi[20], str[64], str1[64];
	uint32_t i;
	static uint32_t usr_para_id = 1;
	
	if(keyId == KEY_UP_PRES) {
		g_Thk.pre_velc_id = (g_Thk.pre_velc_id < 1) ? 0 : (g_Thk.pre_velc_id - 1); 	// 上一个预设声速
	}
	else if(keyId == KEY_DOWN_PRES) {
		g_Thk.pre_velc_id = (g_Thk.pre_velc_id < MENU_PRE_TOTAL) ? (g_Thk.pre_velc_id + 1) : MENU_PRE_TOTAL;// 下一个预设声速
	}
	// 声速存储
	else if((keyId == KEY_UP_LONG_PRES) && (g_Thk.pre_velc_id == 0)) { // 长按+键，存储声速
		uint8_t key_released = 1;
		ClearLED(0x00);
		hanzi[0] = 58; hanzi[1] = 59; hanzi[2] = 60; hanzi[3] = 61; hanzi[4] = 62; // “存储为参数”
		sprintf(str, "%d ", g_Thk.ultraSonic_Velocity);								// 前字符
		sprintf(str1, "%d?(M)", usr_para_id);
		GUI_DispMixText(64, M_LINE_1_Y, ALIGN_CENTER, str, hanzi, str1, 5);		// 结尾字符
		hanzi[0] = 63; 
		GUI_DispMixText(32, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "(+)", 1);	// 确认按键
		OLED_ShowMediumChar(35,M_LINE_2_Y, 96+32);
		hanzi[0] = 64; 
		GUI_DispMixText(96, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "(-)", 1);	// 取消按键
		OLED_ShowMediumChar(99,M_LINE_2_Y, 97+32);
		while(KEY_UP==0){};
			delay_ms(20);											// 等待长按键释放
		while(1){
			if((KEY_UP == 0) || (KEY_DOWN == 0)){							// 确认 取消 被按
				delay_ms(30);
				GUI_DispMixText(32, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "      ", 0);//清除第二栏文字
				GUI_DispMixText(96, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "      ", 0);
				if(KEY_UP == 0) {
					hanzi[0] = 58; hanzi[1] = 59;hanzi[2] = 26;hanzi[3] = 27;	// "存储成功"
					GUI_DispMixText(64, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "!", 4);
					switch(usr_para_id) {
						case 1 : g_Thk.usrSaveVelocity_1 = g_Thk.ultraSonic_Velocity; break;
						case 2 : g_Thk.usrSaveVelocity_2 = g_Thk.ultraSonic_Velocity; break;
						case 3 : g_Thk.usrSaveVelocity_3 = g_Thk.ultraSonic_Velocity; break;
						default:break;
					}
					DataPersistence_SaveAllDatas(0);
				}
				else if(KEY_DOWN == 0) {
					hanzi[0] = 58; hanzi[1] = 59;hanzi[2] = 67;hanzi[3] = 68;	// “存储取消”
					GUI_DispMixText(64, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "!", 4);
					delay_ms(1000);
				}
																// 操作完成的文字显示停留时间
				break;														// 退出while(1)循环
			}
			else if(KEY_SET == 0){												// M键更改预设名称
				delay_ms(10);
				if((KEY_SET == 0) && (key_released==1)) {					// M键被按下，且上一次已释放
					key_released = 0;
					usr_para_id = (usr_para_id == MENU_PRE_USR_MAX) ? 1 :(usr_para_id + 1);
					hanzi[0] = 58; hanzi[1] = 59; hanzi[2] = 60; hanzi[3] = 61; hanzi[4] = 62;// "存储为参数"
					sprintf(str, "%d ", g_Thk.ultraSonic_Velocity);
					sprintf(str1, "%d?(M)", usr_para_id);				// 用户参数序号-如：参数1、参数2、...
					GUI_DispMixText(64, M_LINE_1_Y, ALIGN_CENTER, str, hanzi, str1, 5);
				}
			}
			if(KEY_SET == 1) {
				key_released = 1;											// M键释放标志
			}
		}
		ClearLED(0x00);
	}
	OLED_ShowMenu_Content(1, "            ",12);
	// 显示当前序号 如：2/10
	i = 41;									// 起点X坐标
	OLED_ShowString(i, 7, "   ");			// 清除该区域
	sprintf(str, "%d", g_Thk.pre_velc_id); 
	if(g_Thk.pre_velc_id>9) {						// 两位数时，前点X坐标不同
		OLED_ShowString(i, 7, str); 
	}
	else {
		OLED_ShowString(i+4, 7, str); 
	}
	OLED_ShowChar(i+8, 7, '/'); 
	sprintf(str, "%d", MENU_PRE_TOTAL); OLED_ShowString(i+12, 7, str);
	DrawFocusedTriangle(1);	// 显示选中三角
	if(g_Dev.lang_type == 0) {	// 英文
		GUI_DispString(M_ITEM_L_X, M_LINE_1_Y, ALIGN_LEFT, "Mat.");
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y, ALIGN_LEFT, "Vel.");
		g_Thk.ultraSonic_Velocity = CommonVelcocity_Show(g_Dev.lang_type, g_Thk.pre_velc_id, vel_bak); // 显示预设参数，返回选中材料声速
	}
	else {					// 中文
		hanzi[0] = 73; hanzi[1] = 74; // "材料"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_1_Y, hanzi, 2);
		hanzi[0] = 5; hanzi[1] = 6;	// "声速"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);
		g_Thk.ultraSonic_Velocity = CommonVelcocity_Show(g_Dev.lang_type, g_Thk.pre_velc_id, vel_bak); // 显示预设参数
	}
	if(g_Dev.unit_type==UNIT_SI) sprintf(str, " %d m/s", g_Thk.ultraSonic_Velocity);
	else sprintf(str, "%.4fin/us", UnitConvert_mm2inch((float)g_Thk.ultraSonic_Velocity*1000)/1000000.0);

	OLED_ShowMenu_Content(2, str, strlen(str));
}

static void Menu_VelSetting(uint8_t keyId) {
	char hanzi[20], str[64];
	uint16_t i = 0;
	uint16_t dly_ms=160;
								
	if(keyId == KEY_UP_PRES) {
		if(g_Dev.unit_type==UNIT_SI) g_Thk.ultraSonic_Velocity = g_Thk.ultraSonic_Velocity + 1;
		else g_Thk.ultraSonic_Velocity = g_Thk.ultraSonic_Velocity + UnitConvert_inch2mm(0.0001*1000000)/1000;
		if(g_Thk.pre_velc_id != 0) {
			g_Thk.pre_velc_id = 0;
			OLED_ShowMenu_Content(1, "            ",12);
			// 显示当前序号 如：2/10
			i = 41;									// 起点X坐标
			OLED_ShowString(i, 7, "   ");			// 清除该区域
			sprintf(str, "%d", g_Thk.pre_velc_id); 
			if(g_Thk.pre_velc_id>9) {						// 两位数时，前点X坐标不同
				OLED_ShowString(i, 7, str); 
			}
			else {
				OLED_ShowString(i+4, 7, str); 
			}
			OLED_ShowChar(i+8, 7, '/'); 
			sprintf(str, "%d", MENU_PRE_TOTAL); OLED_ShowString(i+12, 7, str);
			CommonVelcocity_Show(g_Dev.lang_type, g_Thk.pre_velc_id, vel_bak); // 显示预设参数
		}
	}
	else if(keyId == KEY_DOWN_PRES) {
		if(g_Dev.unit_type==UNIT_SI) g_Thk.ultraSonic_Velocity = g_Thk.ultraSonic_Velocity - 1;
		else g_Thk.ultraSonic_Velocity = g_Thk.ultraSonic_Velocity - UnitConvert_inch2mm(0.0001*1000000)/1000;
		if(g_Thk.pre_velc_id != 0) {
			g_Thk.pre_velc_id = 0;
			OLED_ShowMenu_Content(1, "            ",12);
			// 显示当前序号 如：2/10
			i = 41;									// 起点X坐标
			OLED_ShowString(i, 7, "   ");			// 清除该区域
			sprintf(str, "%d", g_Thk.pre_velc_id); 
			if(g_Thk.pre_velc_id>9) {						// 两位数时，前点X坐标不同
				OLED_ShowString(i, 7, str); 
			}
			else {
				OLED_ShowString(i+4, 7, str); 
			}
			OLED_ShowChar(i+8, 7, '/'); 
			sprintf(str, "%d", MENU_PRE_TOTAL); OLED_ShowString(i+12, 7, str);
			CommonVelcocity_Show(g_Dev.lang_type, g_Thk.pre_velc_id, vel_bak); // 显示预设参数
		}
	}
	else if((keyId == KEY_UP_LONG_PRES) || (keyId == KEY_DOWN_LONG_PRES)) {
		while((KEY_UP == 0) || (KEY_DOWN == 0)) {
			delay_ms(dly_ms);
			
			if(i == 6) {
					dly_ms = 100;
					i++;
			}
			else if(i == 20) {
					dly_ms = 30;
					i++;
			}
			else if(i == 60) {
					dly_ms = 3;
			}
			else {
					i++;
			}
			
			if(g_Dev.unit_type==UNIT_SI) g_Thk.ultraSonic_Velocity = (keyId == KEY_UP_LONG_PRES) ? (g_Thk.ultraSonic_Velocity + 1) : (g_Thk.ultraSonic_Velocity - 1);
			else g_Thk.ultraSonic_Velocity = (keyId == KEY_UP_LONG_PRES) ? (g_Thk.ultraSonic_Velocity + UnitConvert_inch2mm(0.0003*1000000)/1000) : (g_Thk.ultraSonic_Velocity - UnitConvert_inch2mm(0.0003*1000000)/1000);
			if(g_Thk.ultraSonic_Velocity > VELOCITY_MAX) g_Thk.ultraSonic_Velocity = VELOCITY_MAX;
			else if(g_Thk.ultraSonic_Velocity < VELOCITY_MIN) g_Thk.ultraSonic_Velocity = VELOCITY_MIN;
			if(g_Dev.unit_type==UNIT_SI) sprintf(str, "%d m/s", g_Thk.ultraSonic_Velocity);
			else sprintf(str, "%.4fin/us", UnitConvert_mm2inch((float)g_Thk.ultraSonic_Velocity*1000)/1000000.0);
			OLED_ShowMenu_Content(2, str, strlen(str));
		}
			
	}
	if(g_Thk.ultraSonic_Velocity > VELOCITY_MAX) g_Thk.ultraSonic_Velocity = VELOCITY_MAX;
	else if(g_Thk.ultraSonic_Velocity < VELOCITY_MIN) g_Thk.ultraSonic_Velocity = VELOCITY_MIN;
	if(g_Dev.unit_type==UNIT_SI) sprintf(str, " %d m/s", g_Thk.ultraSonic_Velocity);
	else sprintf(str, "%.4fin/us", UnitConvert_mm2inch((float)g_Thk.ultraSonic_Velocity*1000)/1000000.0);
	if(g_Dev.lang_type == 0) {
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y, ALIGN_LEFT, "Vel.");
	}
	else {
		hanzi[0] = 5; hanzi[1] = 6;
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);
	}
	OLED_ShowMenu_Content(2, str, strlen(str));
	DrawFocusedTriangle(2);
}



static void Menu_Unit(uint8_t keyId) {
	char hanzi[20];
	if((keyId == KEY_UP_PRES) || (keyId == KEY_DOWN_PRES)) {
		g_Dev.unit_type = !g_Dev.unit_type;
	}
	OLED_ShowMenu_Content(1, "          ",10);
	if(g_Dev.lang_type == 0) {
		GUI_DispString(M_ITEM_L_X, M_LINE_1_Y,ALIGN_LEFT,"Unit");									
		if(g_Dev.unit_type == 0) {
				OLED_ShowMenu_Content(1, "SI(mm) ",6);
		}else{
				OLED_ShowMenu_Content(1, "BS(in.)",6);	
		}
		
		OLED_ShowMenu_Content(2, "English",8);
		hanzi[0] = 17; hanzi[1] = 18;
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);
	}
	else {
		hanzi[0] = 11; hanzi[1] = 12;
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_1_Y, hanzi, 2);
		if(g_Dev.unit_type == 0) {
				hanzi[0] = 13; hanzi[1] = 14; hanzi[2] = 16;
				GUI_DispMixText(M_ALIGN_R+10, 0, 2, "\0", hanzi, "(mm) ", 3);
		}else{
				hanzi[0] = 15; hanzi[1] = 16;
				GUI_DispMixText(M_ALIGN_R+4, 0, 2, "  ", hanzi, "(in.)", 2);
		}
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y,ALIGN_LEFT,"Language");
		hanzi[0] = 19; hanzi[1] = 20; 
		GUI_DispMixText(M_ALIGN_R, M_LINE_2_Y, 2, "\0", hanzi, "\0", 2);
	}
	
	DrawFocusedTriangle(1);
}

static void Menu_Language(uint8_t keyId) {
	char hanzi[20];
	if((keyId == KEY_UP_PRES) || (keyId == KEY_DOWN_PRES)) {
		g_Dev.lang_type = !g_Dev.lang_type;										
	}
	OLED_ShowMenu_Content(2, "          ",10);
	if(g_Dev.lang_type == 0) {
		OLED_ShowMenu_Content(2, "English",8);
	}
	else {
		hanzi[0] = 19; hanzi[1] = 20; 										
		GUI_DispMixText(M_ALIGN_R, M_LINE_2_Y, 2, "\0", hanzi, "\0", 2);
	}
	DrawFocusedTriangle(2);
}


static void Menu_Wifi(uint8_t keyId) {
	char hanzi[20], str[64];
	if((keyId == KEY_UP_PRES) || (keyId == KEY_DOWN_PRES)) {
		if(g_Dev.wifiState == 1) 
			CloseWifi();
		else
			OpenWifi();
	}
	
	if(g_Dev.lang_type == 0) {
		GUI_DispString(M_ITEM_L_X, M_LINE_1_Y,ALIGN_LEFT,"Wifi");
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y,ALIGN_LEFT,"Brightness");
		if(g_Dev.wifiState == 1)
			OLED_ShowMenu_Content(1, " ON",3);
		else
			OLED_ShowMenu_Content(1, "OFF",3);
	}
	else {
		hanzi[0] = 7; hanzi[1] = 8;
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_1_Y, hanzi, 2);
		hanzi[0] = 9; hanzi[1] = 10;
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);
		if(g_Dev.wifiState == 1) {
				hanzi[0] = 21; hanzi[1] = 22; hanzi[2] = 23;
				GUI_DispMixText(M_ALIGN_R+4, 0, 2, "\0", hanzi, "\0", 3);
		}else{
				hanzi[0] = 21; hanzi[1] = 24; hanzi[2] = 25;
				GUI_DispMixText(M_ALIGN_R+4, 0, 2, "\0", hanzi, "\0", 3);
		}
	}
	
	DrawFocusedTriangle(1);
	
	sprintf(str, "%d", g_Dev.brightness_val);
	strcat(str, " %");
	if(g_Dev.brightness_val == 100) {
		OLED_ShowMenu_Content(2, str,5);
	}
	else {
		OLED_ShowMenu_Content(2, "      ", 5);
		OLED_ShowMenu_Content(2, str,4);									
	}
}
static void Menu_OledBright(uint8_t keyId) {
	char hanzi[20], str[64];
	if(keyId == KEY_UP_PRES) {										
		g_Dev.brightness_val = (g_Dev.brightness_val < 100) ? (g_Dev.brightness_val + 10) : 100;
	}
	else if(keyId == KEY_DOWN_PRES) {
		g_Dev.brightness_val = (g_Dev.brightness_val > 10) ? (g_Dev.brightness_val - 10) : 10;
	}
	if(g_Dev.lang_type == 0) {
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y,ALIGN_LEFT, "Brightness");
	}
	else {
		hanzi[0] = 9; hanzi[1] = 10;
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);
	}
	
	sprintf(str, "%d", g_Dev.brightness_val);
	strcat(str, " %");
	if(g_Dev.brightness_val == 100) {
			OLED_ShowMenu_Content(2, str,5);
	}
	else {
			OLED_ShowMenu_Content(2, "      ", 5);
			OLED_ShowMenu_Content(2, str, 4);									
	}
	DrawFocusedTriangle(2);
	OLED_Brightness(g_Dev.brightness_val);
}

static void Menu_Avg(uint8_t keyId) {
	char hanzi[20], str[64];
	uint16_t avg_show = 0;
	if(keyId == KEY_UP_PRES) {
			g_Emat.US_avgTimes= (g_Emat.US_avgTimes < 1024) ? (g_Emat.US_avgTimes*2) : 1024;
	}
	else if(keyId == KEY_DOWN_PRES) {
			g_Emat.US_avgTimes = (g_Emat.US_avgTimes > 8) ? (g_Emat.US_avgTimes/2) : 8;
	}
	if(g_Dev.lang_type == 0) {
		GUI_DispString(M_ITEM_L_X, M_LINE_1_Y,ALIGN_LEFT, "Average");
		// 显示下一栏
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y,ALIGN_LEFT, "Ref.");
		if(g_Thk.referenceBitOn == 1)
			OLED_ShowMenu_Content(2, " ON",3);
		else
			OLED_ShowMenu_Content(2, "OFF",3);
	}
	else {
		hanzi[0] = 69; hanzi[1] = 70;
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_1_Y, hanzi, 2);
		// 显示下一栏
		hanzi[0] = 75; hanzi[1] = 76; hanzi[2] = 77;
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 3);
		if(g_Thk.referenceBitOn == 1) {
			hanzi[0] = 22;
			GUI_DispMixText(M_ALIGN_R+4, M_LINE_2_Y, 2, "\0", hanzi, "\0", 1);
		}else{
			hanzi[0] = 24;
			GUI_DispMixText(M_ALIGN_R+4, M_LINE_2_Y, 2, "\0", hanzi, "\0", 1);
		}
	}
	avg_show = g_Emat.US_avgTimes/64;
	if(avg_show == 4) avg_show = 3;
	sprintf(str, "%d", g_Emat.US_avgTimes);
	OLED_ShowMenu_Content(1, "      ", 5);
	OLED_ShowMenu_Content(1, str,4);
	
	DrawFocusedTriangle(1);
	
}

static void Menu_RefBit(uint8_t keyId) {
	char hanzi[20];

	if(keyId == KEY_UP_PRES) {
		g_Thk.referenceBitOn= (g_Thk.referenceBitOn == 0) ? 1 : 0;
	}
	else if(keyId == KEY_DOWN_PRES) {
		g_Thk.referenceBitOn= (g_Thk.referenceBitOn == 0) ? 1 : 0;
	}
	if(g_Dev.lang_type == 0) {
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y,ALIGN_LEFT, "Ref.");
		if(g_Thk.referenceBitOn == 1)
			OLED_ShowMenu_Content(2, " ON",3);
		else
			OLED_ShowMenu_Content(2, "OFF",3);
	}
	else {
		hanzi[0] = 75; hanzi[1] = 76; hanzi[2] = 77;
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 3);
		if(g_Thk.referenceBitOn == 1) {
			hanzi[0] = 22;
			GUI_DispMixText(M_ALIGN_R+4, M_LINE_2_Y, 2, "\0", hanzi, "\0", 1);
		}else{
			hanzi[0] = 24;
			GUI_DispMixText(M_ALIGN_R+4, M_LINE_2_Y, 2, "\0", hanzi, "\0", 1);
		}
	}

	DrawFocusedTriangle(2);
}



static void Menu_Function(uint8_t keyId) {
	char hanzi[20];

	if(keyId == KEY_UP_PRES) {
		if(g_Dev.functionMode > 0) 
			g_Dev.functionMode--;
	}
	else if(keyId == KEY_DOWN_PRES) {
		if(g_Dev.functionMode < 2) 
			g_Dev.functionMode++;
	}
	
	//若不支持螺栓, 则忽略此功能
	if(DEV_FUNC_SUPPORT_BOLT == 0) {

	}
	//若不支持长测厚, 则忽略此功能
	if(DEV_FUNC_SUPPORT_LONG == 0) {
		if(g_Dev.functionMode == FUNC_THICKNESS_LONG) {
			g_Dev.functionMode = FUNC_THICKNESS_COMMON;
		}
	}
	
	ClearLED(0x00);
	GUI_DispString(M_ITEM_L_X, M_LINE_1_Y,ALIGN_LEFT, "Func.");
	if(g_Dev.functionMode == FUNC_THICKNESS_COMMON)
		OLED_ShowMenu_Content(1, "      THK", 9);
	else if(g_Dev.functionMode == FUNC_THICKNESS_LONG)
		OLED_ShowMenu_Content(1, "THK-Ultra", 9);


	DrawFocusedTriangle(1);
	
	g_Thk.longMode = (g_Dev.functionMode == FUNC_THICKNESS_LONG);
}



uint8_t App_Menu_MainTask(void) {
	uint8_t keyId;
	char str[64];
	char hanzi[20];
	uint8_t first = 1;
	g_Dev.hmi_menu_id = M_ITEM_PRE_VECL;
	while(1) {
		keyId = KEY_Scan(0);
		if((keyId == 0) && (first == 0))
			continue;
		
		first = 0;
		vel_bak = g_Thk.ultraSonic_Velocity;
		// Level 1 -- 菜单
		if(keyId == KEY_SET_PRES) {				// M 键切换项目
			g_Dev.hmi_menu_id = (g_Dev.hmi_menu_id < (MENU_ITEM_TOTAL-1)) ? (g_Dev.hmi_menu_id+1) : 0;
			
			if(g_Dev.hmi_menu_id == M_ITEM_PRE_VECL) { 	
				vel_bak = g_Thk.ultraSonic_Velocity; // 若是预设材料项，则存储当前声速
			}
			if(g_Dev.hmi_menu_id == M_ITEM_VELC) {
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_LANG) {
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_BRIGHT) {
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_CALI){
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_REFBIT){
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_DEV_FUNC){
				if((DEV_FUNC_SUPPORT_BOLT == 0) && (DEV_FUNC_SUPPORT_LONG == 0)) {
					g_Dev.hmi_menu_id = 0;
				}
				ClearLED(0x00);
			}
			else {
				ClearLED(0x00);
			}
		}
		else if(keyId == KEY_SET_LONG_PRES) {
//			quitMenu();
			g_Dev.hmi_level = 0;
			ClearLED(0x00);
			BatteryHandler(1);// 强制刷新电量显示
			DataPersistence_SaveAllDatas(0);
			
			if(g_Dev.functionMode == FUNC_THICKNESS_COMMON)
				Thk_DispMainUI();
			else if(g_Dev.functionMode == FUNC_THICKNESS_LONG)
				Thk_DispMainUI();
			
			return 0;
		}

		if((g_Dev.hmi_menu_id == M_ITEM_CALI_SAMPLE) || (g_Dev.hmi_menu_id == M_ITEM_CALI)) { // 校准声速
			if(g_Dev.hmi_menu_id == M_ITEM_CALI_SAMPLE) {//设置校准件的厚度,小数点后两位10.00mm
				Menu_CaliSample(keyId);
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_CALI) {
				Menu_Cali(keyId);
			}
			if(g_Dev.lang_type == 0) { //英文
				GUI_DispString(M_ITEM_L_X, M_LINE_1_Y, ALIGN_LEFT, "Depth");
				if(g_Dev.unit_type==UNIT_SI) snprintf(str, sizeof(str), "%0.2f mm", g_Thk.caliThicknessVal);
				else snprintf(str, sizeof(str), "%0.3f in.", UnitConvert_mm2inch(g_Thk.caliThicknessVal));
				GUI_DispMixText(M_ALIGN_R, 0, 2, "  ", hanzi, str, 0);
				GUI_DispString(M_ITEM_L_X, M_LINE_2_Y, ALIGN_LEFT, "Calib.");
				GUI_DispMixText(M_ALIGN_R, M_LINE_2_Y, 2, "Press", hanzi, "     ", 0);
				OLED_ShowMediumChar(M_ALIGN_R-18,M_LINE_2_Y, 96+32); // up arrow
			}
			else {
				hanzi[0] = 51; hanzi[1] = 52; hanzi[2] = 53;	// "试样厚"
				OLED_ShowCHN(M_ITEM_L_X, M_LINE_1_Y, hanzi, 3);
				if(g_Dev.unit_type==UNIT_SI) 
					snprintf(str, sizeof(str), "%0.2f mm", g_Thk.caliThicknessVal);			//Display Bug
				else 
					snprintf(str, sizeof(str), "%.3f in.", UnitConvert_mm2inch(g_Thk.caliThicknessVal));
				GUI_DispMixText(M_ALIGN_R, 0, 2, "  ", hanzi, str, 0);
				hanzi[0] = 0; hanzi[1] = 1; // "校准"
				OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);
				hanzi[0] = 2;
				GUI_DispMixText(M_ALIGN_R, M_LINE_2_Y, 2, "\0", hanzi, "     ", 1);
				OLED_ShowMediumChar(M_ALIGN_R-18,M_LINE_2_Y, 96+32); // up arrow
			}								
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_PRE_VECL) { // 预设声速选择
			Menu_MaterialVel(keyId);
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_VELC) { // 声速
			Menu_VelSetting(keyId);
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_UNIT) {	// Unit
			Menu_Unit(keyId);
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_LANG) { // language
			Menu_Language(keyId);
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_WIFI) {	//WIFI
			Menu_Wifi(keyId);
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_BRIGHT) { // Brightness
			Menu_OledBright(keyId);
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_AVG) { // 平均次数设置
			Menu_Avg(keyId);
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_REFBIT) { // 参考位开关
			Menu_RefBit(keyId);
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_DEV_FUNC) { // 功能选择
			Menu_Function(keyId);
		}
		
		if(g_Dev.hmi_menu_id == M_ITEM_BRIGHT && g_Dev.brightness_val == 100) {
			if(UiFactory_EnterMainTaskDetection(keyId) != 0) {
				ClearLED(0x00);
				Menu_Wifi(KEY_NULL_PRES);
				Menu_OledBright(KEY_NULL_PRES);
			}
		}
		
		Draw_Progressbar(g_Dev.hmi_menu_id, MENU_ITEM_TOTAL);
		keyId = KEY_NULL_PRES;
	}
}



	