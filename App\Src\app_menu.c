#include "app_menu.h"          // 包含应用菜单头文件
#include <stdio.h>             // 包含标准输入输出函数库
#include <string.h>            // 包含字符串操作函数库
#include "key.h"               // 包含按键处理头文件
#include "oled.h"              // 包含OLED显示屏头文件
#include "app_thickness.h"     // 包含厚度测量应用头文件
#include "app_stressdetector.h" // 包含应力检测应用头文件
#include "delay.h"             // 包含延时功能头文件
#include "wifi.h"              // 包含WiFi功能头文件
#include "adc.h"               // 包含模数转换器头文件
#include "app_cali.h"          // 包含校准应用头文件
#include "fpga.h"              // 包含FPGA控制头文件
#include "data_persistence.h"  // 包含数据持久化头文件
#include "bsp_zpt.h"           // 包含零点时间板级支持包头文件
#include "unit_utils.h"        // 包含单位转换工具头文件
#include "ui_factory.h"        // 包含工厂界面头文件

static uint16_t vel_bak;       // 静态变量，备份声速值，用于恢复操作

static void DrawFocusedTriangle(uint8_t line) { // 绘制焦点三角形指示器的静态函数，参数为行号
	OLED_ShowMediumChar(2, (line == 1) ? M_LINE_2_Y : M_LINE_1_Y, ' '); // 清除另一行的三角形（显示空格）
	OLED_ShowMediumChar(2, (line == 1) ? M_LINE_1_Y : M_LINE_2_Y, 0x7F); // 在指定行显示三角形字符（0x7F）
}

static void Menu_CaliSample(uint8_t keyId) {              // 校准样品厚度设置菜单的静态函数，参数为按键ID
	char hanzi[20], str[64];                              // 定义汉字缓冲区和字符串缓冲区
	uint32_t i, dly_ms = 160;                             // 定义循环计数器和延时时间，初始延时160毫秒
	if(keyId == KEY_UP_PRES) {                            // 如果UP键被按下
		if(g_Dev.unit_type==UNIT_SI) g_Thk.caliThicknessVal = g_Thk.caliThicknessVal + 0.01; // 国际制单位：增加0.01毫米
		else g_Thk.caliThicknessVal = g_Thk.caliThicknessVal + UnitConvert_inch2mm(0.001); // 英制单位：增加0.001英寸对应的毫米值
	}
	else if(keyId == KEY_DOWN_PRES) {                     // 如果DOWN键被按下
		if(g_Dev.unit_type==UNIT_SI) g_Thk.caliThicknessVal = g_Thk.caliThicknessVal - 0.01; // 国际制单位：减少0.01毫米
		else g_Thk.caliThicknessVal = g_Thk.caliThicknessVal - UnitConvert_inch2mm(0.001); // 英制单位：减少0.001英寸对应的毫米值
	}
	else if((keyId == KEY_UP_LONG_PRES) || (keyId == KEY_DOWN_LONG_PRES)) { // 如果UP键或DOWN键被长按
		i = 0;                                            // 初始化计数器
		while((KEY_UP == 0) || (KEY_DOWN == 0)) {         // 当按键持续按下时循环
			delay_ms(dly_ms);                             // 延时
			if(i == 6) {                                  // 按下6次后
				dly_ms = 100;                             // 减少延时到100毫秒，加快调整速度
				i++;
			}
			else if(i == 20) {                            // 按下20次后
				dly_ms = 30;                              // 进一步减少延时到30毫秒
				i++;
			}
			else if(i == 60) {                            // 按下60次后
				dly_ms = 1;                               // 延时减少到1毫秒，最快调整速度
			}
			else {                                        // 其他情况
				i++;                                      // 计数器递增
			}

			if(g_Dev.unit_type==UNIT_SI) g_Thk.caliThicknessVal = (keyId == KEY_UP_LONG_PRES) ? (g_Thk.caliThicknessVal + 0.03) : (g_Thk.caliThicknessVal - 0.03); // 国际制：每次调整0.03毫米
			else g_Thk.caliThicknessVal = (keyId == KEY_UP_LONG_PRES) ? (g_Thk.caliThicknessVal + UnitConvert_inch2mm(0.03)) : (g_Thk.caliThicknessVal - UnitConvert_inch2mm(0.03)); // 英制：每次调整0.03英寸对应的毫米值
			if(g_Thk.caliThicknessVal > CALI_THICKNESS_MAX) g_Thk.caliThicknessVal = CALI_THICKNESS_MAX; // 限制最大值
			else if(g_Thk.caliThicknessVal < CALI_THICKNESS_MIN) g_Thk.caliThicknessVal = CALI_THICKNESS_MIN; // 限制最小值
			if(g_Dev.unit_type==UNIT_SI) sprintf(str, "%.2f mm", g_Thk.caliThicknessVal); // 国际制：格式化显示毫米值
			else sprintf(str, "%.3f in.", UnitConvert_mm2inch(g_Thk.caliThicknessVal)); // 英制：格式化显示英寸值
			GUI_DispMixText(M_ALIGN_R, 0, 2, "  ", hanzi, str, 0); // 实时更新显示
		}
	}
	if(g_Thk.caliThicknessVal > CALI_THICKNESS_MAX) g_Thk.caliThicknessVal = CALI_THICKNESS_MAX; // 再次检查并限制最大值
	else if(g_Thk.caliThicknessVal < CALI_THICKNESS_MIN) g_Thk.caliThicknessVal = CALI_THICKNESS_MIN; // 再次检查并限制最小值
	DrawFocusedTriangle(1);                               // 绘制焦点三角形在第1行
}

static void Menu_Cali(uint8_t keyId) {                   // 校准菜单的静态函数，参数为按键ID
	char hanzi[20], str[64];                              // 定义汉字缓冲区和字符串缓冲区

	if(keyId == KEY_UP_PRES) { //校准声速               // 如果UP键被按下，执行声速校准
		ClearLED(0x00);                                   // 清除OLED显示屏
		if(g_Dev.lang_type == 0) {                        // 如果语言设置为英文
			GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "Calibrating", 0); // 显示"Calibrating"
			if( Calibrate_Velocity((uint32_t)(g_Thk.caliThicknessVal*1000)) == 0) { // 调用声速校准函数，传入厚度值（转换为微米）
				ClearLED(0x00);                           // 清除显示屏
				GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "Done!", 0); // 显示"Done!"表示校准成功
				if(g_Dev.unit_type==UNIT_SI) sprintf(str, " %d m/s", g_Thk.ultraSonic_Velocity); // 国际制：显示米/秒
				else sprintf(str, "%.4fin/us", UnitConvert_mm2inch((float)g_Thk.ultraSonic_Velocity*1000)/1000000.0); // 英制：显示英寸/微秒
				GUI_DispMixText(64, M_LINE_2_Y, 1, "Vel. ", hanzi, str, 0); // 显示校准后的声速值
				g_Thk.pre_velc_id = 0;                    // 重置预设声速ID为0（当前材料）
			}
			else {                                        // 如果校准失败
				ClearLED(0x00);                           // 清除显示屏
				GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "Failed!", 0); // 显示"Failed!"
			}

		}
		else {                                            // 如果语言设置为中文
			hanzi[0] = 0; hanzi[1] = 1; hanzi[2] = 19;// "校准中" // 设置汉字编码"校准中"
			GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "\0", 3); // 显示"校准中"
			if( Calibrate_Velocity((uint32_t)(g_Thk.caliThicknessVal*1000)) == 0) { // 调用声速校准函数
				ClearLED(0x00);                           // 清除显示屏
				hanzi[0] = 0; hanzi[1] = 1; hanzi[2] = 26; hanzi[3] = 27; // "校准成功" // 设置汉字编码"校准成功"
				GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "\0", 4); // 显示"校准成功"
				hanzi[0] = 5; hanzi[1] = 6;               // "声速" // 设置汉字编码"声速"
				if(g_Dev.unit_type==UNIT_SI) sprintf(str, " %d m/s", g_Thk.ultraSonic_Velocity); // 国际制：格式化声速值
				else sprintf(str, "%.4fin/us", UnitConvert_mm2inch((float)g_Thk.ultraSonic_Velocity*1000)/1000000.0); // 英制：格式化声速值
				GUI_DispMixText(64, M_LINE_2_Y, 1, "\0", hanzi, str, 2); // 显示"声速"和数值
				g_Thk.pre_velc_id = 0;                    // 重置预设声速ID为0
			}
			else {                                        // 如果校准失败
				ClearLED(0x00);                           // 清除显示屏
				hanzi[0] = 0; hanzi[1] = 1; hanzi[2] = 28; hanzi[3] = 29; // "校准失败" // 设置汉字编码"校准失败"
				GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, "\0", 4); // 显示"校准失败"
			}
		}
		SendParameter2FPGA(g_Daq.fs_MHz, g_Emat.US_avgTimes); //恢复校准前的参数 // 向FPGA发送参数，恢复校准前的设置
		delay_ms(1000);delay_ms(800);ClearLED(0x00);      // 延时1.8秒后清除显示屏
		DrawFocusedTriangle(2);                           // 绘制焦点三角形在第2行
	}
	else if(keyId == KEY_DOWN_LONG_PRES) { //下箭头键, 校准零点 // 如果DOWN键被长按，执行零点相关操作
		delay_ms(1000);                                   // 延时1秒
		if((KEY_SET == 0) && (KEY_DOWN == 0))  //长按下 和 M键 // 如果同时按下SET键和DOWN键
		{

			ClearLED(0x00);                               // 清除OLED显示屏
			sprintf(str, "FW:%d", g_Dev.firmWareVersion); // 格式化固件版本信息
			GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, str, 0); // 显示固件版本
			hanzi[0] = 78; hanzi[1] = 79;// "零点"         // 设置汉字编码"零点"
			sprintf(str, " %d ns", g_Zpt.zeroPoint_ns);   // 格式化零点时间值（纳秒）
			GUI_DispMixText(64, M_LINE_2_Y, 1, "\0", hanzi, str, 2); // 显示"零点"和时间值
			delay_ms(1000);                               // 延时1秒
			if(KEY_DOWN == 1) {//下键已释放,只显示零点      // 如果DOWN键已释放，进入零点手动调整模式
				while(1) {                                // 无限循环，等待用户操作
					if(KEY_UP == 0) {                     // 如果UP键被按下
						delay_ms(10);                     // 延时10毫秒消除抖动
						if(KEY_UP == 0)                   // 再次确认UP键被按下
							g_Zpt.zeroPoint_ns = g_Zpt.zeroPoint_ns + 1; // 零点时间增加1纳秒
					}
					else if(KEY_DOWN == 0) {              // 如果DOWN键被按下
						delay_ms(10);                     // 延时10毫秒消除抖动
						if(KEY_DOWN == 0)                 // 再次确认DOWN键被按下
							g_Zpt.zeroPoint_ns = g_Zpt.zeroPoint_ns - 1; // 零点时间减少1纳秒
					}
					hanzi[0] = 78; hanzi[1] = 79;// "零点" // 重新设置汉字编码"零点"
					sprintf(str, " %d ns", g_Zpt.zeroPoint_ns); // 格式化更新后的零点时间值
					GUI_DispMixText(64, M_LINE_2_Y, 1, "\0", hanzi, str, 2); // 实时更新显示
					if(KEY_SET == 0) {                    // 如果SET键被按下
						delay_ms(10);                     // 延时10毫秒消除抖动
						if(KEY_SET == 0) {                // 再次确认SET键被按下
							DataPersistence_SaveAllDatas(1); // 保存所有数据到持久化存储
							break;                        // 跳出循环
						}
					}
					delay_ms(100);                        // 延时100毫秒
				}
				ClearLED(0x00);                           // 清除OLED显示屏
				DrawFocusedTriangle(2);                   // 绘制焦点三角形在第2行
			}
			else if(KEY_DOWN == 0) {                      // 如果DOWN键仍被按下，进入自动零点校准模式
				ClearLED(0x00);                           // 清除OLED显示屏
				hanzi[0]=0; hanzi[1]=1; hanzi[2]=78; hanzi[3]=79; hanzi[4]=19; //"校准零点中" // 设置汉字编码"校准零点中"
				GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi,  g_Dev.lang_type?"\0":"Calibrating Zero", g_Dev.lang_type?5:0); // 根据语言显示校准提示

				if(Calibrate_ZeroPoint((uint32_t)(g_Thk.caliThicknessVal*1000)) == 0) { // 调用零点校准函数，传入厚度值（转换为微米）
					ClearLED(0x00);                       // 清除显示屏
					hanzi[0] = 0; hanzi[1] = 1; hanzi[2] = 26; hanzi[3] = 27; str[0] = 0x00; //"校准成功" // 设置汉字编码"校准成功"
					GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, g_Dev.lang_type?"\0":"Done!", g_Dev.lang_type?4:0); // 根据语言显示成功信息

					hanzi[0] = 78; hanzi[1] = 79; str[0] = 0x00; //"" // 设置汉字编码"零点"
					sprintf(str, " %d ns", g_Zpt.zeroPoint_ns); // 格式化零点时间值
					GUI_DispMixText(64, M_LINE_2_Y, 1, g_Dev.lang_type?"\0":"Zero. ", hanzi, str, g_Dev.lang_type?2:0); // 根据语言显示零点值
					g_Thk.pre_velc_id = 0;                // 重置预设声速ID为0
				}
				else {                                    // 如果零点校准失败
					ClearLED(0x00);                       // 清除显示屏
					hanzi[0] = 0; hanzi[1] = 1; hanzi[2] = 28; hanzi[3] = 29; //"校准失败" // 设置汉字编码"校准失败"
					GUI_DispMixText(64, M_LINE_1_Y, 1, "\0", hanzi, g_Dev.lang_type?"\0":"Failed!", g_Dev.lang_type?4:0); // 根据语言显示失败信息
				}

				DataPersistence_SaveAllDatas(1);         // 保存所有数据到持久化存储
				SendParameter2FPGA(g_Daq.fs_MHz, g_Emat.US_avgTimes); //恢复校准前的参数 // 向FPGA发送参数，恢复校准前的设置
				delay_ms(1000);delay_ms(800);            // 延时1.8秒
				ClearLED(0x00);                           // 清除显示屏
				DrawFocusedTriangle(2);                   // 绘制焦点三角形在第2行
			}
		}
	}
	else {                                                // 如果没有按键操作
		DrawFocusedTriangle(2);                           // 绘制焦点三角形在第2行
	}
}

static void Menu_MaterialVel(uint8_t keyId) {            // 材料声速选择菜单的静态函数，参数为按键ID
	char hanzi[20], str[64], str1[64];                    // 定义汉字缓冲区和字符串缓冲区
	uint32_t i;                                           // 定义循环变量
	static uint32_t usr_para_id = 1;                      // 静态变量，用户参数ID，初始值为1

	if(keyId == KEY_UP_PRES) {                            // 如果UP键被按下
		g_Thk.pre_velc_id = (g_Thk.pre_velc_id < 1) ? 0 : (g_Thk.pre_velc_id - 1); 	// 上一个预设声速，最小值为0
	}
	else if(keyId == KEY_DOWN_PRES) {                     // 如果DOWN键被按下
		g_Thk.pre_velc_id = (g_Thk.pre_velc_id < MENU_PRE_TOTAL) ? (g_Thk.pre_velc_id + 1) : MENU_PRE_TOTAL;// 下一个预设声速，最大值为MENU_PRE_TOTAL
	}
	// 声速存储
	else if((keyId == KEY_UP_LONG_PRES) && (g_Thk.pre_velc_id == 0)) { // 长按+键，存储声速
		uint8_t key_released = 1;
		ClearLED(0x00);
		hanzi[0] = 58; hanzi[1] = 59; hanzi[2] = 60; hanzi[3] = 61; hanzi[4] = 62; // “存储为参数”
		sprintf(str, "%d ", g_Thk.ultraSonic_Velocity);								// 前字符
		sprintf(str1, "%d?(M)", usr_para_id);
		GUI_DispMixText(64, M_LINE_1_Y, ALIGN_CENTER, str, hanzi, str1, 5);		// 结尾字符
		hanzi[0] = 63; 
		GUI_DispMixText(32, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "(+)", 1);	// 确认按键
		OLED_ShowMediumChar(35,M_LINE_2_Y, 96+32);
		hanzi[0] = 64; 
		GUI_DispMixText(96, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "(-)", 1);	// 取消按键
		OLED_ShowMediumChar(99,M_LINE_2_Y, 97+32);
		while(KEY_UP==0){};
			delay_ms(20);											// 等待长按键释放
		while(1){
			if((KEY_UP == 0) || (KEY_DOWN == 0)){							// 确认 取消 被按
				delay_ms(30);
				GUI_DispMixText(32, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "      ", 0);//清除第二栏文字
				GUI_DispMixText(96, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "      ", 0);
				if(KEY_UP == 0) {
					hanzi[0] = 58; hanzi[1] = 59;hanzi[2] = 26;hanzi[3] = 27;	// "存储成功"
					GUI_DispMixText(64, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "!", 4);
					switch(usr_para_id) {
						case 1 : g_Thk.usrSaveVelocity_1 = g_Thk.ultraSonic_Velocity; break;
						case 2 : g_Thk.usrSaveVelocity_2 = g_Thk.ultraSonic_Velocity; break;
						case 3 : g_Thk.usrSaveVelocity_3 = g_Thk.ultraSonic_Velocity; break;
						default:break;
					}
					DataPersistence_SaveAllDatas(0);
				}
				else if(KEY_DOWN == 0) {
					hanzi[0] = 58; hanzi[1] = 59;hanzi[2] = 67;hanzi[3] = 68;	// “存储取消”
					GUI_DispMixText(64, M_LINE_2_Y, ALIGN_CENTER, "\0", hanzi, "!", 4);
					delay_ms(1000);
				}
																// 操作完成的文字显示停留时间
				break;														// 退出while(1)循环
			}
			else if(KEY_SET == 0){												// M键更改预设名称
				delay_ms(10);
				if((KEY_SET == 0) && (key_released==1)) {					// M键被按下，且上一次已释放
					key_released = 0;
					usr_para_id = (usr_para_id == MENU_PRE_USR_MAX) ? 1 :(usr_para_id + 1);
					hanzi[0] = 58; hanzi[1] = 59; hanzi[2] = 60; hanzi[3] = 61; hanzi[4] = 62;// "存储为参数"
					sprintf(str, "%d ", g_Thk.ultraSonic_Velocity);
					sprintf(str1, "%d?(M)", usr_para_id);				// 用户参数序号-如：参数1、参数2、...
					GUI_DispMixText(64, M_LINE_1_Y, ALIGN_CENTER, str, hanzi, str1, 5);
				}
			}
			if(KEY_SET == 1) {
				key_released = 1;											// M键释放标志
			}
		}
		ClearLED(0x00);
	}
	OLED_ShowMenu_Content(1, "            ",12);
	// 显示当前序号 如：2/10
	i = 41;									// 起点X坐标
	OLED_ShowString(i, 7, "   ");			// 清除该区域
	sprintf(str, "%d", g_Thk.pre_velc_id); 
	if(g_Thk.pre_velc_id>9) {						// 两位数时，前点X坐标不同
		OLED_ShowString(i, 7, str); 
	}
	else {
		OLED_ShowString(i+4, 7, str); 
	}
	OLED_ShowChar(i+8, 7, '/'); 
	sprintf(str, "%d", MENU_PRE_TOTAL); OLED_ShowString(i+12, 7, str);
	DrawFocusedTriangle(1);	// 显示选中三角
	if(g_Dev.lang_type == 0) {	// 英文
		GUI_DispString(M_ITEM_L_X, M_LINE_1_Y, ALIGN_LEFT, "Mat.");
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y, ALIGN_LEFT, "Vel.");
		g_Thk.ultraSonic_Velocity = CommonVelcocity_Show(g_Dev.lang_type, g_Thk.pre_velc_id, vel_bak); // 显示预设参数，返回选中材料声速
	}
	else {					// 中文
		hanzi[0] = 73; hanzi[1] = 74; // "材料"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_1_Y, hanzi, 2);
		hanzi[0] = 5; hanzi[1] = 6;	// "声速"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);
		g_Thk.ultraSonic_Velocity = CommonVelcocity_Show(g_Dev.lang_type, g_Thk.pre_velc_id, vel_bak); // 显示预设参数
	}
	if(g_Dev.unit_type==UNIT_SI) sprintf(str, " %d m/s", g_Thk.ultraSonic_Velocity);
	else sprintf(str, "%.4fin/us", UnitConvert_mm2inch((float)g_Thk.ultraSonic_Velocity*1000)/1000000.0);

	OLED_ShowMenu_Content(2, str, strlen(str));
}

static void Menu_VelSetting(uint8_t keyId) {             // 声速设置菜单的静态函数，参数为按键ID
	char hanzi[20], str[64];                              // 定义汉字缓冲区和字符串缓冲区
	uint16_t i = 0;                                       // 定义循环计数器，初始为0
	uint16_t dly_ms=160;                                  // 定义延时时间，初始为160毫秒

	if(keyId == KEY_UP_PRES) {                            // 如果UP键被按下
		if(g_Dev.unit_type==UNIT_SI) g_Thk.ultraSonic_Velocity = g_Thk.ultraSonic_Velocity + 1; // 国际制：声速增加1 m/s
		else g_Thk.ultraSonic_Velocity = g_Thk.ultraSonic_Velocity + UnitConvert_inch2mm(0.0001*1000000)/1000; // 英制：声速增加对应的英制单位值
		if(g_Thk.pre_velc_id != 0) {                      // 如果当前不是"当前材料"模式
			g_Thk.pre_velc_id = 0;                        // 切换到"当前材料"模式
			OLED_ShowMenu_Content(1, "            ",12);   // 清除第一行内容
			// 显示当前序号 如：2/10                       // 注释：显示材料序号
			i = 41;                                       // 起点X坐标 // 设置显示起始X坐标
			OLED_ShowString(i, 7, "   ");                 // 清除该区域 // 清除原有序号显示
			sprintf(str, "%d", g_Thk.pre_velc_id);        // 格式化当前预设ID
			if(g_Thk.pre_velc_id>9) {                     // 两位数时，前点X坐标不同 // 如果是两位数
				OLED_ShowString(i, 7, str);               // 在起始位置显示
			}
			else {                                        // 如果是一位数
				OLED_ShowString(i+4, 7, str);             // 向右偏移4像素显示
			}
			OLED_ShowChar(i+8, 7, '/');                   // 显示分隔符'/'
			sprintf(str, "%d", MENU_PRE_TOTAL); OLED_ShowString(i+12, 7, str); // 显示总数
			CommonVelcocity_Show(g_Dev.lang_type, g_Thk.pre_velc_id, vel_bak); // 显示预设参数 // 更新材料名称显示
		}
	}
	else if(keyId == KEY_DOWN_PRES) {                     // 如果DOWN键被按下
		if(g_Dev.unit_type==UNIT_SI) g_Thk.ultraSonic_Velocity = g_Thk.ultraSonic_Velocity - 1; // 国际制：声速减少1 m/s
		else g_Thk.ultraSonic_Velocity = g_Thk.ultraSonic_Velocity - UnitConvert_inch2mm(0.0001*1000000)/1000; // 英制：声速减少对应的英制单位值
		if(g_Thk.pre_velc_id != 0) {                      // 如果当前不是"当前材料"模式
			g_Thk.pre_velc_id = 0;                        // 切换到"当前材料"模式
			OLED_ShowMenu_Content(1, "            ",12);   // 清除第一行内容
			// 显示当前序号 如：2/10                       // 注释：显示材料序号
			i = 41;                                       // 起点X坐标 // 设置显示起始X坐标
			OLED_ShowString(i, 7, "   ");                 // 清除该区域 // 清除原有序号显示
			sprintf(str, "%d", g_Thk.pre_velc_id);        // 格式化当前预设ID
			if(g_Thk.pre_velc_id>9) {                     // 两位数时，前点X坐标不同 // 如果是两位数
				OLED_ShowString(i, 7, str);               // 在起始位置显示
			}
			else {                                        // 如果是一位数
				OLED_ShowString(i+4, 7, str);             // 向右偏移4像素显示
			}
			OLED_ShowChar(i+8, 7, '/');                   // 显示分隔符'/'
			sprintf(str, "%d", MENU_PRE_TOTAL); OLED_ShowString(i+12, 7, str); // 显示总数
			CommonVelcocity_Show(g_Dev.lang_type, g_Thk.pre_velc_id, vel_bak); // 显示预设参数 // 更新材料名称显示
		}
	}
	else if((keyId == KEY_UP_LONG_PRES) || (keyId == KEY_DOWN_LONG_PRES)) { // 如果UP键或DOWN键被长按
		while((KEY_UP == 0) || (KEY_DOWN == 0)) {         // 当按键持续按下时循环
			delay_ms(dly_ms);                             // 延时

			if(i == 6) {                                  // 按下6次后
					dly_ms = 100;                         // 减少延时到100毫秒，加快调整速度
					i++;
			}
			else if(i == 20) {                            // 按下20次后
					dly_ms = 30;                          // 进一步减少延时到30毫秒
					i++;
			}
			else if(i == 60) {                            // 按下60次后
					dly_ms = 3;                           // 延时减少到3毫秒，最快调整速度
			}
			else {                                        // 其他情况
					i++;                                  // 计数器递增
			}

			if(g_Dev.unit_type==UNIT_SI) g_Thk.ultraSonic_Velocity = (keyId == KEY_UP_LONG_PRES) ? (g_Thk.ultraSonic_Velocity + 1) : (g_Thk.ultraSonic_Velocity - 1); // 国际制：每次调整1 m/s
			else g_Thk.ultraSonic_Velocity = (keyId == KEY_UP_LONG_PRES) ? (g_Thk.ultraSonic_Velocity + UnitConvert_inch2mm(0.0003*1000000)/1000) : (g_Thk.ultraSonic_Velocity - UnitConvert_inch2mm(0.0003*1000000)/1000); // 英制：每次调整对应的英制单位值
			if(g_Thk.ultraSonic_Velocity > VELOCITY_MAX) g_Thk.ultraSonic_Velocity = VELOCITY_MAX; // 限制最大值
			else if(g_Thk.ultraSonic_Velocity < VELOCITY_MIN) g_Thk.ultraSonic_Velocity = VELOCITY_MIN; // 限制最小值
			if(g_Dev.unit_type==UNIT_SI) sprintf(str, "%d m/s", g_Thk.ultraSonic_Velocity); // 国际制：格式化显示
			else sprintf(str, "%.4fin/us", UnitConvert_mm2inch((float)g_Thk.ultraSonic_Velocity*1000)/1000000.0); // 英制：格式化显示
			OLED_ShowMenu_Content(2, str, strlen(str));   // 实时更新显示
		}

	}
	if(g_Thk.ultraSonic_Velocity > VELOCITY_MAX) g_Thk.ultraSonic_Velocity = VELOCITY_MAX; // 再次检查并限制最大值
	else if(g_Thk.ultraSonic_Velocity < VELOCITY_MIN) g_Thk.ultraSonic_Velocity = VELOCITY_MIN; // 再次检查并限制最小值
	if(g_Dev.unit_type==UNIT_SI) sprintf(str, " %d m/s", g_Thk.ultraSonic_Velocity); // 国际制：格式化最终显示值
	else sprintf(str, "%.4fin/us", UnitConvert_mm2inch((float)g_Thk.ultraSonic_Velocity*1000)/1000000.0); // 英制：格式化最终显示值
	if(g_Dev.lang_type == 0) {                            // 如果语言设置为英文
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y, ALIGN_LEFT, "Vel."); // 显示"Vel."标签
	}
	else {                                                // 如果语言设置为中文
		hanzi[0] = 5; hanzi[1] = 6;                       // 设置汉字编码"声速"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);  // 显示"声速"标签
	}
	OLED_ShowMenu_Content(2, str, strlen(str));           // 显示声速数值
	DrawFocusedTriangle(2);                               // 绘制焦点三角形在第2行
}



static void Menu_Unit(uint8_t keyId) {                   // 单位设置菜单的静态函数，参数为按键ID
	char hanzi[20];                                       // 定义汉字缓冲区
	if((keyId == KEY_UP_PRES) || (keyId == KEY_DOWN_PRES)) { // 如果UP键或DOWN键被按下
		g_Dev.unit_type = !g_Dev.unit_type;               // 切换单位类型（国际制/英制）
	}
	OLED_ShowMenu_Content(1, "          ",10);            // 清除第一行内容
	if(g_Dev.lang_type == 0) {                            // 如果语言设置为英文
		GUI_DispString(M_ITEM_L_X, M_LINE_1_Y,ALIGN_LEFT,"Unit"); // 显示"Unit"标签
		if(g_Dev.unit_type == 0) {                        // 如果单位类型为国际制
				OLED_ShowMenu_Content(1, "SI(mm) ",6);    // 显示"SI(mm)"
		}else{                                            // 如果单位类型为英制
				OLED_ShowMenu_Content(1, "BS(in.)",6);    // 显示"BS(in.)"
		}

		OLED_ShowMenu_Content(2, "English",8);            // 在第二行显示"English"
		hanzi[0] = 17; hanzi[1] = 18;                     // 设置汉字编码"语言"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);  // 显示"语言"标签
	}
	else {                                                // 如果语言设置为中文
		hanzi[0] = 11; hanzi[1] = 12;                     // 设置汉字编码"单位"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_1_Y, hanzi, 2);  // 显示"单位"标签
		if(g_Dev.unit_type == 0) {                        // 如果单位类型为国际制
				hanzi[0] = 13; hanzi[1] = 14; hanzi[2] = 16; // 设置汉字编码"国际制"
				GUI_DispMixText(M_ALIGN_R+10, 0, 2, "\0", hanzi, "(mm) ", 3); // 显示"国际制(mm)"
		}else{                                            // 如果单位类型为英制
				hanzi[0] = 15; hanzi[1] = 16;             // 设置汉字编码"英制"
				GUI_DispMixText(M_ALIGN_R+4, 0, 2, "  ", hanzi, "(in.)", 2); // 显示"英制(in.)"
		}
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y,ALIGN_LEFT,"Language"); // 显示"Language"标签
		hanzi[0] = 19; hanzi[1] = 20;                     // 设置汉字编码"中文"
		GUI_DispMixText(M_ALIGN_R, M_LINE_2_Y, 2, "\0", hanzi, "\0", 2); // 显示"中文"
	}

	DrawFocusedTriangle(1);                               // 绘制焦点三角形在第1行
}

static void Menu_Language(uint8_t keyId) {               // 语言设置菜单的静态函数，参数为按键ID
	char hanzi[20];                                       // 定义汉字缓冲区
	if((keyId == KEY_UP_PRES) || (keyId == KEY_DOWN_PRES)) { // 如果UP键或DOWN键被按下
		g_Dev.lang_type = !g_Dev.lang_type;               // 切换语言类型（中文/英文）
	}
	OLED_ShowMenu_Content(2, "          ",10);            // 清除第二行内容
	if(g_Dev.lang_type == 0) {                            // 如果语言设置为英文
		OLED_ShowMenu_Content(2, "English",8);            // 显示"English"
	}
	else {                                                // 如果语言设置为中文
		hanzi[0] = 19; hanzi[1] = 20;                     // 设置汉字编码"中文"
		GUI_DispMixText(M_ALIGN_R, M_LINE_2_Y, 2, "\0", hanzi, "\0", 2); // 显示"中文"
	}
	DrawFocusedTriangle(2);                               // 绘制焦点三角形在第2行
}


static void Menu_Wifi(uint8_t keyId) {                   // WiFi设置菜单的静态函数，参数为按键ID
	char hanzi[20], str[64];                              // 定义汉字缓冲区和字符串缓冲区
	if((keyId == KEY_UP_PRES) || (keyId == KEY_DOWN_PRES)) { // 如果UP键或DOWN键被按下
		if(g_Dev.wifiState == 1)                          // 如果WiFi当前为开启状态
			CloseWifi();                                  // 关闭WiFi
		else                                              // 如果WiFi当前为关闭状态
			OpenWifi();                                   // 打开WiFi
	}

	if(g_Dev.lang_type == 0) {                            // 如果语言设置为英文
		GUI_DispString(M_ITEM_L_X, M_LINE_1_Y,ALIGN_LEFT,"Wifi"); // 显示"Wifi"标签
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y,ALIGN_LEFT,"Brightness"); // 显示"Brightness"标签
		if(g_Dev.wifiState == 1)                          // 如果WiFi状态为开启
			OLED_ShowMenu_Content(1, " ON",3);            // 显示" ON"
		else                                              // 如果WiFi状态为关闭
			OLED_ShowMenu_Content(1, "OFF",3);            // 显示"OFF"
	}
	else {                                                // 如果语言设置为中文
		hanzi[0] = 7; hanzi[1] = 8;                       // 设置汉字编码"无线"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_1_Y, hanzi, 2);  // 显示"无线"标签
		hanzi[0] = 9; hanzi[1] = 10;                      // 设置汉字编码"亮度"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);  // 显示"亮度"标签
		if(g_Dev.wifiState == 1) {                        // 如果WiFi状态为开启
				hanzi[0] = 21; hanzi[1] = 22; hanzi[2] = 23; // 设置汉字编码"已开启"
				GUI_DispMixText(M_ALIGN_R+4, 0, 2, "\0", hanzi, "\0", 3); // 显示"已开启"
		}else{                                            // 如果WiFi状态为关闭
				hanzi[0] = 21; hanzi[1] = 24; hanzi[2] = 25; // 设置汉字编码"已关闭"
				GUI_DispMixText(M_ALIGN_R+4, 0, 2, "\0", hanzi, "\0", 3); // 显示"已关闭"
		}
	}

	DrawFocusedTriangle(1);                               // 绘制焦点三角形在第1行

	sprintf(str, "%d", g_Dev.brightness_val);            // 格式化亮度值为字符串
	strcat(str, " %");                                    // 在字符串后添加百分号
	if(g_Dev.brightness_val == 100) {                    // 如果亮度值为100%
		OLED_ShowMenu_Content(2, str,5);                 // 显示5个字符（包括百分号）
	}
	else {                                                // 如果亮度值小于100%
		OLED_ShowMenu_Content(2, "      ", 5);           // 先清除显示区域
		OLED_ShowMenu_Content(2, str,4);                 // 显示4个字符
	}
}
static void Menu_OledBright(uint8_t keyId) {             // OLED亮度设置菜单的静态函数，参数为按键ID
	char hanzi[20], str[64];                              // 定义汉字缓冲区和字符串缓冲区
	if(keyId == KEY_UP_PRES) {                            // 如果UP键被按下
		g_Dev.brightness_val = (g_Dev.brightness_val < 100) ? (g_Dev.brightness_val + 10) : 100; // 亮度增加10%，最大100%
	}
	else if(keyId == KEY_DOWN_PRES) {                     // 如果DOWN键被按下
		g_Dev.brightness_val = (g_Dev.brightness_val > 10) ? (g_Dev.brightness_val - 10) : 10; // 亮度减少10%，最小10%
	}
	if(g_Dev.lang_type == 0) {                            // 如果语言设置为英文
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y,ALIGN_LEFT, "Brightness"); // 显示"Brightness"标签
	}
	else {                                                // 如果语言设置为中文
		hanzi[0] = 9; hanzi[1] = 10;                      // 设置汉字编码"亮度"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2);  // 显示"亮度"标签
	}

	sprintf(str, "%d", g_Dev.brightness_val);            // 格式化亮度值为字符串
	strcat(str, " %");                                    // 在字符串后添加百分号
	if(g_Dev.brightness_val == 100) {                    // 如果亮度值为100%
			OLED_ShowMenu_Content(2, str,5);             // 显示5个字符
	}
	else {                                                // 如果亮度值小于100%
			OLED_ShowMenu_Content(2, "      ", 5);       // 先清除显示区域
			OLED_ShowMenu_Content(2, str, 4);            // 显示4个字符
	}
	DrawFocusedTriangle(2);                               // 绘制焦点三角形在第2行
	OLED_Brightness(g_Dev.brightness_val);                // 立即应用新的亮度设置
}

static void Menu_Avg(uint8_t keyId) {                    // 平均次数设置菜单的静态函数，参数为按键ID
	char hanzi[20], str[64];                              // 定义汉字缓冲区和字符串缓冲区
	uint16_t avg_show = 0;                                // 定义平均次数显示变量，初始为0
	if(keyId == KEY_UP_PRES) {                            // 如果UP键被按下
			g_Emat.US_avgTimes= (g_Emat.US_avgTimes < 1024) ? (g_Emat.US_avgTimes*2) : 1024; // 平均次数翻倍，最大1024
	}
	else if(keyId == KEY_DOWN_PRES) {                     // 如果DOWN键被按下
			g_Emat.US_avgTimes = (g_Emat.US_avgTimes > 8) ? (g_Emat.US_avgTimes/2) : 8; // 平均次数减半，最小8
	}
	if(g_Dev.lang_type == 0) {                            // 如果语言设置为英文
		GUI_DispString(M_ITEM_L_X, M_LINE_1_Y,ALIGN_LEFT, "Average"); // 显示"Average"标签
		// 显示下一栏                                      // 注释：显示下一栏内容
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y,ALIGN_LEFT, "Ref."); // 显示"Ref."标签
		if(g_Thk.referenceBitOn == 1)                     // 如果参考位开启
			OLED_ShowMenu_Content(2, " ON",3);            // 显示" ON"
		else                                              // 如果参考位关闭
			OLED_ShowMenu_Content(2, "OFF",3);            // 显示"OFF"
	}
	else {                                                // 如果语言设置为中文
		hanzi[0] = 69; hanzi[1] = 70;                     // 设置汉字编码"平均"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_1_Y, hanzi, 2);  // 显示"平均"标签
		// 显示下一栏                                      // 注释：显示下一栏内容
		hanzi[0] = 75; hanzi[1] = 76; hanzi[2] = 77;      // 设置汉字编码"参考位"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 3);  // 显示"参考位"标签
		if(g_Thk.referenceBitOn == 1) {                   // 如果参考位开启
			hanzi[0] = 22;                                // 设置汉字编码"开"
			GUI_DispMixText(M_ALIGN_R+4, M_LINE_2_Y, 2, "\0", hanzi, "\0", 1); // 显示"开"
		}else{                                            // 如果参考位关闭
			hanzi[0] = 24;                                // 设置汉字编码"关"
			GUI_DispMixText(M_ALIGN_R+4, M_LINE_2_Y, 2, "\0", hanzi, "\0", 1); // 显示"关"
		}
	}
	avg_show = g_Emat.US_avgTimes/64;                     // 计算显示用的平均次数（除以64）
	if(avg_show == 4) avg_show = 3;                       // 如果计算结果为4，则设为3
	sprintf(str, "%d", g_Emat.US_avgTimes);               // 格式化平均次数为字符串
	OLED_ShowMenu_Content(1, "      ", 5);                // 先清除显示区域
	OLED_ShowMenu_Content(1, str,4);                      // 显示平均次数值

	DrawFocusedTriangle(1);                               // 绘制焦点三角形在第1行

}

static void Menu_RefBit(uint8_t keyId) {                 // 参考位设置菜单的静态函数，参数为按键ID
	char hanzi[20];                                       // 定义汉字缓冲区

	if(keyId == KEY_UP_PRES) {                            // 如果UP键被按下
		g_Thk.referenceBitOn= (g_Thk.referenceBitOn == 0) ? 1 : 0; // 切换参考位状态
	}
	else if(keyId == KEY_DOWN_PRES) {                     // 如果DOWN键被按下
		g_Thk.referenceBitOn= (g_Thk.referenceBitOn == 0) ? 1 : 0; // 切换参考位状态
	}
	if(g_Dev.lang_type == 0) {                            // 如果语言设置为英文
		GUI_DispString(M_ITEM_L_X, M_LINE_2_Y,ALIGN_LEFT, "Ref."); // 显示"Ref."标签
		if(g_Thk.referenceBitOn == 1)                     // 如果参考位开启
			OLED_ShowMenu_Content(2, " ON",3);            // 显示" ON"
		else                                              // 如果参考位关闭
			OLED_ShowMenu_Content(2, "OFF",3);            // 显示"OFF"
	}
	else {                                                // 如果语言设置为中文
		hanzi[0] = 75; hanzi[1] = 76; hanzi[2] = 77;      // 设置汉字编码"参考位"
		OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 3);  // 显示"参考位"标签
		if(g_Thk.referenceBitOn == 1) {                   // 如果参考位开启
			hanzi[0] = 22;                                // 设置汉字编码"开"
			GUI_DispMixText(M_ALIGN_R+4, M_LINE_2_Y, 2, "\0", hanzi, "\0", 1); // 显示"开"
		}else{                                            // 如果参考位关闭
			hanzi[0] = 24;                                // 设置汉字编码"关"
			GUI_DispMixText(M_ALIGN_R+4, M_LINE_2_Y, 2, "\0", hanzi, "\0", 1); // 显示"关"
		}
	}

	DrawFocusedTriangle(2);                               // 绘制焦点三角形在第2行
}



static void Menu_Function(uint8_t keyId) {               // 功能选择菜单的静态函数，参数为按键ID
	char hanzi[20];                                       // 定义汉字缓冲区

	if(keyId == KEY_UP_PRES) {                            // 如果UP键被按下
		if(g_Dev.functionMode > 0)                        // 如果功能模式大于0
			g_Dev.functionMode--;                         // 功能模式减1
	}
	else if(keyId == KEY_DOWN_PRES) {                     // 如果DOWN键被按下
		if(g_Dev.functionMode < 2)                        // 如果功能模式小于2
			g_Dev.functionMode++;                         // 功能模式加1
	}

	//若不支持螺栓, 则忽略此功能                           // 注释：螺栓功能支持检查
	if(DEV_FUNC_SUPPORT_BOLT == 0) {                     // 如果设备不支持螺栓功能
                                                          // 空的条件体，暂未实现螺栓功能
	}
	//若不支持长测厚, 则忽略此功能                         // 注释：长距离测厚功能支持检查
	if(DEV_FUNC_SUPPORT_LONG == 0) {                     // 如果设备不支持长距离测厚功能
		if(g_Dev.functionMode == FUNC_THICKNESS_LONG) {  // 如果当前选择的是长距离测厚模式
			g_Dev.functionMode = FUNC_THICKNESS_COMMON;   // 强制切换到普通测厚模式
		}
	}

	ClearLED(0x00);                                       // 清除OLED显示屏
	GUI_DispString(M_ITEM_L_X, M_LINE_1_Y,ALIGN_LEFT, "Func."); // 显示"Func."标签
	if(g_Dev.functionMode == FUNC_THICKNESS_COMMON)      // 如果功能模式为普通测厚
		OLED_ShowMenu_Content(1, "      THK", 9);         // 显示"THK"
	else if(g_Dev.functionMode == FUNC_THICKNESS_LONG)   // 如果功能模式为长距离测厚
		OLED_ShowMenu_Content(1, "THK-Ultra", 9);         // 显示"THK-Ultra"


	DrawFocusedTriangle(1);                               // 绘制焦点三角形在第1行

	g_Thk.longMode = (g_Dev.functionMode == FUNC_THICKNESS_LONG); // 根据功能模式设置长模式标志
}



uint8_t App_Menu_MainTask(void) {                        // 应用菜单主任务函数，返回值为uint8_t类型
	uint8_t keyId;                                        // 定义按键ID变量
	char str[64];                                         // 定义字符串缓冲区
	char hanzi[20];                                       // 定义汉字缓冲区
	uint8_t first = 1;                                    // 定义首次进入标志，初始为1
	g_Dev.hmi_menu_id = M_ITEM_PRE_VECL;                  // 设置菜单ID为预设声速项
	while(1) {                                            // 无限循环，处理菜单操作
		keyId = KEY_Scan(0);                              // 扫描按键状态
		if((keyId == 0) && (first == 0))                  // 如果没有按键且不是首次进入
			continue;                                     // 继续循环

		first = 0;                                        // 清除首次进入标志
		vel_bak = g_Thk.ultraSonic_Velocity;              // 备份当前声速值
		// Level 1 -- 菜单                                // 注释：菜单层级1
		if(keyId == KEY_SET_PRES) {                       // M 键切换项目 // 如果SET键被按下
			g_Dev.hmi_menu_id = (g_Dev.hmi_menu_id < (MENU_ITEM_TOTAL-1)) ? (g_Dev.hmi_menu_id+1) : 0; // 循环切换菜单项

			if(g_Dev.hmi_menu_id == M_ITEM_PRE_VECL) {    // 如果切换到预设声速项
				vel_bak = g_Thk.ultraSonic_Velocity; // 若是预设材料项，则存储当前声速 // 备份当前声速值
			}
			if(g_Dev.hmi_menu_id == M_ITEM_VELC) {        // 如果切换到声速设置项
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_LANG) {   // 如果切换到语言设置项
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_BRIGHT) { // 如果切换到亮度设置项
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_CALI){    // 如果切换到校准项
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_REFBIT){  // 如果切换到参考位项
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_DEV_FUNC){ // 如果切换到设备功能项
				if((DEV_FUNC_SUPPORT_BOLT == 0) && (DEV_FUNC_SUPPORT_LONG == 0)) { // 如果设备不支持螺栓和长距离功能
					g_Dev.hmi_menu_id = 0;                // 跳过该菜单项，回到第一项
				}
				ClearLED(0x00);                           // 清除OLED显示屏
			}
			else {                                        // 其他菜单项
				ClearLED(0x00);                           // 清除OLED显示屏
			}
		}
		else if(keyId == KEY_SET_LONG_PRES) {             // 如果SET键被长按
//			quitMenu();                                   // 退出菜单（已注释）
			g_Dev.hmi_level = 0;                          // 设置HMI层级为0（主界面）
			ClearLED(0x00);                               // 清除OLED显示屏
			BatteryHandler(1);// 强制刷新电量显示         // 强制刷新电池电量显示
			DataPersistence_SaveAllDatas(0);             // 保存所有数据到持久化存储

			if(g_Dev.functionMode == FUNC_THICKNESS_COMMON) // 如果功能模式为普通测厚
				Thk_DispMainUI();                         // 显示测厚主界面
			else if(g_Dev.functionMode == FUNC_THICKNESS_LONG) // 如果功能模式为长距离测厚
				Thk_DispMainUI();                         // 显示测厚主界面

			return 0;                                     // 返回0，退出菜单
		}

		if((g_Dev.hmi_menu_id == M_ITEM_CALI_SAMPLE) || (g_Dev.hmi_menu_id == M_ITEM_CALI)) { // 校准声速 // 如果当前菜单为校准样品或校准项
			if(g_Dev.hmi_menu_id == M_ITEM_CALI_SAMPLE) {//设置校准件的厚度,小数点后两位10.00mm // 如果是校准样品设置项
				Menu_CaliSample(keyId);                   // 调用校准样品菜单函数
			}
			else if(g_Dev.hmi_menu_id == M_ITEM_CALI) {   // 如果是校准执行项
				Menu_Cali(keyId);                         // 调用校准菜单函数
			}
			if(g_Dev.lang_type == 0) { //英文             // 如果语言设置为英文
				GUI_DispString(M_ITEM_L_X, M_LINE_1_Y, ALIGN_LEFT, "Depth"); // 显示"Depth"标签
				if(g_Dev.unit_type==UNIT_SI) snprintf(str, sizeof(str), "%0.2f mm", g_Thk.caliThicknessVal); // 国际制：格式化厚度值（毫米）
				else snprintf(str, sizeof(str), "%0.3f in.", UnitConvert_mm2inch(g_Thk.caliThicknessVal)); // 英制：格式化厚度值（英寸）
				GUI_DispMixText(M_ALIGN_R, 0, 2, "  ", hanzi, str, 0); // 显示厚度值
				GUI_DispString(M_ITEM_L_X, M_LINE_2_Y, ALIGN_LEFT, "Calib."); // 显示"Calib."标签
				GUI_DispMixText(M_ALIGN_R, M_LINE_2_Y, 2, "Press", hanzi, "     ", 0); // 显示"Press"提示
				OLED_ShowMediumChar(M_ALIGN_R-18,M_LINE_2_Y, 96+32); // up arrow // 显示UP箭头符号
			}
			else {                                        // 如果语言设置为中文
				hanzi[0] = 51; hanzi[1] = 52; hanzi[2] = 53; // "试样厚" // 设置汉字编码"试样厚"
				OLED_ShowCHN(M_ITEM_L_X, M_LINE_1_Y, hanzi, 3); // 显示"试样厚"标签
				if(g_Dev.unit_type==UNIT_SI)              // 如果单位为国际制
					snprintf(str, sizeof(str), "%0.2f mm", g_Thk.caliThicknessVal); //Display Bug // 格式化厚度值（毫米）
				else                                      // 如果单位为英制
					snprintf(str, sizeof(str), "%.3f in.", UnitConvert_mm2inch(g_Thk.caliThicknessVal)); // 格式化厚度值（英寸）
				GUI_DispMixText(M_ALIGN_R, 0, 2, "  ", hanzi, str, 0); // 显示厚度值
				hanzi[0] = 0; hanzi[1] = 1; // "校准"     // 设置汉字编码"校准"
				OLED_ShowCHN(M_ITEM_L_X, M_LINE_2_Y, hanzi, 2); // 显示"校准"标签
				hanzi[0] = 2;                             // 设置汉字编码"按"
				GUI_DispMixText(M_ALIGN_R, M_LINE_2_Y, 2, "\0", hanzi, "     ", 1); // 显示"按"提示
				OLED_ShowMediumChar(M_ALIGN_R-18,M_LINE_2_Y, 96+32); // up arrow // 显示UP箭头符号
			}
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_PRE_VECL) { // 预设声速选择 // 如果当前菜单为预设声速选择项
			Menu_MaterialVel(keyId);                      // 调用材料声速菜单函数
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_VELC) { // 声速 // 如果当前菜单为声速设置项
			Menu_VelSetting(keyId);                       // 调用声速设置菜单函数
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_UNIT) {	// Unit // 如果当前菜单为单位设置项
			Menu_Unit(keyId);                             // 调用单位设置菜单函数
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_LANG) { // language // 如果当前菜单为语言设置项
			Menu_Language(keyId);                         // 调用语言设置菜单函数
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_WIFI) {	//WIFI // 如果当前菜单为WiFi设置项
			Menu_Wifi(keyId);                             // 调用WiFi设置菜单函数
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_BRIGHT) { // Brightness // 如果当前菜单为亮度设置项
			Menu_OledBright(keyId);                       // 调用OLED亮度设置菜单函数
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_AVG) { // 平均次数设置 // 如果当前菜单为平均次数设置项
			Menu_Avg(keyId);                              // 调用平均次数设置菜单函数
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_REFBIT) { // 参考位开关 // 如果当前菜单为参考位设置项
			Menu_RefBit(keyId);                           // 调用参考位设置菜单函数
		}
		else if(g_Dev.hmi_menu_id == M_ITEM_DEV_FUNC) { // 功能选择 // 如果当前菜单为设备功能选择项
			Menu_Function(keyId);                         // 调用功能选择菜单函数
		}

		if(g_Dev.hmi_menu_id == M_ITEM_BRIGHT && g_Dev.brightness_val == 100) { // 如果当前在亮度设置项且亮度为100%
			if(UiFactory_EnterMainTaskDetection(keyId) != 0) { // 检测是否进入工厂模式
				ClearLED(0x00);                           // 清除OLED显示屏
				Menu_Wifi(KEY_NULL_PRES);                 // 刷新WiFi菜单显示
				Menu_OledBright(KEY_NULL_PRES);           // 刷新亮度菜单显示
			}
		}

		Draw_Progressbar(g_Dev.hmi_menu_id, MENU_ITEM_TOTAL); // 绘制进度条，显示当前菜单位置
		keyId = KEY_NULL_PRES;                            // 清除按键ID
	}
}



	