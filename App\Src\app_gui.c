#include "app_gui.h"           // 包含GUI应用头文件
#include <stdio.h>             // 包含标准输入输出函数库
#include <string.h>            // 包含字符串操作函数库
#include "app_menu.h"          // 包含应用菜单头文件
#include "oled.h"              // 包含OLED显示屏头文件
#include "key.h"               // 包含按键处理头文件
#include "app.h"               // 包含应用程序主头文件
#include "wifi.h"              // 包含WiFi功能头文件
#include "app_thickness.h"     // 包含厚度测量应用头文件
#include "delay.h"             // 包含延时功能头文件
#include "adc.h"               // 包含模数转换器头文件
#include "ui_draw_wave.h"      // 包含波形绘制界面头文件
#include "unit_utils.h"        // 包含单位转换工具头文件

THK_HMI_T g_ThkHmi = {.isDispWave = 0, .isDispWaveEnvelope=1}; // 全局测厚人机界面结构体，初始化波形显示为关闭，波形包络显示为开启

static uint8_t agcShowing = 0; // 静态变量，AGC显示状态标志，0=不显示，1/2=显示状态切换
void UI_ShowAGC(void) {        // AGC显示函数
	agcShowing = (agcShowing == 1) ? 2 : 1; // 在状态1和2之间切换，实现闪烁效果
	OLED_ShowString(82, 0, (agcShowing==1) ? "AGC." : "AGC "); // 在OLED坐标(82,0)显示"AGC."或"AGC "
}
void UI_ClearAGC(void) {       // 清除AGC显示函数
	if(!agcShowing) return;    // 如果AGC未显示则直接返回
	OLED_ShowString(82, 0, "    "); // 在OLED上清除AGC显示区域
	agcShowing = 0;            // 重置AGC显示状态为0
}


uint8_t OLED_ShowLargeNumber(uint32_t num) {           // 在OLED上显示大数字的函数，参数为要显示的数值（微米单位）
    unsigned char i = 0, j = 0;                       // 定义循环变量（未使用）
    uint8_t x = 0;                                    // 定义X坐标，初始为0
    uint8_t y = 0;                                    // 定义Y坐标，初始为0
    uint8_t x_step = 16;                              // 定义X坐标步长为16像素
    uint8_t n = 0;                                    // 定义数字提取变量
//    uint32_t thick;                                   // 厚度变量（已注释）
    if (g_Dev.hmi_level != 0)                         // 如果不在主界面层级
        return 1;                                     // 返回1，不显示
    if (num == 0) {                                   // 如果数值为0（无效测量）
        OLED_ShowLargerChar(x, y, '-');               // 显示第一个'-'字符
        x = x + x_step;                               // X坐标向右移动一个步长
        OLED_ShowLargerChar(x, y, '-');               // 显示第二个'-'字符
        x = x + x_step;                               // X坐标向右移动
        OLED_ShowLargerChar(x, y, '.');               // 显示小数点
        x = x + x_step;                               // X坐标向右移动
        OLED_ShowLargerChar(x, y, '-');               // 显示第三个'-'字符
        x = x + x_step;                               // X坐标向右移动
        OLED_ShowLargerChar(x, y, '-');               // 显示第四个'-'字符
        x = x + x_step;                               // X坐标向右移动
        OLED_Show16x32Char(x + 2, 25, ' ');          // 在小字体位置显示空格
    } else {                                          // 如果数值不为0（有效测量）
        if (g_Dev.unit_type == UNIT_SI) {    //国际制  // 如果使用国际单位制（毫米）
            if (g_Thk.referenceBitOn == 0) { // 保留小数点后两位, 第三位四舍五入 // 如果参考位关闭，进行四舍五入
                num = ((num % 10) >= 5) ? (num + 10) : num; // 对个位数进行四舍五入处理
            }

            if (num < 100000) {                       // 如果数值小于100.000mm（小于100毫米）
                if (num > 9999) {                     // 如果数值大于9.999mm
                    n = (num % 100000) / 10000;       // 提取十位数字
                    OLED_ShowLargerChar(x, y, n + '0'); // 显示十位数字
                    x = x + x_step;                   // X坐标向右移动
                } else {                              // 如果数值小于等于9.999mm
                    n = (num % 100000) / 10000;       // 提取十位数字（为0）
                    OLED_ShowLargerChar(x, y, ' ');   // 显示空格（不显示前导零）
                    x = x + x_step;                   // X坐标向右移动
                }

                n = (num % 10000) / 1000;             // 提取个位数字
                OLED_ShowLargerChar(x, y, n + '0');   // 显示个位数字
                x = x + x_step;                       // X坐标向右移动
                OLED_ShowLargerChar(x, y, '.');       // 显示小数点
                x = x + x_step;                       // X坐标向右移动
                n = (num % 1000) / 100;               // 提取小数点后第一位
                OLED_ShowLargerChar(x, y, n + '0');   // 显示小数点后第一位
                x = x + x_step;                       // X坐标向右移动
                n = (num % 100) / 10;                 // 提取小数点后第二位
                OLED_ShowLargerChar(x, y, n + '0');   // 显示小数点后第二位
                x = x + x_step;                       // X坐标向右移动
                n = (num % 10) / 1;                   // 提取小数点后第三位
                if (g_Thk.referenceBitOn == 1) {     // 如果参考位开启
                    OLED_Show16x32Char(x + 2, 25, n + '0'); // 显示小数点后第三位
                } else {                              // 如果参考位关闭
                    OLED_Show16x32Char(x + 2, 25, ' '); // 显示空格
                }
            } else {                                  // 如果数值大于等于100.000mm
                n = (num % 1000000) / 100000;         // 提取百位数字
                OLED_ShowLargerChar(x, y, n + '0');   // 显示百位数字
                x = x + x_step;                       // X坐标向右移动
                n = (num % 100000) / 10000;           // 提取十位数字
                OLED_ShowLargerChar(x, y, n + '0');   // 显示十位数字
                x = x + x_step;                       // X坐标向右移动
                n = (num % 10000) / 1000;             // 提取个位数字
                OLED_ShowLargerChar(x, y, n + '0');   // 显示个位数字
                x = x + x_step;                       // X坐标向右移动
                OLED_ShowLargerChar(x, y, '.');       // 显示小数点
                x = x + x_step;                       // X坐标向右移动
                n = (num % 1000) / 100;               // 提取小数点后第一位
                OLED_ShowLargerChar(x, y, n + '0');   // 显示小数点后第一位
                x = x + x_step;                       // X坐标向右移动
                n = (num % 100) / 10;                 // 提取小数点后第二位
                OLED_Show16x32Char(x + 2, 25, n + '0'); // 显示小数点后第二位
            }
        } else { //英制单位                           // 如果使用英制单位（英寸）
			// num = 10*1000;                        // 测试数值（已注释）
            if (g_Dev.unit_type == UNIT_BS) { // 如果是英制，则换算 // 如果单位类型为英制
                double thkInch = UnitConvert_mm2inch(num / 1000.0); // 将微米转换为毫米再转换为英寸
                num = thkInch * 10000;                // 将英寸值乘以10000得到显示用的整数
                if ((g_Thk.referenceBitOn == 0) && (num % 10 >= 5)) // 如果参考位关闭且需要四舍五入
                    num = num + 10;                   // 进行四舍五入
            }
            n = (num / 100000);                       // 提取最高位数字
            if (n != 0) {                             // 如果最高位不为0
                OLED_ShowLargerChar(x, y, n + '0');   // 显示最高位数字
                x = x + x_step;                       // X坐标向右移动
            }
            n = (num % 100000) / 10000;               // 提取次高位数字
            OLED_ShowLargerChar(x, y, n + '0');       // 显示次高位数字
            x = x + x_step;                           // X坐标向右移动
            OLED_ShowLargerChar(x, y, '.');           // 显示小数点
            x = x + x_step;                           // X坐标向右移动
            n = (num % 10000) / 1000;                 // 提取小数点后第一位
            OLED_ShowLargerChar(x, y, n + '0');       // 显示小数点后第一位
            x = x + x_step;                           // X坐标向右移动
            n = (num % 1000) / 100;                   // 提取小数点后第二位
            OLED_ShowLargerChar(x, y, n + '0');       // 显示小数点后第二位
            x = x + x_step;                           // X坐标向右移动
            n = (num % 100) / 10;                     // 提取小数点后第三位
            OLED_ShowLargerChar(x, y, n + '0');       // 显示小数点后第三位
            x = x + x_step;                           // X坐标向右移动
            n = (num % 10) / 1;                       // 提取小数点后第四位
            if (g_Thk.referenceBitOn == 1)           // 如果参考位开启
                OLED_Show16x32Char(x + 2, 25, n + '0'); // 显示小数点后第四位
            else                                      // 如果参考位关闭
                OLED_Show16x32Char(x + 2, 25, ' ');  // 显示空格
        }
    }
    return 0;                                         // 返回0表示成功
}

void Oled_ShowWifiName(char *str) {                    // 显示WiFi名称的函数，参数为要显示的字符串
    uint8_t x = 20;                                   // 定义起始X坐标为20
    uint8_t y = 20;                                   // 定义起始Y坐标为20
    uint8_t i = 0;                                    // 定义循环计数器
    ClearLED(0x00);                                   // 清除OLED显示屏
    for (i = 0; i < 15; i++) {                        // 循环最多显示15个字符
        if (str[i] == '\0')                           // 如果遇到字符串结束符
            break;                                    // 跳出循环
        OLED_ShowMediumChar(x, y, str[i]);            // 在指定位置显示中等大小的字符
        x = x + 7;                                    // X坐标向右移动7像素
    }

    GUI_DrawWifi(getWifiState());                     // 绘制WiFi状态图标

    delay_ms(1000);                                   // 延时1000毫秒
    delay_ms(600);                                    // 延时600毫秒（总共1.6秒）

	Thk_DispMainUI();                                 // 显示测厚主界面
    BatteryHandler(1);                                // 处理电池显示，参数1表示强制更新
}


//void Oled_ShowMainScreen(uint8_t emit, uint8_t wState) { // 显示主屏幕函数（已注释）
//    ClearLED(0x00);                                 // 清除OLED显示屏（已注释）
//    Oled_ShowUnit(g_Dev.unit_type);                 // 显示单位类型（已注释）
//	OLED_ShowLargeNumber(0);                         // 显示大数字0（已注释）
//    GUI_DrawWifi(wState);                           // 绘制WiFi状态（已注释）
//    BatteryHandler(0);                              // 处理电池显示（已注释）
//    GUI_DrawSnow(emit);                             // 绘制发射状态图标（已注释）
//	                                                 // 空行（已注释）
//}                                                   // 函数结束（已注释）

//void GuiThk_DispMainUiForMeasureing(void) {        // 测量时显示主界面函数（已注释）
//	if(g_ThkHmi.isDispWave) {                        // 如果显示波形模式（已注释）
//		if(g_Thk.resValid)                           // 如果测量结果有效（已注释）
//			UiDrawWave_DisplayWaveAndThk(g_Thk.wave.buf, g_Thk.wave.buflen, g_Thk.thkResValUm); // 显示波形和厚度（已注释）
//		else                                         // 否则（已注释）
//			UiDrawWave_DisplayInvalidWaveAndThk();   // 显示无效波形和厚度（已注释）
//		                                             // 空行（已注释）
//		Oled_ShowUnit(g_Dev.unit_type);              // 显示单位类型（已注释）
//		GUI_DrawWifi(g_Dev.wifiState);               // 绘制WiFi状态（已注释）
//		BatteryHandler(1);                           // 处理电池显示（已注释）
//		GUI_DrawSnow(g_Emat.Emit_Switch);            // 绘制发射状态图标（已注释）
//	}                                                // 条件结束（已注释）
//	else {                                           // 否则（已注释）
//		Oled_ShowLongFlag(g_Thk.longMode);           // 显示长距离模式标志（已注释）
//		OLED_ShowLargeNumber(g_Thk.resValid ? g_Thk.thkResValUm : 0); // 显示大数字（已注释）
//	}                                                // 条件结束（已注释）
//}                                                   // 函数结束（已注释）

/**
  * @brief  主屏显示                                 // 函数功能：显示测厚仪主界面
  * @param  None                                      // 参数：无
  * @retval None                                      // 返回值：无
  */
void Thk_DispMainUI(void) {                          // 测厚主界面显示函数
//	if(animation) {                                  // 如果需要动画效果（已注释）
//		ClearLED(0x00);                              // 清除OLED显示屏（已注释）
//		Oled_DispInLine1("Thickness", TA_LEFT);     // 在第一行显示"Thickness"（已注释）
//		Oled_DispInLine2(" .", TA_LEFT); delay_ms(500); // 显示动画点1并延时（已注释）
//		Oled_DispInLine2("  .", TA_LEFT); delay_ms(500); // 显示动画点2并延时（已注释）
//		Oled_DispInLine2("   .", TA_LEFT); delay_ms(500); // 显示动画点3并延时（已注释）
//	}                                                // 动画条件结束（已注释）

	ClearLED(0x00);                                  // 清除OLED显示屏

	if(g_ThkHmi.isDispWave) {                        // 如果当前处于波形显示模式
		if(g_Thk.resValid)                           // 如果测量结果有效
			UiDrawWave_DisplayWaveAndThk(g_Thk.wave.buf, g_Thk.wave.buflen, g_Thk.thkResValUm, (g_Dev.unit_type == UNIT_BS), g_Thk.referenceBitOn, g_ThkHmi.isDispWaveEnvelope); // 显示波形和厚度值，参数包括：波形缓冲区、缓冲区长度、厚度值、是否英制单位、是否显示参考位、是否显示包络
		else                                         // 如果测量结果无效
			UiDrawWave_DisplayInvalidWaveAndThk();   // 显示无效波形和厚度提示
	}
	else {                                           // 如果当前处于数字显示模式
		Oled_ShowLongFlag(g_Thk.longMode);           // 显示长距离模式标志
		OLED_ShowLargeNumber(g_Thk.resValid ? g_Thk.thkResValUm : 0); // 显示大数字：如果结果有效显示厚度值，否则显示0
	}

	Oled_ShowUnit(g_Dev.unit_type);                  // 显示单位类型（mm或inch）
	GUI_DrawWifi(g_Dev.wifiState);                   // 绘制WiFi状态图标
	BatteryHandler(1);                               // 处理电池电量显示，参数1表示强制更新
	GUI_DrawSnow(g_Emat.Emit_Switch);                // 绘制发射状态图标（雪花图标）
}


uint32_t CommonVelcocity_Show(uint8_t lang, uint8_t id, uint32_t velcity_old) { // 显示常用声速的函数，参数：语言类型、材料ID、当前声速值
    char hzbuf[10];                                   // 定义汉字缓冲区，存储汉字编码
    char str[20];                                     // 定义字符串缓冲区
    char str1[20] = "Now,";                           // 定义字符串，初始值为"Now,"
    uint32_t velcity = 0;                             // 定义声速变量，初始化为0
    uint8_t alignRight = 124;                         // 定义右对齐位置为124像素
    if (lang == 1) {
        switch (id) { // Chinese
        case 0:
            hzbuf[0] = 30;
            hzbuf[1] = 31;
            hzbuf[2] = 73;
            hzbuf[3] = 74; //"当前材料"
            sprintf(str, "%d", velcity_old);
            str1[0] = '@';
            str1[1] = 0;
            strcat(str1, str);
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 4);
            velcity = velcity_old;
            break;
        case 1:
            hzbuf[0] = 57;
            hzbuf[1] = 41;
            hzbuf[2] = 35; // "低碳钢"
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 3);
            velcity = 3230;
            break;
        case 2:
            hzbuf[0] = 33;
            hzbuf[1] = 34;
            hzbuf[2] = 35; // "304不锈钢"
            GUI_DispMixText(alignRight, 0, 2, "304", hzbuf, "\0", 3);
            velcity = 3145;
            break;
        case 3:
            hzbuf[0] = 42;
            hzbuf[1] = 32; // “铸铁”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
            velcity = 2400;
            break;
        case 4:
            hzbuf[0] = 36;
            hzbuf[1] = 39;
            hzbuf[2] = 40; // "铝合金"
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 3);
            velcity = 3100;
            break;
        case 5:
            hzbuf[0] = 72;
            hzbuf[1] = 37; // “紫铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
            velcity = 2260;
            break;
        case 6:
            hzbuf[0] = 50;
            hzbuf[1] = 37; // “黄铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
            velcity = 2120;
            break;
        case 7:
            hzbuf[0] = 71;
            hzbuf[1] = 37; // “青铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
            velcity = 2230;
            break;
        case 8:
            hzbuf[0] = 45;
            hzbuf[1] = 46; // “纯钛”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
            velcity = 3120;
            break;
        case 9:
            hzbuf[0] = 61;
            hzbuf[1] = 62; // “参数1”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "1", 2);
            velcity = g_Thk.usrSaveVelocity_1;
            break;
        case 10:
            hzbuf[0] = 61;
            hzbuf[1] = 62; // “参数2”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "2", 2);
            velcity = g_Thk.usrSaveVelocity_2;
            break;
        case 11:
            hzbuf[0] = 61;
            hzbuf[1] = 62; // “参数3”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "3", 2);
            velcity = g_Thk.usrSaveVelocity_3;
            break;
        default:
            break;
        }
    } else if (lang == 0) { // English
        switch (id) {
        case 0:
            hzbuf[0] = 30;
            hzbuf[1] = 31; //"当前"
            // sprintf(str, "Now@%d", velcity_old);
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Curr. Mat.", 0);
            velcity = velcity_old;
            break;
        case 1: // "低碳钢"
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Mild Steel", 0);
            velcity = 3230;
            break;
        case 2: // "304不锈钢"
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "304 SS", 0);
            velcity = 3145;
            break;
        case 3: // “铸铁”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Cast Iron", 0);
            velcity = 2400;
            break;
        case 4: // "铝合金"
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Aluminum", 0);
            velcity = 3100;
            break;
        case 5: // “紫铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Copper", 0);
            velcity = 2260;
            break;
        case 6: // “黄铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Brass", 0);
            velcity = 2120;
            break;
        case 7: // “青铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Bronze", 0);
            velcity = 2230;
            break;
        case 8: // “纯钛”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Titanium", 0);
            velcity = 3120;
            break;
        case 9: // “参数1”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "User Def.1", 0);
            velcity = g_Thk.usrSaveVelocity_1;
            break;
        case 10: // “参数2”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "User Def.2", 0);
            velcity = g_Thk.usrSaveVelocity_2;
            break;
        case 11: // “参数3”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "User Def.3", 0);
            velcity = g_Thk.usrSaveVelocity_3;
            break;
        default:
            break;
        }
    }
    return velcity;                                   // 返回选中材料的声速值
}
// 人机交互处理                                        // 注释：人机交互处理模块
// Human-Machine Interaction                          // 英文注释：人机交互
uint8_t HMI_Manage(void)                             // 人机界面管理函数
{
		uint8_t keyId = 0;                            // 定义按键ID变量，初始化为0
		keyId = KEY_Scan(0);                          // 扫描按键状态
		if(keyId == 0) return 0;                      // 如果没有按键按下，返回0
		if(g_Dev.controlByApp) {                      // 如果设备被APP控制
			if(keyId == KEY_DOWN_PRES) {              // 如果DOWN键被按下
				Oled_ShowWifiName(g_Dev.deviceSN);    // 显示WiFi名称（设备序列号）
			}
			else if(keyId == KEY_DOWN_LONG_PRES) {    // 如果DOWN键被长按
				if((g_Dev.wifiState == 1) || (g_Dev.wifiState == 2)){ // 如果WiFi处于连接状态
					CloseWifi();                      // 关闭WiFi
					GUI_DrawWifi(0x00);               // 更新WiFi图标为关闭状态
					g_Dev.controlByApp = 0;           // 将控制权交还给设备本身
				}
				else {                                // 如果WiFi处于关闭状态
					OpenWifi();                       // 打开WiFi
					GUI_DrawWifi(0x01);               // 更新WiFi图标为开启状态
				}
			}
			keyId = KEY_NULL_PRES;                    // 清除按键ID
			return 1;                                 // 返回1表示已处理按键
		}
		if(g_Dev.hmi_level == GUI_LEVEL_MAIN) { // Level 0 -- 主界面 // 如果当前处于主界面层级
			if(keyId == KEY_SET_PRES) {               // 如果SET键被按下
				g_Emat.Emit_Switch = !g_Emat.Emit_Switch; // 切换EMAT发射开关状态
				g_Dev.pcCmd_buf[3] = g_Emat.Emit_Switch; // 更新PC命令缓冲区中的发射状态
				GUI_DrawSnow(g_Emat.Emit_Switch);     // 更新发射状态图标
			}
			else if(keyId == KEY_SET_LONG_PRES) {     // 如果SET键被长按
				g_Dev.hmi_level = GUI_LEVEL_MENU; // 长按M键，切换到菜单 // 切换到菜单界面层级
				g_Emat.Emit_Switch = 0; // 关闭发射    // 关闭EMAT发射
				GUI_DrawSnow(g_Emat.Emit_Switch);     // 更新发射状态图标
				ClearLED(0x00);                       // 清除OLED显示屏
			}
			else if(keyId == KEY_UP_PRES) {           // 如果UP键被按下
				g_ThkHmi.isDispWave = !g_ThkHmi.isDispWave; // 切换波形显示模式
				g_ThkHmi.isDispWaveEnvelope = 1;      // 启用波形包络显示

				Thk_DispMainUI();                     // 刷新主界面显示
			}
			else if(keyId == KEY_DOWN_PRES) {         // 如果DOWN键被按下
				if(g_ThkHmi.isDispWave) {             // 如果当前处于波形显示模式
					g_ThkHmi.isDispWaveEnvelope = !g_ThkHmi.isDispWaveEnvelope; // 切换波形包络显示状态
					Thk_DispMainUI();                 // 刷新主界面显示
				}
				else                                  // 如果当前处于数字显示模式
					Oled_ShowWifiName(g_Dev.deviceSN); // 显示WiFi名称（设备序列号）
			}
			else if(keyId == KEY_DOWN_LONG_PRES) {    // 如果DOWN键被长按
				if(g_Dev.wifiState != 0) {            // 如果WiFi处于开启状态
					CloseWifi();                      // 关闭WiFi
					GUI_DrawWifi(0x00);               // 更新WiFi图标为关闭状态
				}
				else {                                // 如果WiFi处于关闭状态
					OpenWifi();                       // 打开WiFi
					GUI_DrawWifi(0x01);               // 更新WiFi图标为开启状态
				}
			}
			keyId = KEY_NULL_PRES;                    // 清除按键ID
		}
		if(g_Dev.hmi_level == GUI_LEVEL_MENU) {      // 如果当前处于菜单界面层级
			App_Menu_MainTask();                      // 执行菜单主任务
			g_Dev.hmi_level = GUI_LEVEL_MAIN;         // 菜单退出后返回主界面层级
		}
}


