#include "app_gui.h"
#include <stdio.h>
#include <string.h>
#include "app_menu.h"
#include "oled.h"
#include "key.h"
#include "app.h"
#include "wifi.h"
#include "app_thickness.h"
#include "delay.h"
#include "adc.h"
#include "ui_draw_wave.h"
#include "unit_utils.h"

THK_HMI_T g_ThkHmi = {.isDispWave = 0, .isDispWaveEnvelope=1};

static uint8_t agcShowing = 0;
void UI_ShowAGC(void) {
	agcShowing = (agcShowing == 1) ? 2 : 1;
	OLED_ShowString(82, 0, (agcShowing==1) ? "AGC." : "AGC ");
}
void UI_ClearAGC(void) {
	if(!agcShowing) return;
	OLED_ShowString(82, 0, "    ");
	agcShowing = 0;
}


uint8_t OLED_ShowLargeNumber(uint32_t num) {
    unsigned char i = 0, j = 0;
    uint8_t x = 0;
    uint8_t y = 0;
    uint8_t x_step = 16;
    uint8_t n = 0;
//    uint32_t thick;
    if (g_Dev.hmi_level != 0)
        return 1;
    if (num == 0) {
        OLED_ShowLargerChar(x, y, '-');
        x = x + x_step;
        OLED_ShowLargerChar(x, y, '-');
        x = x + x_step;
        OLED_ShowLargerChar(x, y, '.');
        x = x + x_step;
        OLED_ShowLargerChar(x, y, '-');
        x = x + x_step;
        OLED_ShowLargerChar(x, y, '-');
        x = x + x_step;
        OLED_Show16x32Char(x + 2, 25, ' ');
    } else {
        if (g_Dev.unit_type == UNIT_SI) {    //国际制
            if (g_Thk.referenceBitOn == 0) { // 保留小数点后两位, 第三位四舍五入
                num = ((num % 10) >= 5) ? (num + 10) : num;
            }

            if (num < 100000) {
                if (num > 9999) {
                    n = (num % 100000) / 10000;
                    OLED_ShowLargerChar(x, y, n + '0');
                    x = x + x_step;
                } else {
                    n = (num % 100000) / 10000;
                    OLED_ShowLargerChar(x, y, ' ');
                    x = x + x_step;
                }

                n = (num % 10000) / 1000;
                OLED_ShowLargerChar(x, y, n + '0');
                x = x + x_step;
                OLED_ShowLargerChar(x, y, '.');
                x = x + x_step;
                n = (num % 1000) / 100;
                OLED_ShowLargerChar(x, y, n + '0');
                x = x + x_step;
                n = (num % 100) / 10;
                OLED_ShowLargerChar(x, y, n + '0');
                x = x + x_step;
                n = (num % 10) / 1;
                if (g_Thk.referenceBitOn == 1) {
                    OLED_Show16x32Char(x + 2, 25, n + '0');
                } else {
                    OLED_Show16x32Char(x + 2, 25, ' ');
                }
            } else {
                n = (num % 1000000) / 100000;
                OLED_ShowLargerChar(x, y, n + '0');
                x = x + x_step;
                n = (num % 100000) / 10000;
                OLED_ShowLargerChar(x, y, n + '0');
                x = x + x_step;
                n = (num % 10000) / 1000;
                OLED_ShowLargerChar(x, y, n + '0');
                x = x + x_step;
                OLED_ShowLargerChar(x, y, '.');
                x = x + x_step;
                n = (num % 1000) / 100;
                OLED_ShowLargerChar(x, y, n + '0');
                x = x + x_step;
                n = (num % 100) / 10;
                OLED_Show16x32Char(x + 2, 25, n + '0');
            }
        } else { //英制单位
			// num = 10*1000;
            if (g_Dev.unit_type == UNIT_BS) { // 如果是英制，则换算
                double thkInch = UnitConvert_mm2inch(num / 1000.0);
                num = thkInch * 10000;
                if ((g_Thk.referenceBitOn == 0) && (num % 10 >= 5))
                    num = num + 10;
            }
            n = (num / 100000);
            if (n != 0) {
                OLED_ShowLargerChar(x, y, n + '0');
                x = x + x_step;
            }
            n = (num % 100000) / 10000;
            OLED_ShowLargerChar(x, y, n + '0');
            x = x + x_step;
            OLED_ShowLargerChar(x, y, '.');
            x = x + x_step;
            n = (num % 10000) / 1000;
            OLED_ShowLargerChar(x, y, n + '0');
            x = x + x_step;
            n = (num % 1000) / 100;
            OLED_ShowLargerChar(x, y, n + '0');
            x = x + x_step;
            n = (num % 100) / 10;
            OLED_ShowLargerChar(x, y, n + '0');
            x = x + x_step;
            n = (num % 10) / 1;
            if (g_Thk.referenceBitOn == 1)
                OLED_Show16x32Char(x + 2, 25, n + '0');
            else
                OLED_Show16x32Char(x + 2, 25, ' ');
        }
    }
    return 0;
}

void Oled_ShowWifiName(char *str) {
    uint8_t x = 20;
    uint8_t y = 20;
    uint8_t i = 0;
    ClearLED(0x00);
    for (i = 0; i < 15; i++) {
        if (str[i] == '\0')
            break;
        OLED_ShowMediumChar(x, y, str[i]);
        x = x + 7;
    }

    GUI_DrawWifi(getWifiState());

    delay_ms(1000);
    delay_ms(600);
	
	Thk_DispMainUI();
    BatteryHandler(1);
}


//void Oled_ShowMainScreen(uint8_t emit, uint8_t wState) {
//    ClearLED(0x00);
//    Oled_ShowUnit(g_Dev.unit_type);
//	OLED_ShowLargeNumber(0);
//    GUI_DrawWifi(wState);
//    BatteryHandler(0);
//    GUI_DrawSnow(emit);
//	
//}

//void GuiThk_DispMainUiForMeasureing(void) {
//	if(g_ThkHmi.isDispWave) {
//		if(g_Thk.resValid)
//			UiDrawWave_DisplayWaveAndThk(g_Thk.wave.buf, g_Thk.wave.buflen, g_Thk.thkResValUm);
//		else
//			UiDrawWave_DisplayInvalidWaveAndThk();
//		
//		Oled_ShowUnit(g_Dev.unit_type);
//		GUI_DrawWifi(g_Dev.wifiState);
//		BatteryHandler(1);
//		GUI_DrawSnow(g_Emat.Emit_Switch);
//	}
//	else {
//		Oled_ShowLongFlag(g_Thk.longMode);
//		OLED_ShowLargeNumber(g_Thk.resValid ? g_Thk.thkResValUm : 0);
//	}
//}

/**
  * @brief  主屏显示
  * @param  None
  * @retval None
  */
void Thk_DispMainUI(void) {
//	if(animation) {
//		ClearLED(0x00);
//		Oled_DispInLine1("Thickness", TA_LEFT);
//		Oled_DispInLine2(" .", TA_LEFT); delay_ms(500);
//		Oled_DispInLine2("  .", TA_LEFT); delay_ms(500);
//		Oled_DispInLine2("   .", TA_LEFT); delay_ms(500);
//	}
	
	ClearLED(0x00);

	if(g_ThkHmi.isDispWave) {
		if(g_Thk.resValid)
			UiDrawWave_DisplayWaveAndThk(g_Thk.wave.buf, g_Thk.wave.buflen, g_Thk.thkResValUm, (g_Dev.unit_type == UNIT_BS), g_Thk.referenceBitOn, g_ThkHmi.isDispWaveEnvelope);
		else
			UiDrawWave_DisplayInvalidWaveAndThk();
	}
	else {
		Oled_ShowLongFlag(g_Thk.longMode);
		OLED_ShowLargeNumber(g_Thk.resValid ? g_Thk.thkResValUm : 0);
	}
	
	Oled_ShowUnit(g_Dev.unit_type);
	GUI_DrawWifi(g_Dev.wifiState);
	BatteryHandler(1);
	GUI_DrawSnow(g_Emat.Emit_Switch);
}


uint32_t CommonVelcocity_Show(uint8_t lang, uint8_t id, uint32_t velcity_old) {
    char hzbuf[10];
    char str[20];
    char str1[20] = "Now,";
    uint32_t velcity = 0;
    uint8_t alignRight = 124;
    if (lang == 1) {
        switch (id) { // Chinese
        case 0:
            hzbuf[0] = 30;
            hzbuf[1] = 31;
            hzbuf[2] = 73;
            hzbuf[3] = 74; //"当前材料"
            sprintf(str, "%d", velcity_old);
            str1[0] = '@';
            str1[1] = 0;
            strcat(str1, str);
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 4);
            velcity = velcity_old;
            break;
        case 1:
            hzbuf[0] = 57;
            hzbuf[1] = 41;
            hzbuf[2] = 35; // "低碳钢"
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 3);
            velcity = 3230;
            break;
        case 2:
            hzbuf[0] = 33;
            hzbuf[1] = 34;
            hzbuf[2] = 35; // "304不锈钢"
            GUI_DispMixText(alignRight, 0, 2, "304", hzbuf, "\0", 3);
            velcity = 3145;
            break;
        case 3:
            hzbuf[0] = 42;
            hzbuf[1] = 32; // “铸铁”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
            velcity = 2400;
            break;
        case 4:
            hzbuf[0] = 36;
            hzbuf[1] = 39;
            hzbuf[2] = 40; // "铝合金"
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 3);
            velcity = 3100;
            break;
        case 5:
            hzbuf[0] = 72;
            hzbuf[1] = 37; // “紫铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
            velcity = 2260;
            break;
        case 6:
            hzbuf[0] = 50;
            hzbuf[1] = 37; // “黄铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
            velcity = 2120;
            break;
        case 7:
            hzbuf[0] = 71;
            hzbuf[1] = 37; // “青铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
            velcity = 2230;
            break;
        case 8:
            hzbuf[0] = 45;
            hzbuf[1] = 46; // “纯钛”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
            velcity = 3120;
            break;
        case 9:
            hzbuf[0] = 61;
            hzbuf[1] = 62; // “参数1”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "1", 2);
            velcity = g_Thk.usrSaveVelocity_1;
            break;
        case 10:
            hzbuf[0] = 61;
            hzbuf[1] = 62; // “参数2”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "2", 2);
            velcity = g_Thk.usrSaveVelocity_2;
            break;
        case 11:
            hzbuf[0] = 61;
            hzbuf[1] = 62; // “参数3”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "3", 2);
            velcity = g_Thk.usrSaveVelocity_3;
            break;
        default:
            break;
        }
    } else if (lang == 0) { // English
        switch (id) {
        case 0:
            hzbuf[0] = 30;
            hzbuf[1] = 31; //"当前"
            // sprintf(str, "Now@%d", velcity_old);
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Curr. Mat.", 0);
            velcity = velcity_old;
            break;
        case 1: // "低碳钢"
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Mild Steel", 0);
            velcity = 3230;
            break;
        case 2: // "304不锈钢"
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "304 SS", 0);
            velcity = 3145;
            break;
        case 3: // “铸铁”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Cast Iron", 0);
            velcity = 2400;
            break;
        case 4: // "铝合金"
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Aluminum", 0);
            velcity = 3100;
            break;
        case 5: // “紫铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Copper", 0);
            velcity = 2260;
            break;
        case 6: // “黄铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Brass", 0);
            velcity = 2120;
            break;
        case 7: // “青铜”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Bronze", 0);
            velcity = 2230;
            break;
        case 8: // “纯钛”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Titanium", 0);
            velcity = 3120;
            break;
        case 9: // “参数1”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "User Def.1", 0);
            velcity = g_Thk.usrSaveVelocity_1;
            break;
        case 10: // “参数2”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "User Def.2", 0);
            velcity = g_Thk.usrSaveVelocity_2;
            break;
        case 11: // “参数3”
            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "User Def.3", 0);
            velcity = g_Thk.usrSaveVelocity_3;
            break;
        default:
            break;
        }
    }
    return velcity;
}
// 人机交互处理
// Human-Machine Interaction
uint8_t HMI_Manage(void)
{
		uint8_t keyId = 0;
		keyId = KEY_Scan(0);
		if(keyId == 0) return 0;
		if(g_Dev.controlByApp) {
			if(keyId == KEY_DOWN_PRES) {
				Oled_ShowWifiName(g_Dev.deviceSN);
			}
			else if(keyId == KEY_DOWN_LONG_PRES) {
				if((g_Dev.wifiState == 1) || (g_Dev.wifiState == 2)){ 
					CloseWifi();
					GUI_DrawWifi(0x00);
					g_Dev.controlByApp = 0;
				}
				else {
					OpenWifi();
					GUI_DrawWifi(0x01);
				}
			}
			keyId = KEY_NULL_PRES;
			return 1;
		}
		if(g_Dev.hmi_level == GUI_LEVEL_MAIN) { // Level 0 -- 主界面
			if(keyId == KEY_SET_PRES) {
				g_Emat.Emit_Switch = !g_Emat.Emit_Switch;
				g_Dev.pcCmd_buf[3] = g_Emat.Emit_Switch;
				GUI_DrawSnow(g_Emat.Emit_Switch);
			}
			else if(keyId == KEY_SET_LONG_PRES) {
				g_Dev.hmi_level = GUI_LEVEL_MENU; // 长按M键，切换到菜单
				g_Emat.Emit_Switch = 0; // 关闭发射
				GUI_DrawSnow(g_Emat.Emit_Switch);
				ClearLED(0x00);
			}
			else if(keyId == KEY_UP_PRES) {
				g_ThkHmi.isDispWave = !g_ThkHmi.isDispWave;
				g_ThkHmi.isDispWaveEnvelope = 1;
				
				Thk_DispMainUI();
			}
			else if(keyId == KEY_DOWN_PRES) {
				if(g_ThkHmi.isDispWave) {
					g_ThkHmi.isDispWaveEnvelope = !g_ThkHmi.isDispWaveEnvelope;
					Thk_DispMainUI();
				}
				else
					Oled_ShowWifiName(g_Dev.deviceSN);
			}
			else if(keyId == KEY_DOWN_LONG_PRES) {
				if(g_Dev.wifiState != 0) { 
					CloseWifi();
					GUI_DrawWifi(0x00);
				}
				else {
					OpenWifi();
					GUI_DrawWifi(0x01);
				}
			}
			keyId = KEY_NULL_PRES;
		}
		if(g_Dev.hmi_level == GUI_LEVEL_MENU) {
			App_Menu_MainTask();
			g_Dev.hmi_level = GUI_LEVEL_MAIN;
		}
}


	