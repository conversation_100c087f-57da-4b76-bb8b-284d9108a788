
#include "oled.h"
#include "stm32f7xx_hal.h"
#include <string.h>
#include <stdio.h>
#include "bmp.h"
#include "adc.h"
#include "delay.h"
#include "oledfont.h"
#include "app_gui.h"

/**
  * CS --- PD11
  * DC/RS --- PD12
  * RES --- PD13
  * CLK --- PB13
  * MISO/SDIN --- PB14
  */

//#define OLED_SCLK_Clr() HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET)
//#define OLED_SCLK_Set() HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_SET)

//#define OLED_SDIN_Clr() HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14, GPIO_PIN_RESET)
//#define OLED_SDIN_Set() HAL_GPIO_WritePin(GPIOB, GPIO_PIN_14, GPIO_PIN_SET)

//#define OLED_RST_Clr() HAL_GPIO_WritePin(GPIOD, GPIO_PIN_13, GPIO_PIN_RESET)
//#define OLED_RST_Set() HAL_GPIO_WritePin(GPIOD, GPIO_PIN_13, GPIO_PIN_SET)

//#define OLED_RS_Clr() HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12, GPIO_PIN_RESET)
//#define OLED_RS_Set() HAL_GPIO_WritePin(GPIOD, GPIO_PIN_12, GPIO_PIN_SET)

//#define OLED_CS_Clr() HAL_GPIO_WritePin(GPIOD, GPIO_PIN_11, GPIO_PIN_RESET)
//#define OLED_CS_Set() HAL_GPIO_WritePin(GPIOD, GPIO_PIN_11, GPIO_PIN_SET)

#define OLED_SCLK_Clr() GPIOB->BSRR = (uint32_t)GPIO_PIN_13 << 16
#define OLED_SCLK_Set() GPIOB->BSRR = GPIO_PIN_13

#define OLED_SDIN_Clr() GPIOB->BSRR = (uint32_t)GPIO_PIN_14 << 16
#define OLED_SDIN_Set() GPIOB->BSRR = GPIO_PIN_14

#define OLED_RST_Clr() GPIOD->BSRR = (uint32_t)GPIO_PIN_13 << 16
#define OLED_RST_Set() GPIOD->BSRR = GPIO_PIN_13

#define OLED_RS_Clr() GPIOD->BSRR = (uint32_t)GPIO_PIN_12 << 16
#define OLED_RS_Set() GPIOD->BSRR = GPIO_PIN_12

#define OLED_CS_Clr() GPIOD->BSRR = (uint32_t)GPIO_PIN_11 << 16
#define OLED_CS_Set() GPIOD->BSRR = GPIO_PIN_11

#define OLED_READ_MISO (GPIOB->IDR & GPIO_PIN_15)

#define TA_LEFT 0
#define TA_CENTER 1
#define TA_RIGHT 2

static void Delay(unsigned int Data) {
    unsigned char temp;
    for (; Data > 0; Data--) {
        for (temp = 0; temp < 0xff; temp++) {
		}
    }
}

//static uint8_t Spi_ReadStatus(void) {
//	uint8_t data = 0;
//	
//	OLED_RS_Clr();
//    OLED_CS_Clr();

//	for (int32_t i = 0; i < 8; i++) {
//        OLED_SCLK_Clr();
//		data = data << 1;
//		data |= OLED_READ_MISO ? 0x01 : 0;
//        OLED_SCLK_Set();
//    }
//    OLED_RS_Clr();
//    OLED_CS_Set();
//	
//	return data;
//}

//uint8_t tstStatus;

/**
  * CS --- PD11
  * DC/RS --- PD12
  * RES --- PD13
  * CLK --- PB13
  * MISO/SDIN --- PB14
  */
void Oled_Init(void) {
	GPIO_InitTypeDef GPIO_InitStruct = {0};

	__HAL_RCC_GPIOB_CLK_ENABLE();
	__HAL_RCC_GPIOD_CLK_ENABLE();
	
	GPIO_InitStruct.Pin = GPIO_PIN_13 | GPIO_PIN_14;
	GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
	
	GPIO_InitStruct.Pin = GPIO_PIN_11 | GPIO_PIN_12 | GPIO_PIN_13;
	HAL_GPIO_Init(GPIOD, &GPIO_InitStruct);
	
	GPIO_InitStruct.Pin = GPIO_PIN_15;
	GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
	GPIO_InitStruct.Pull = GPIO_NOPULL;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
	
//	while(1) {
////		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_SET);
////		HAL_GPIO_WritePin(GPIOB, GPIO_PIN_13, GPIO_PIN_RESET);
//		GPIOB->BSRR = GPIO_PIN_13;
//		GPIOB->BSRR = (uint32_t)GPIO_PIN_13 << 16;
//	}

    OLED_RST_Clr();
	Delay(5000);
    OLED_RST_Set();
//	Delay(5000);
	
	Write_Register(0xAE); //Set Display Off
	Write_Register(0xB0); //Row address Mode Setting
	Write_Register(0x00);
	Write_Register(0x10); //Set Higher Column Address of display RAM
	Write_Register(0x00); //Set Lower Column Address of display RAM
	Write_Register(0xD5); //Set Display Clock Divide Ratio/Oscillator Frequency
	Write_Register(0x50); //50 125hz
	Write_Register(0xD9); //Set Discharge/Precharge Period
	Write_Register(0x22);
	Write_Register(0x40); //Set Display Start Line
	Write_Register(0x81); //The Contrast Control Mode Set
	Write_Register(0x30);
	Write_Register(0xA0); //Set Segment Re-map
	Write_Register(0xC0); //Set Common Output Scan Direction
	Write_Register(0xA4); //Set Entire Display OFF/ON
	Write_Register(0xA6); //Set Normal/Reverse Display
	Write_Register(0xA8); //Set Multiplex Ration
	Write_Register(0x3F);
	Write_Register(0xAD); //DC-DC Setting
	Write_Register(0x80); //DC-DC is disable
	Write_Register(0xD3); //Set Display Offset
	Write_Register(0x00);
	Write_Register(0xDB); //Set VCOM Deselect Level
	Write_Register(0x30);
	Write_Register(0xDC); //Set VSEGM Level
	Write_Register(0x30);
	Write_Register(0x33); //Set Discharge VSL Level 1.8V
//	Write_Register(0xAF); //Set Display On
    OLED_Set_Pos(0, 0);
    ClearLED(0x00);
	Write_Register(0xAF); // Set Display On
    OLED_Set_Pos(0, 0);
	
//	uint32_t thk = 0;
//	while(1) {
//		thk += 10;
//		OLED_ShowLargeNumber(thk);
//	}

//	while(1) {
//		
//		tstStatus = Spi_ReadStatus();
//		ClearLED(0x00);
//		for(uint32_t i = 0; i < 256; i ++) {
//			for(uint32_t j = 0; j < 64; j ++) {
//				drawpoint(i, j, 1);
//				delay_us(100);
//			}
//		}
//		
//		delay_ms(100);
//		ClearLED(0x00);
//		
//		for(uint32_t i = 0; i < 256; i ++) {
//			for(uint32_t j = 0; j < 64; j ++) {
//				Oled_DrawPixel(i, j, 1);
//				delay_us(100);
//			}
//		}
//		delay_ms(100);
//		
//	}
}

void OLED_Brightness(uint8_t percent) {
    Write_Register(0x81); // The Contrast Control Mode Set
    if (percent == 100) {
        Write_Register(0xff);
    } else {
        Write_Register((percent - 9) * 0xff / 100);
    }
}

void Oled_ClearScreen(void) {
	ClearLED(0x00);
}

//**************************四线SPI接口
void Write_Register(unsigned char Data) {
    unsigned char i;

    OLED_CS_Clr();
    OLED_RS_Clr();
    for (i = 0; i < 8; i++) {
        OLED_SCLK_Clr();
//		delay_us(1);
        if (Data & 0x80) { // Delay(1);
            OLED_SDIN_Set();
        } else { // Delay(1);
            OLED_SDIN_Clr();
        }
        Data = Data << 1;
//		delay_us(1);
        OLED_SCLK_Set();
//		delay_us(1);
    }
    OLED_RS_Set();
    OLED_CS_Set();
}

void Write_Parameter(unsigned char Data) {
    unsigned char i;

    OLED_CS_Clr();
    OLED_RS_Set();
    for (i = 0; i < 8; i++) {
        OLED_SCLK_Clr();
        if (Data & 0x80) { // Delay(1);
            OLED_SDIN_Set();

        } else { // Delay(1);
            OLED_SDIN_Clr();
        }
        Data = Data << 1;
        OLED_SCLK_Set();
    }
    OLED_RS_Set();
    OLED_CS_Set();
}

void Oled_CopyDispMemToScreenRect(uint8_t dispMem[OLED_HEIGHT][OLED_WIDTH/2], int32_t x0, int32_t y0, int32_t x1, int32_t y1) {
	int32_t x, y;
	int32_t xEnd = x1/2 + x1%2;
    
	for (y = y0; y <= y1; y++) {
		OLED_Set_Pos(x0/2, y);
		for (x = x0/2; x < xEnd; x++) {
			Write_Parameter(dispMem[y][x]);
		}
	}
}

void Oled_CopyDispMemToScreen(uint8_t dispMem[OLED_HEIGHT][OLED_WIDTH/2]) {
	int32_t x, y;
	int32_t x1 = OLED_X1/2;
	
	OLED_Set_Pos(0, 0);
	for (y = 0; y <= OLED_Y1; y++) {
		for (x = 0; x <= x1; x++) {
			Write_Parameter(dispMem[y][x]);
		}
	}
}

static uint8_t Spi_ReadDisplayData(void) {
	uint8_t data = 0;
	
	OLED_RS_Set();
    OLED_CS_Clr();

	for (int32_t i = 0; i < 8; i++) {
        OLED_SCLK_Clr();
		data = data << 1;
		data |= OLED_READ_MISO ? 0x01 : 0;
        OLED_SCLK_Set();
    }
    OLED_RS_Set();
    OLED_CS_Set();
	
	return data;
}

static uint8_t Oled_ReadDisplayData(int32_t x, int32_t y) {
	uint8_t data = 0;
    OLED_Set_Pos(x, y);
	return Spi_ReadDisplayData();
}

//******************************************************************************
//    函数说明：OLED显示列的起始终止地址
//    入口数据：a  列的起始地址
//              b  列的终止地址
//    返回值：  无
//******************************************************************************
void Column_Address(unsigned char x) {
    // Write_Register(0x15);			        // Set Column Address
    Write_Register(((x & 0xf0) >> 4) | 0x10); //   Default => 0x00
    Write_Register((x & 0x0f));               //   Default => 0x77
}

//******************************************************************************
//    函数说明：OLED显示行的起始终止地址
//    入口数据：a  行的起始地址
//              b  行的终止地址
//    返回值：  无
//******************************************************************************
void Row_Address(unsigned char a) {
    Write_Register(0xb0); // Set Row Address
    Write_Parameter(a);   //   Default => 0x00
    //	Write_Parameter(b);				//   Default => 0x3F
}

//******************************************************************************
//    函数说明：OLED清屏显示
//    入口数据：无
//    返回值：  无
//******************************************************************************
void ClearLED(unsigned char color) {
    unsigned char x, y;

    //	Write_Register(0x5C);    //开始写RAM命令

    for (y = 0; y < 64; y++) {
        for (x = 0; x < 128; x++) {
            Write_Parameter(color | (color << 4));
        }
    }
}

void bar(void) {
    unsigned char i, j, k;

    for (i = 0; i < 15; i++) {
        ;
        for (k = 0; k < 64; k++) {
            OLED_Set_Pos(8 * i, k);
            {
                for (j = 0; j < 16; j++) {

                    Write_Parameter(i | (i << 4));
                }
            }
        }
    }
}

void OLED_DrawLine(unsigned char x0, unsigned char y0, unsigned char x1, unsigned char y1, unsigned char color) {
    float k, b;
    uint8_t i;
    uint8_t x_p, y_p;
    if ((x0 != x1) && (y0 != y1)) {
        k = (float)(y1 - y0) / (x1 - x0);
        b = (float)(y0 - k * x0);
        if ((x1 - x0) > (y1 - y0)) {
            for (i = x0; i <= x1; i++) {
                y_p = k * i + b;
                drawpoint(i, y_p, color);
            }
        } else {
            for (i = y0; i <= y1; i++) {
                x_p = (float)(i - b) / k;
                drawpoint(x_p, i, color);
            }
        }
    } else {
        if (y0 == y1) {
            for (i = x0; i <= x1; i++) {
                drawpoint(i, y0, color);
            }
        }
        if (x0 == x1) {
            for (i = y0; i <= y1; i++) {
                drawpoint(x0, i, color);
            }
        }
    }
}

void OLED_Set_Pos(unsigned char x, unsigned char y) {
    Write_Register(0xB0);
    Write_Register(y);
    Write_Register(((x & 0xf0) >> 4) | 0x10);
    Write_Register((x & 0x0f));
}

void drawpoint(unsigned char x, unsigned char y, unsigned char color) // x: 0~127
{
    OLED_Set_Pos(x, y);
    Write_Parameter(color | (color << 4));
}

void Oled_DrawPixel(int32_t x, int32_t y, uint8_t color) // x: 0~255
{
    uint8_t xRam = x / 2;
	
	uint8_t data = Oled_ReadDisplayData(xRam, y);
	
//	OLED_Set_Pos(xRam, y);
//	data = Spi_ReadDisplayData();
	
	OLED_Set_Pos(xRam, y);
    if (xRam * 2 == x) { //偶数
        Write_Parameter(((color << 4) & 0xf0) | (data & 0x0f));
    } else {
        Write_Parameter((color & 0x0f) | (data & 0xf0));
    }
}



void Set_Colume_Address(unsigned char Addr1, unsigned char Addr2) {
    Write_Register(0x15);
    Write_Parameter(Addr1 + 28); //送列地址开始
    Write_Parameter(Addr2 + 28); //送列地址结束
}

//=======写行地址==============
void Set_Row_Address(unsigned char Addr1, unsigned char Addr2) {
    Write_Register(0x75);
    Write_Parameter(Addr1); //送行地址开始
    Write_Parameter(Addr2); //送行地址结束
}
//============显示一个8*16的字符==============================
void OLED_ShowChar(uint8_t x, uint8_t y, uint8_t chr) {
    unsigned char c = 0, i = 0;
    /*8x16点阵的上半部分即上8x8*/
    uint8_t m, x1, y1, y2;
    uint8_t Data1, Data2, DATA1 = 0;
    c = chr - ' '; //得到偏移后的值
    x1 = x;
    y1 = y;
    for (m = 0; m < 4; m++) {

        Data1 = F8X16[c * 16 + 2 * m + 0];
        Data2 = F8X16[c * 16 + 2 * m + 1];
        for (i = 0; i < 8; i++) {
            if (Data1 & (0x01 << i)) {
                DATA1 = 0xf0;
            }
            if (Data2 & (0x01 << i)) {
                DATA1 |= 0x0f;
            }
            OLED_Set_Pos(x1, y1);
            y1++;

            Write_Parameter(DATA1);
            DATA1 = 0;
        }
        x1++;
        y1 = y;
    }

    /*****************8x16点阵的下半部分即下8x8****************************************/

    x1 = x;
    y2 = y + 8;
    for (m = 0; m < 4; m++) {

        Data1 = F8X16[c * 16 + 2 * m + 8];
        Data2 = F8X16[c * 16 + 2 * m + 9];
        for (i = 0; i < 8; i++) {
            if (Data1 & (0x01 << i)) {
                DATA1 = 0xf0;
            }
            if (Data2 & (0x01 << i)) {
                DATA1 |= 0x0f;
            }
            OLED_Set_Pos(x1, y2);
            y2++;

            Write_Parameter(DATA1);
            DATA1 = 0;
        }
        x1++;
        y2 = y + 8;
    }
}

void OLED_ShowMediumChar(uint8_t x, uint8_t y, uint8_t chr) {
    unsigned char c = 0, i = 0, k = 0;
    uint8_t m, x1, y1, y2, y3, y4;
    uint8_t Data1, Data2, DATA1 = 0;
    c = chr - ' ';

    for (k = 0; k < 3; k++) {
        x1 = x;
        y1 = y + 8 * k;
        for (m = 0; m < 6; m++) {

            Data1 = F12X24[c * 36 + 2 * m + 12 * k];
            Data2 = F12X24[c * 36 + 2 * m + 12 * k + 1];
            for (i = 0; i < 8; i++) {
                if (Data1 & (0x01 << i)) {
                    DATA1 = 0xf0;
                }
                if (Data2 & (0x01 << i)) {
                    DATA1 |= 0x0f;
                }
                OLED_Set_Pos(x1, y1);
                y1++;

                Write_Parameter(DATA1);
                DATA1 = 0;
            }
            x1++;
            y1 = y + 8 * k;
        }
    }
}
void OLED_Show16x32Char(uint8_t x, uint8_t y, uint8_t chr) {
    unsigned char c = 0, i = 0, k = 0;
    uint8_t m, x1, y1, y2, y3, y4;
    uint8_t Data1, Data2, DATA1 = 0;
    c = chr - '0';
    if (chr == 'm')
        c = 13;
    else if (chr == ' ')
        c = 11;
    else if (chr == '.')
        c = 10;
    else if (chr == '-')
        c = 12;

    for (k = 0; k < 4; k++) {
        x1 = x;
        y1 = y + 8 * k;
        for (m = 0; m < 8; m++) {

            Data1 = F16X32[c * 64 + 2 * m + 16 * k];
            Data2 = F16X32[c * 64 + 2 * m + 16 * k + 1];
            for (i = 0; i < 8; i++) {
                if (Data1 & (0x01 << i)) {
                    DATA1 = 0xf0;
                }
                if (Data2 & (0x01 << i)) {
                    DATA1 |= 0x0f;
                }
                OLED_Set_Pos(x1, y1);
                y1++;

                Write_Parameter(DATA1);
                DATA1 = 0;
            }
            x1++;
            y1 = y + 8 * k;
        }
    }
}
void OLED_ShowLargerChar(uint8_t x, uint8_t y, uint8_t chr) {
    unsigned char c = 0, i = 0, k = 0;
    uint8_t m, x1, y1, y2, y3, y4;
    uint8_t Data1, Data2, DATA1 = 0;
    c = chr - '0';
    if (chr == 'm')
        c = 13;
    else if (chr == ' ')
        c = 11;
    else if (chr == '.')
        c = 10;
    else if (chr == '-')
        c = 12;

    for (k = 0; k < 8; k++) {
        x1 = x;
        y1 = y + 8 * k;
        for (m = 0; m < 16; m++) {

            Data1 = F32X64[c * 16 * 16 + 2 * m + 32 * k];
            Data2 = F32X64[c * 16 * 16 + 2 * m + 32 * k + 1];
            for (i = 0; i < 8; i++) {
                if (Data1 & (0x01 << i)) {
                    DATA1 = 0xf0;
                }
                if (Data2 & (0x01 << i)) {
                    DATA1 |= 0x0f;
                }
                OLED_Set_Pos(x1, y1);
                y1++;

                Write_Parameter(DATA1);
                DATA1 = 0;
            }
            x1++;
            y1 = y + 8 * k;
        }
    }
}

//uint8_t OLED_ShowLargeNumber(uint32_t num) {
//    unsigned char i = 0, j = 0;
//    uint8_t x = 0;
//    uint8_t y = 0;
//    uint8_t x_step = 16;
//    uint8_t n = 0;
//    uint32_t thick;
//    if (g_Dev.hmi_level != 0)
//        return 1;
//    if (num == 0) {
//        OLED_ShowLargerChar(x, y, '-');
//        x = x + x_step;
//        OLED_ShowLargerChar(x, y, '-');
//        x = x + x_step;
//        OLED_ShowLargerChar(x, y, '.');
//        x = x + x_step;
//        OLED_ShowLargerChar(x, y, '-');
//        x = x + x_step;
//        OLED_ShowLargerChar(x, y, '-');
//        x = x + x_step;
//        //		if(g_Thk.referenceBitOn == 1) {
//        //			OLED_Show16x32Char(x+2, 25,'-');
//        //		}
//        OLED_Show16x32Char(x + 2, 25, ' ');
//    } else {
//        if (g_Dev.unit_type == UNIT_SI) {    //国际制
//            if (g_Thk.referenceBitOn == 0) { // 保留小数点后两位, 第三位四舍五入
//                num = ((num % 10) >= 5) ? (num + 10) : num;
//            }

//            if (num < 100000) {
//                if (num > 9999) {
//                    n = (num % 100000) / 10000;
//                    OLED_ShowLargerChar(x, y, n + '0');
//                    x = x + x_step;
//                } else {
//                    n = (num % 100000) / 10000;
//                    OLED_ShowLargerChar(x, y, ' ');
//                    x = x + x_step;
//                }

//                n = (num % 10000) / 1000;
//                OLED_ShowLargerChar(x, y, n + '0');
//                x = x + x_step;
//                OLED_ShowLargerChar(x, y, '.');
//                x = x + x_step;
//                n = (num % 1000) / 100;
//                OLED_ShowLargerChar(x, y, n + '0');
//                x = x + x_step;
//                n = (num % 100) / 10;
//                OLED_ShowLargerChar(x, y, n + '0');
//                x = x + x_step;
//                n = (num % 10) / 1;
//                if (g_Thk.referenceBitOn == 1) {
//                    OLED_Show16x32Char(x + 2, 25, n + '0');
//                } else {
//                    OLED_Show16x32Char(x + 2, 25, ' ');
//                }
//            } else {
//                n = (num % 1000000) / 100000;
//                OLED_ShowLargerChar(x, y, n + '0');
//                x = x + x_step;
//                n = (num % 100000) / 10000;
//                OLED_ShowLargerChar(x, y, n + '0');
//                x = x + x_step;
//                n = (num % 10000) / 1000;
//                OLED_ShowLargerChar(x, y, n + '0');
//                x = x + x_step;
//                OLED_ShowLargerChar(x, y, '.');
//                x = x + x_step;
//                n = (num % 1000) / 100;
//                OLED_ShowLargerChar(x, y, n + '0');
//                x = x + x_step;
//                n = (num % 100) / 10;
//                OLED_Show16x32Char(x + 2, 25, n + '0');
//            }
//        } else {                              //英制单位
//                                              //			num = 10*1000;
//            if (g_Dev.unit_type == UNIT_BS) { // 如果是英制，则换算
//                thick = UnitConvert_mm2inch((uint32_t)num / 1000.0);
//                num = thick * 10000;
//                if ((g_Thk.referenceBitOn == 0) && (num % 10 >= 5))
//                    num = num + 10;
//                //				if((thick-(uint32_t)thick) >= 0.5) num = (uint32_t)thick + 1;
//                //				else num = (uint32_t)thick;
//            }
//            n = (num / 100000);
//            if (n != 0) {
//                OLED_ShowLargerChar(x, y, n + '0');
//                x = x + x_step;
//            }
//            n = (num % 100000) / 10000;
//            OLED_ShowLargerChar(x, y, n + '0');
//            x = x + x_step;
//            OLED_ShowLargerChar(x, y, '.');
//            x = x + x_step;
//            n = (num % 10000) / 1000;
//            OLED_ShowLargerChar(x, y, n + '0');
//            x = x + x_step;
//            n = (num % 1000) / 100;
//            OLED_ShowLargerChar(x, y, n + '0');
//            x = x + x_step;
//            n = (num % 100) / 10;
//            OLED_ShowLargerChar(x, y, n + '0');
//            x = x + x_step;
//            n = (num % 10) / 1;
//            if (g_Thk.referenceBitOn == 1)
//                OLED_Show16x32Char(x + 2, 25, n + '0');
//            else
//                OLED_Show16x32Char(x + 2, 25, ' ');
//        }
//    }
//    return 0;
//}

//显示一个字符号串
void OLED_ShowString(uint8_t x, uint8_t y, char *chr) {
    unsigned char j = 0;
    while (chr[j] != '\0') {
        OLED_ShowChar(x, y, chr[j]);
        x += 4;
        j++;
    }
}
void OLED_ShowMediumString(uint8_t x, uint8_t y, char *chr) {
    unsigned char j = 0;
    while (chr[j] != '\0') {
        OLED_ShowMediumChar(x, y, chr[j]);
        x += 6;
        j++;
    }
}
void GUI_DispString(uint8_t x, uint8_t y, uint8_t align, char *chr) {
    unsigned char j = 0;

    while (chr[j] != '\0') {
        j++;
    }
    if (align == 1) { // Center
        x = x - (j / 2) * 6;
        j = 0;
        while (chr[j] != '\0') {
            OLED_ShowMediumChar(x, y, chr[j]);
            x += 6;
            j++;
        }
    } else if (align == 2) { // Right
        x = x - j * 6;
        j = 0;
        while (chr[j] != '\0') {
            OLED_ShowMediumChar(x, y, chr[j]);
            x += 6;
            j++;
        }
    } else if (align == 0) { // Left
        x = x;
        j = 0;
        while (chr[j] != '\0') {
            OLED_ShowMediumChar(x, y, chr[j]);
            x += 6;
            j++;
        }
    }
}

void OLED_ShowCHN(uint8_t x, uint8_t y, char chr[], uint8_t size) {
    uint8_t i;
    for (i = 0; i < size; i++) {
        OLED_ShowCHinese(x, y, Hzk + 72 * chr[i]);
        x = x + 17;
    }
}

void OLED_ShowCHN_Content(uint8_t x, uint8_t line, uint8_t chr[], uint8_t size) {
    unsigned char i = 0;
    unsigned char y;
    if (line == 1)
        y = 0;
    else
        y = 32;
    x = x - size * 12;
    for (i = 0; i < size; i++) {
        OLED_ShowCHinese(x, y, Hzk + 72 * chr[i]);
        x = x + 12;
    }
}
void OLED_ShowCHN_AlignCenter(uint8_t x, uint8_t y, uint8_t chr[], uint8_t size) {
    uint8_t i;
    x = x - (size * 14) / 2;
    for (i = 0; i < size; i++) {
        OLED_ShowCHinese(x, y, Hzk + 72 * chr[i]);
        x = x + 14;
    }
}
void OLED_ShowMediumString_AlignRight(uint8_t x, uint8_t y, uint8_t *chr, uint8_t size) {
    unsigned char j = 0;
    x = x - size * 6;
    while (chr[j] != '\0') {
        OLED_ShowMediumChar(x, y, chr[j]);
        x += 6;
        j++;
    }
}
void OLED_ShowMenu_Content(uint8_t line, char *chr, uint8_t size) {
    unsigned char j = 0;
    unsigned char y, x;
    if (line == 1)
        y = 0;
    else
        y = 32;
    x = 124 - size * 6;
    while (chr[j] != '\0') {
        OLED_ShowMediumChar(x, y, chr[j]);
        x += 6;
        j++;
    }
}

void OLED_DispSetting_withUnderline(uint8_t line, char *chr, uint8_t x0, uint8_t underLinePos) {
    unsigned char j = 0;
    unsigned char y, x;
    uint8_t size = 4;
    if (line == 1)
        y = 0;
    else
        y = 32;
    x = x0;
    ClearLED(0x00);
    while (chr[j] != '\0') {
        OLED_ShowMediumChar(x, y, chr[j]);
        if (underLinePos == (j + 1)) {
            OLED_DrawLine(x, y + 26, x + size, y + 26, 100);
        }
        x += 6;
        j++;
    }
}

void GUI_DispMixText(uint8_t x, uint8_t y, uint8_t align, char *chr1, char zwchr[], char *chr2, uint8_t zwSize) {
    unsigned char j = 0;
    uint8_t i = 0;
    while (chr1[j] != '\0') {
        j++;
    }

    while (chr2[i] != '\0') {
        i++;
    }

    if (align == 2) { // Align right
        x = x - (i + j) * 6 - zwSize * 12;
    } else if (align == 1) { // Center
        x = x - ((i + j) * 6 + zwSize * 12) / 2;
    } else if (align == 0) { // Left
        x = x;
    }

    i = 0;
    while (chr1[i] != '\0') {
        OLED_ShowMediumChar(x, y, chr1[i]);
        x += 6;
        i++;
    }
    for (i = 0; i < zwSize; i++) {
        OLED_ShowCHinese(x, y, Hzk + 72 * zwchr[i]);
        x = x + 12;
    }
    i = 0;
    while (chr2[i] != '\0') {
        OLED_ShowMediumChar(x, y, chr2[i]);
        x += 6;
        i++;
    }
}

// m^n函数
uint32_t oled_pow(uint8_t m, uint8_t n) {
    uint32_t result = 1;
    while (n--)
        result *= m;
    return result;
}
//显示2个数字
// x,y :起点坐标
// len :数字的位数
// size:字体大小
// mode:模式	0,填充模式;1,叠加模式
// num:数值(0~4294967295);
void OLED_ShowNum(uint8_t x, uint8_t y, uint32_t num, uint8_t len, uint8_t size) {
    uint8_t t, temp;
    uint8_t enshow = 0;
    for (t = 0; t < len; t++) {
        temp = (num / oled_pow(10, len - t - 1)) % 10;
        if (enshow == 0 && t < (len)) {
            if (temp == 0) {
                OLED_ShowChar(x + (size / 4) * t, y, ' ');
                continue;
            } else
                enshow = 1;
        }
        OLED_ShowChar(x + 4 * t, y, temp + '0');
    }
}

void OLED_ShowCHinese(uint8_t x, uint8_t y, uint8_t *chn) { /*****************16x16点阵的上半部分，即上8x16****************************************/
    uint8_t j, i, k;
    uint8_t Data1, Data2, DATA1 = 0, DATA2 = 0;
    // x_number=x_number/4;
    k = y;
    for (j = 0; j < 24; j++) {
        OLED_Set_Pos(x, k);
        Data1 = *chn++;
        Data2 = *chn++;

        for (i = 0; i < 4; i++) {
            if (Data1 & (0x01 << (i * 2))) {
                DATA1 = 0xF0;
            }
            if (Data1 & (0x01 << (i * 2 + 1))) {
                DATA1 |= 0x0F;
            }
            Write_Parameter(DATA1);
            DATA1 = 0;
        }
        for (i = 0; i < 4; i++) {
            if (Data2 & (0x01 << (i * 2))) {
                DATA2 = 0xF0;
            }
            if (Data2 & (0x01 << (i * 2 + 1))) {
                DATA2 |= 0x0F;
            }
            Write_Parameter(DATA2);
            DATA2 = 0;
        }
        Data2 = *chn++;
        for (i = 0; i < 4; i++) {
            if (Data2 & (0x01 << (i * 2))) {
                DATA2 = 0xF0;
            }
            if (Data2 & (0x01 << (i * 2 + 1))) {
                DATA2 |= 0x0F;
            }
            Write_Parameter(DATA2);
            DATA2 = 0;
        }

        k++;
    }
}

void OLED_DrawBMP(uint8_t *BMP) // BMP为图像数据，Gray_Level=0时全屏只显示一种颜色，Gray_Level=1时显示支持灰度图片；Color为灰度值0～15;
{                          /*****************16x16点阵的上半部分，即上8x16****************************************/
    uint8_t j, k;

    for (k = 0; k < 64; k++) {
        OLED_Set_Pos(0, k);
        {
            for (j = 0; j < 128; j++) {

                Write_Parameter(*(BMP + 128 * k + j));
            }
        }
    }
}
// void GUI_DrawSnow(uint8_t x, uint8_t y, uint8_t color)//BMP为图像数据，Gray_Level=0时全屏只显示一种颜色，Gray_Level=1时显示支持灰度图片；Color为灰度值0～15;
//{/*****************16x16点阵的上半部分，即上8x16****************************************/
//	uint8_t j,i,k,t;
//	uint8_t Data1,Data2,DATA1=0,DATA2=0;

//	j = 0;
//	for(k=0;k<24;k++)
//	{
//
//		OLED_Set_Pos( x,   y+k) ;
//		for(t=0; t<3; t++)
//		{
//			Data1=gImage_snow[j++];
//			for(i=0;i<4;i++) {
//				if(Data1&(0x01<<(i*2)))
//				{
//					DATA1=color<<4;//0xF0;
//				}
//				if(Data1&(0x01<<(i*2+1)))
//				{
//					DATA1|=color;//0x0F;
//				}
//				Write_Parameter (DATA1);
//				DATA1=0;
//			}
//		}
//	}
//}
void GUI_DrawBmp24x24(uint8_t x, uint8_t y, uint8_t color, uint8_t *bmp) // BMP为图像数据，Gray_Level=0时全屏只显示一种颜色，Gray_Level=1时显示支持灰度图片；Color为灰度值0～15;
{                                                    /*****************16x16点阵的上半部分，即上8x16****************************************/
    uint8_t j, i, k, t;
    uint8_t Data1, Data2, DATA1 = 0, DATA2 = 0;

    j = 0;
    for (k = 0; k < 24; k++) {

        OLED_Set_Pos(x, y + k);
        for (t = 0; t < 3; t++) {
            Data1 = bmp[j++];
            for (i = 0; i < 4; i++) {
                if (Data1 & (0x01 << (i * 2))) {
                    DATA1 = color << 4; // 0xF0;
                }
                if (Data1 & (0x01 << (i * 2 + 1))) {
                    DATA1 |= color; // 0x0F;
                }
                Write_Parameter(DATA1);
                DATA1 = 0;
            }
        }
    }
}

void GUI_DrawWifi(uint8_t state) {
    uint8_t x = 116;
    uint8_t y = 37;
    if (state == 0x00) { // wifi 关闭
        GUI_DrawBmp24x24(x, y, 0, gImage_wifiConnectted);
    } else if (state == 0x01) { // wifi 打开 未连接
        GUI_DrawBmp24x24(x, y, 3, gImage_wifiNoConnect);
    } else if (state == 0x02) { // wifi 打开 已连接
        GUI_DrawBmp24x24(x, y, 3, gImage_wifiConnectted);
    }
}
void GUI_DrawSnow(uint8_t state) {
    uint8_t x = 98;
    uint8_t y = 0;
    if (state == 0x01) { // 雪花 关闭
        GUI_DrawBmp24x24(x, y, 0, gImage_snow);
    } else if (state == 0x00) { // 雪花显示
        GUI_DrawBmp24x24(x, y, 10, gImage_snow);
    }
}
void GUI_DrawLightning(uint8_t color) {
    uint8_t x = 115;
    uint8_t y = 0;
    GUI_DrawBmp24x24(x, y, color, gImage_lightning);
}

void Draw_Progressbar(uint8_t val, uint8_t total) {
    uint8_t y0, y1;
    y0 = (val)*64 / total;
    y1 = (val + 1) * 64 / total - 1;
    OLED_DrawLine(127, 0, 127, 63, 0);
    OLED_DrawLine(127, y0, 127, y1, 1);
}

//void Oled_ShowWifiName(char *str) {
//    uint8_t x = 20;
//    uint8_t y = 20;
//    uint8_t i = 0;
//    ClearLED(0x00);
//    for (i = 0; i < 15; i++) {
//        if (str[i] == '\0')
//            break;
//        OLED_ShowMediumChar(x, y, str[i]);
//        x = x + 7;
//    }
//    //	if(getWifiState() == 0x01) {
//    GUI_DrawWifi(getWifiState());
//    //	}
//    delay_ms(1000);
//    delay_ms(600);
//    Oled_ShowMainScreen(g_Emat.Emit_Switch, g_Dev.wifiState);
//    BatteryHandler(1);
//}

void Oled_ShowLongFlag(uint8_t longmode) {
    uint8_t x = 98;
    uint8_t y = 25;
    if (longmode) {
        OLED_ShowString(x, y, "long");
    } else {
        OLED_ShowString(x, y, "    ");
    }
}

void Oled_ShowUnit(uint8_t unitSystem) {
    uint8_t x = 98;
    uint8_t y = 29 + 10;
    if (unitSystem == 0) { // SI (mm)
        OLED_ShowMediumChar(x, y, 'm');
        OLED_ShowMediumChar(x + 7, y, 'm');
    } else { // BS (inch)
        OLED_ShowMediumChar(x, y, 'i');
        OLED_ShowMediumChar(x + 7, y, 'n');
        OLED_ShowMediumChar(x + 14, y, '.');
    }
}

void Oled_ShowWifiState(uint8_t state) {
    uint8_t x = 98;
    uint8_t y = 0;
    if (state == 0) { // wifi 关闭
                      //		OLED_ShowMediumChar(x,y,'m');
                      //		OLED_ShowMediumChar(x+7,y,'m');
    } else {          // wifi 开启
        OLED_ShowMediumChar(x, y, 'w');
        OLED_ShowMediumChar(x + 7, y, ' ');
        if (state == 0x02) { // 已连接 APP
            OLED_ShowMediumChar(x + 7, y, '.');
        }
    }
}
static void Oled_DrawRect(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1) {
    OLED_DrawLine(x0, y0, x1, y0, 5);
    OLED_DrawLine(x0, y1, x1, y1, 5);
    OLED_DrawLine(x0, y0, x0, y1, 5);
    OLED_DrawLine(x1, y0, x1, y1, 5);
}
void GUI_FillRect(uint8_t x0, uint8_t y0, uint8_t x1, uint8_t y1, uint8_t color) {
    uint8_t i = 0;
    for (i = x0; i < x1; i++) {
        OLED_DrawLine(i, y0, i, y1, color);
    }
}

uint8_t needErasureLighting = 0;
void Oled_ShowBattery(uint8_t Li, uint8_t isCharge) {
    uint8_t x0, x1, y0, y1;
    x0 = 114;
    x1 = 127;
    y0 = 0 + 6;
    y1 = 11 + 6;

    if (isCharge == 1) { // 充电时画闪电
        GUI_DrawLightning(5);
        needErasureLighting = 1;
        // 画电池框
        OLED_DrawLine(x0, y0, x0 + 5, y0, 5);
        OLED_DrawLine(x1 - 3, y0, x1, y0, 5);
        OLED_DrawLine(x0, y1, x0 + 4, y1, 5);
        OLED_DrawLine(x1 - 4, y1, x1, y1, 5);
        OLED_DrawLine(x0, y0, x0, y1, 5);
        OLED_DrawLine(x1, y0, x1, y1, 5);
    } else {
        if (needErasureLighting != 0) {
            GUI_DrawLightning(0);
            needErasureLighting = 0;
        }
        if (Li >= 10) {
            OLED_DrawLine(x1 - 2, y0 + 2, x1 - 2, y1 - 2, 1);
        } else {
            OLED_DrawLine(x1 - 2, y0 + 2, x1 - 2, y1 - 2, 0);
        }
        if (Li >= 10)
            OLED_DrawLine(x1 - 2, y0 + 2, x1 - 2, y1 - 2, 1);
        else
            OLED_DrawLine(x1 - 2, y0 + 2, x1 - 2, y1 - 2, 0);
        if (Li >= 20)
            OLED_DrawLine(x1 - 3, y0 + 2, x1 - 3, y1 - 2, 1);
        else
            OLED_DrawLine(x1 - 3, y0 + 2, x1 - 3, y1 - 2, 0);
        if (Li >= 30)
            OLED_DrawLine(x1 - 4, y0 + 2, x1 - 4, y1 - 2, 2);
        else
            OLED_DrawLine(x1 - 4, y0 + 2, x1 - 4, y1 - 2, 0);
        if (Li >= 40)
            OLED_DrawLine(x1 - 5, y0 + 2, x1 - 5, y1 - 2, 2);
        else
            OLED_DrawLine(x1 - 5, y0 + 2, x1 - 5, y1 - 2, 0);
        if (Li >= 50)
            OLED_DrawLine(x1 - 6, y0 + 2, x1 - 6, y1 - 2, 3);
        else
            OLED_DrawLine(x1 - 6, y0 + 2, x1 - 6, y1 - 2, 0);
        if (Li >= 60)
            OLED_DrawLine(x1 - 7, y0 + 2, x1 - 7, y1 - 2, 3);
        else
            OLED_DrawLine(x1 - 7, y0 + 2, x1 - 7, y1 - 2, 0);
        if (Li >= 70)
            OLED_DrawLine(x1 - 8, y0 + 2, x1 - 8, y1 - 2, 4);
        else
            OLED_DrawLine(x1 - 8, y0 + 2, x1 - 8, y1 - 2, 0);
        if (Li >= 80)
            OLED_DrawLine(x1 - 9, y0 + 2, x1 - 9, y1 - 2, 4);
        else
            OLED_DrawLine(x1 - 9, y0 + 2, x1 - 9, y1 - 2, 0);
        if (Li >= 90)
            OLED_DrawLine(x1 - 10, y0 + 2, x1 - 10, y1 - 2, 4);
        else
            OLED_DrawLine(x1 - 10, y0 + 2, x1 - 10, y1 - 2, 0);
        if (Li >= 100)
            OLED_DrawLine(x1 - 11, y0 + 2, x1 - 11, y1 - 2, 4);
        else
            OLED_DrawLine(x1 - 11, y0 + 2, x1 - 11, y1 - 2, 0);
        if (Li < 10) {
            OLED_DrawLine(x1 - 10, y0 + 2, x1 - 3, y1 - 2, 5);
            OLED_DrawLine(x1 - 10, y1 - 2, x1 - 3, y0 + 2, 5);
        }
        Oled_DrawRect(x0, y0, x1, y1); // 画电池框
    }
    //	GUI_DrawRect(x0, y0, x1, y1); // 画电池框
    OLED_DrawLine(x0 - 1, y0 + 3, x0 - 1, y1 - 3, 5); // 画电池头儿
}

//void Oled_ShowMainScreen(uint8_t emit, uint8_t wState) {
//    ClearLED(0x00);
//    Oled_ShowUnit(g_Dev.unit_type);
//    //	OLED_ShowLargeNumber(thick_val);
//    GUI_DrawWifi(wState);
//    BatteryHandler(0);
//    GUI_DrawSnow(emit);
//}

void Oled_ShowRestoreFactory() {
    char hanziBuf[10];
    ClearLED(0x00);
    GUI_DispMixText(0, 0, 0, "Reset?", hanziBuf, "\0", 0);
    GUI_DispMixText(0, 32, 0, "M Reset    ", hanziBuf, "UP Cancel", 0);
}
void Oled_ShowRestoreFactory_Cancel() {
    char hanziBuf[10];
    ClearLED(0x00);
    GUI_DispMixText(0, 0, 0, "Reset Cancel", hanziBuf, "\0", 0);
}
void Oled_ShowRestoreFactory_Resetting() {
    char hanziBuf[10];
    ClearLED(0x00);
    GUI_DispMixText(0, 0, 0, "Resetting...", hanziBuf, "\0", 0);
}
void Oled_ShowRestoreFactory_ResetOk() {
    char hanziBuf[10];
    ClearLED(0x00);
    GUI_DispMixText(0, 0, 0, "Reset OK", hanziBuf, "\0", 0);
}

//uint32_t CommonVelcocity_Show(uint8_t lang, uint8_t id, uint32_t velcity_old) {
//    char hzbuf[10];
//    char str[20];
//    char str1[20] = "Now,";
//    uint32_t velcity = 0;
//    uint8_t alignRight = 124;
//    if (lang == 1) {
//        switch (id) { // Chinese
//        case 0:
//            hzbuf[0] = 30;
//            hzbuf[1] = 31;
//            hzbuf[2] = 73;
//            hzbuf[3] = 74; //"当前材料"
//            sprintf(str, "%d", velcity_old);
//            str1[0] = '@';
//            str1[1] = 0;
//            strcat(str1, str);
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 4);
//            velcity = velcity_old;
//            break;
//        case 1:
//            hzbuf[0] = 57;
//            hzbuf[1] = 41;
//            hzbuf[2] = 35; // "低碳钢"
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 3);
//            velcity = 3230;
//            break;
//        case 2:
//            hzbuf[0] = 33;
//            hzbuf[1] = 34;
//            hzbuf[2] = 35; // "304不锈钢"
//            GUI_DispMixText(alignRight, 0, 2, "304", hzbuf, "\0", 3);
//            velcity = 3145;
//            break;
//        case 3:
//            hzbuf[0] = 42;
//            hzbuf[1] = 32; // “铸铁”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
//            velcity = 2400;
//            break;
//        case 4:
//            hzbuf[0] = 36;
//            hzbuf[1] = 39;
//            hzbuf[2] = 40; // "铝合金"
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 3);
//            velcity = 3100;
//            break;
//        case 5:
//            hzbuf[0] = 72;
//            hzbuf[1] = 37; // “紫铜”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
//            velcity = 2260;
//            break;
//        case 6:
//            hzbuf[0] = 50;
//            hzbuf[1] = 37; // “黄铜”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
//            velcity = 2120;
//            break;
//        case 7:
//            hzbuf[0] = 71;
//            hzbuf[1] = 37; // “青铜”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
//            velcity = 2230;
//            break;
//        case 8:
//            hzbuf[0] = 45;
//            hzbuf[1] = 46; // “纯钛”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "\0", 2);
//            velcity = 3120;
//            break;
//        case 9:
//            hzbuf[0] = 61;
//            hzbuf[1] = 62; // “参数1”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "1", 2);
//            velcity = g_Thk.usrSaveVelocity_1;
//            break;
//        case 10:
//            hzbuf[0] = 61;
//            hzbuf[1] = 62; // “参数2”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "2", 2);
//            velcity = g_Thk.usrSaveVelocity_2;
//            break;
//        case 11:
//            hzbuf[0] = 61;
//            hzbuf[1] = 62; // “参数3”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "3", 2);
//            velcity = g_Thk.usrSaveVelocity_3;
//            break;
//        default:
//            break;
//        }
//    } else if (lang == 0) { // English
//        switch (id) {
//        case 0:
//            hzbuf[0] = 30;
//            hzbuf[1] = 31; //"当前"
//            // sprintf(str, "Now@%d", velcity_old);
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Curr. Mat.", 0);
//            velcity = velcity_old;
//            break;
//        case 1: // "低碳钢"
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Mild Steel", 0);
//            velcity = 3230;
//            break;
//        case 2: // "304不锈钢"
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "304 SS", 0);
//            velcity = 3145;
//            break;
//        case 3: // “铸铁”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Cast Iron", 0);
//            velcity = 2400;
//            break;
//        case 4: // "铝合金"
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Aluminum", 0);
//            velcity = 3100;
//            break;
//        case 5: // “紫铜”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Copper", 0);
//            velcity = 2260;
//            break;
//        case 6: // “黄铜”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Brass", 0);
//            velcity = 2120;
//            break;
//        case 7: // “青铜”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Bronze", 0);
//            velcity = 2230;
//            break;
//        case 8: // “纯钛”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "Titanium", 0);
//            velcity = 3120;
//            break;
//        case 9: // “参数1”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "User Def.1", 0);
//            velcity = g_Thk.usrSaveVelocity_1;
//            break;
//        case 10: // “参数2”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "User Def.2", 0);
//            velcity = g_Thk.usrSaveVelocity_2;
//            break;
//        case 11: // “参数3”
//            GUI_DispMixText(alignRight, 0, 2, "\0", hzbuf, "User Def.3", 0);
//            velcity = g_Thk.usrSaveVelocity_3;
//            break;
//        default:
//            break;
//        }
//    }
//    return velcity;
//}

void OLED_ShowDF_State(char *chr) {
    uint8_t x = 120;
    uint8_t y = 20;
    unsigned char j = 0;
    while (chr[j] != '\0') {
        OLED_ShowChar(x, y, chr[j]);
        x += 4;
        j++;
    }
}

void Oled_DispInLine1(char *str, uint8_t align) {
    char hanzi[1];
    uint8_t i;
    uint8_t N = 15;
    char string[20];

    strcpy(string, str);
    for (i = strlen(str); i < N; i++) {
        string[i] = ' ';
    }
    string[i] = 0x00;

    GUI_DispMixText(2, M_LINE_1_Y, align, string, hanzi, "\0", 0);
}

void Oled_DispInLine2(char *str, uint8_t align) {
    char hanzi[1];
    uint8_t i;
    uint8_t N = 19;
    char string[20];

    strcpy(string, str);
    for (i = strlen(str); i < N; i++) {
        string[i] = ' ';
    }
    string[i] = 0x00;
    GUI_DispMixText(2, M_LINE_2_Y, align, string, hanzi, "\0", 0);
}
