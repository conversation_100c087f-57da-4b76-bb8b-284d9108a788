#ifndef __APP_THICKNESS_H
#define __APP_THICKNESS_H
#include <stdint.h>
#include "app.h"

#define THK_WAVE_LEN				4096
#define THK_LONG_WAVE_LEN			4096*2 // 4096*8



typedef struct
{
	
	WAVE_T		wave;
	WAVE_T		longWave;
	uint8_t		longMode;
	
	uint8_t		resValid;
	double		thkResValUm;
	uint16_t 	ultraSonic_Velocity; 	//功能模式
	uint16_t 	usrSaveVelocity_1; 		//功能模式
	uint16_t 	usrSaveVelocity_2; 		//功能模式
	uint16_t 	usrSaveVelocity_3; 		//功能模式
	uint8_t 	pre_velc_id; 			//功能模式
	float		caliThicknessVal;
	uint8_t		referenceBitOn;
	uint8_t 	supplyEmitEnable;
}THICK_FUNCTION_T;
extern THICK_FUNCTION_T g_Thk;

void App_Thk_Init(void);
//void Thk_DispMainUI(uint8_t animation);
void App_Thickness_MainTask(void);
uint32_t Thk_MeasureThickness(uint16_t vel, uint8_t showAgc);
uint32_t Thk_MeasureThk_Long(WAVE_T *wave, uint32_t vel);

#endif
