/**
  ******************************************************************************
  * @file    ui_draw_wave.h
  * @brief   This file contains all the function prototypes for
  *          the ui_draw_wave.c file
  ******************************************************************************
  * @attention
  *
  ******************************************************************************
  */
#ifndef __UI_DRAW_WAVE_H__
#define __UI_DRAW_WAVE_H__

#ifdef __cplusplus
extern "C" {
#endif
#include <stdint.h>

void UiDrawWave_Init(void);
//void UiDrawWave_DrawWave(int16_t *pWaveBuf, uint32_t waveLen);
void UiDrawWave_DisplayWaveAndThk(int16_t *pWaveBuf, uint32_t waveLen, double thkUm, uint8_t isUseBsUnit, uint8_t isDispRefBit,  uint8_t isShowEnvelope);
void UiDrawWave_DisplayInvalidWaveAndThk(void);

#ifdef __cplusplus
}
#endif
#endif

/******************************* end of file **********************************/