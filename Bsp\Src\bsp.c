/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : bsp.c
  * @project : 
  * @brief   : 
  * <AUTHOR> PR Team
  * @since   : 2024/07/29
  * @version : V1.0 
  * @history :
  *	2024/07/29: V0.1
  *	  1) 首次创建;
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "bsp.h"


void Bsp_Init(void)
{


}

/**
  * @brief  This function is executed in case of error occurrence.
  * @retval None
  */
void Bsp_ErrorHandler(void)
{
  /* USER CODE BEGIN Error_Handler_Debug */
  /* User can add his own implementation to report the HAL error return state */
  __disable_irq();
  while (1)
  {
  }
  /* USER CODE END Error_Handler_Debug */
}

/******************************* end of file **********************************/
