/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : bsp_pwr.c
  * @project : MiniBox
  * @brief   : 在线监测小模块
  * <AUTHOR> PR Team
  * @since   : 2024/07/22
  * @version : V1.0 
  * @history :
  *	2024/07/22: V0.1
  *	  1) 首次创建;
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "bsp_pwr.h"
#include "stm32u5xx_hal.h"


#define POWER_CTRL(on) HAL_GPIO_WritePin(GPIOA, GPIO_PIN_3, on ? GPIO_PIN_SET : GPIO_PIN_RESET)


/** Configure pins
     PA3   ------> EN-VOUT
*/
void BspPwr_Init(void)
{

  GPIO_InitTypeDef GPIO_InitStruct = {0};

  __HAL_RCC_GPIOA_CLK_ENABLE();

  HAL_GPIO_WritePin(GPIOA, GPIO_PIN_3, GPIO_PIN_RESET);

  /*Configure GPIO pin : PA3 */
  GPIO_InitStruct.Pin = GPIO_PIN_3;
  GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
  GPIO_InitStruct.Pull = GPIO_NOPULL;
  GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
  HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  BspPwr_PowerOnOtherParts();
}


/******************************* end of file **********************************/
