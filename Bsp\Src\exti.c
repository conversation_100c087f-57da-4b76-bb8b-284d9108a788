#include "exti.h"
#include "stm32f7xx_hal.h"
#include "adc.h"
#include "app.h"
#include "delay.h"
#include "key.h"
#include "oled.h"
#include "wifi.h"
//////////////////////////////////////////////////////////////////////////////////

//uint8_t extiInterrupt_M_key_pressed = 0;

//外部中断初始化程序
//初始化PE2~4,PA0为中断输入.
void EXTI_Init(void) {
	GPIO_InitTypeDef GPIO_Initure = {0};
	__HAL_RCC_GPIOA_CLK_ENABLE();
	
	 /* 配置EXTI_Line6, 充电插拔中断 PA6*/
    GPIO_Initure.Pin = GPIO_PIN_6;
    GPIO_Initure.Mode = GPIO_MODE_IT_RISING_FALLING;
    GPIO_Initure.Pull = GPIO_NOPULL;
    HAL_GPIO_Init(GPIOA, &GPIO_Initure);
	
	/* 配置EXTI_Line13: DOWN 按键 PC14*/
	/* 配置EXTI_Line14: UP 按键 PC14*/
	/* 配置EXTI_Line15: M 按键 PC15*/
	GPIO_Initure.Pin = GPIO_PIN_13 | GPIO_PIN_14 | GPIO_PIN_15;
    GPIO_Initure.Mode=GPIO_MODE_IT_FALLING;
    GPIO_Initure.Pull=GPIO_PULLUP;
    HAL_GPIO_Init(GPIOC, &GPIO_Initure);
	
	HAL_NVIC_SetPriority(EXTI9_5_IRQn, 4, 4);
    HAL_NVIC_EnableIRQ(EXTI9_5_IRQn);
	
	HAL_NVIC_SetPriority(EXTI15_10_IRQn, 4, 4);
    HAL_NVIC_EnableIRQ(EXTI15_10_IRQn);
	
	
//    NVIC_InitTypeDef NVIC_InitStructure;
//    EXTI_InitTypeDef EXTI_InitStructure;

//    RCC_APB2PeriphClockCmd(RCC_APB2Periph_SYSCFG, ENABLE); //使能SYSCFG时钟

//    SYSCFG_EXTILineConfig(EXTI_PortSourceGPIOC, EXTI_PinSource4);
//    SYSCFG_EXTILineConfig(EXTI_PortSourceGPIOC, EXTI_PinSource15);

//    /* 配置EXTI_Line6, 充电插拔中断*/
//    EXTI_InitStructure.EXTI_Line = EXTI_Line6;
//    EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;            //中断事件
//    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Rising_Falling; //上下沿触发
//    EXTI_InitStructure.EXTI_LineCmd = ENABLE;
//    EXTI_Init(&EXTI_InitStructure);

//    /* 配置EXTI_Line15: M 按键*/
//    EXTI_InitStructure.EXTI_Line = EXTI_Line15;
//    EXTI_InitStructure.EXTI_Mode = EXTI_Mode_Interrupt;     //中断事件
//    EXTI_InitStructure.EXTI_Trigger = EXTI_Trigger_Falling; //下降沿触发
//    EXTI_InitStructure.EXTI_LineCmd = ENABLE;               //使能LINE0
//    EXTI_Init(&EXTI_InitStructure);

//    NVIC_InitStructure.NVIC_IRQChannel = EXTI9_5_IRQn;
//    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0x04;
//    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0x04;
//    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
//    NVIC_Init(&NVIC_InitStructure);

//    NVIC_InitStructure.NVIC_IRQChannel = EXTI15_10_IRQn;
//    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0x04;
//    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0x04;
//    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
//    NVIC_Init(&NVIC_InitStructure);
}

void EXTI9_5_IRQHandler(void)
{
    HAL_GPIO_EXTI_IRQHandler(GPIO_PIN_6);
}

void EXTI15_10_IRQHandler(void)
{
	HAL_GPIO_EXTI_IRQHandler(GPIO_PIN_13);
	HAL_GPIO_EXTI_IRQHandler(GPIO_PIN_14);
    HAL_GPIO_EXTI_IRQHandler(GPIO_PIN_15);
}

void HAL_GPIO_EXTI_Callback(uint16_t GPIO_Pin)
{
    switch(GPIO_Pin)
    {
        case GPIO_PIN_6:
            delay_ms(1);
			BatteryHandler(1);
//			EXTI_ClearITPendingBit(EXTI_Line6);
            break;
		case GPIO_PIN_13:
            if (g_Dev.hmi_level == 0) {
				delay_ms(10); //消抖
				if (KEY_DOWN == 0) {
					Key_SetDownKeyPressed();
				}
			}
            break;
        case GPIO_PIN_14:
            if (g_Dev.hmi_level == 0) {
				delay_ms(10); //消抖
				if (KEY_UP == 0) {
					Key_SetUpKeyPressed();
				}
			}
            break;
		case GPIO_PIN_15:
            if (g_Dev.hmi_level == 0) {
				delay_ms(10); //消抖
				if (KEY_SET == 0) {
					Key_SetMKeyPressed();
				}
			}
            break;
    }
}
