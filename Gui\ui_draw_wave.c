/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : ui_draw_wave.c
  * @project : MiniBox
  * @brief   : 在线监测小模块
  * <AUTHOR> PR Team
  * @since   : 2024/08/15
  * @version : V1.0 
  * @history :
  *	2024/08/15: V0.1
  *	  1) 首次创建;
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "ui_draw_wave.h"
#include <string.h>
#include <stdio.h>
#include <math.h>
#include <stdlib.h>
#include "gui_lib.h"
#include "fpga.h"
#include "unit_utils.h"


static uint32_t WavePointsConvert2Path(GUI_RECT* rCanvas, GUI_POINT* ptsBuf, uint32_t ptsBufLen, int16_t* waveBuf, uint32_t waveLen, float yMaxVal) {
    uint32_t i, j, k;
    float yMax = yMaxVal; //2048;
    float yMin = -yMaxVal; //-2048;
    int16_t xPixLast = 0;
    GUI_POINT point;
    //每列像素,最多需要保留4各点的即可,第一个、最后一个、最大、最小, 就可还原出波形的外形
    for (i = 0, k = 0, j = 0; i < waveLen; i++) {
        point.x = i * (rCanvas->x1 - rCanvas->x0) / (waveLen - 0) + rCanvas->x0;
        point.y = ((yMax - waveBuf[i]) * (rCanvas->y1 - rCanvas->y0) / (yMax - yMin)) + rCanvas->y0;
        if (point.x != xPixLast) { //本次X坐标和上次不同, j=0, 下一组数据
            j = 0;
        }
        else {
            j++;
        }
        if (j <= 3) { //第一个点
            ptsBuf[k].x = point.x;
            ptsBuf[k].y = point.y;
            k++;
            if (k >= ptsBufLen) {
                break; //Over Range
            }
        }
        else if (j >= 4) { //超过4个点(5+), 需要从前三个里挑个空位
            if (((ptsBuf[k - 1].y <= ptsBuf[k - 2].y) && (ptsBuf[k - 1].y >= ptsBuf[k - 3].y)) || ((ptsBuf[k - 1].y <= ptsBuf[k - 3].y) && (ptsBuf[k - 1].y >= ptsBuf[k - 2].y))) {

            }
            else if (((ptsBuf[k - 2].y <= ptsBuf[k - 1].y) && (ptsBuf[k - 2].y >= ptsBuf[k - 3].y)) || ((ptsBuf[k - 2].y <= ptsBuf[k - 3].y) && (ptsBuf[k - 2].y >= ptsBuf[k - 1].y))) {
                ptsBuf[k - 2].y = ptsBuf[k - 1].y;
            }
            else {
                ptsBuf[k - 3].y = ptsBuf[k - 1].y;
            }
            ptsBuf[k - 1].y = point.y;
        }
        xPixLast = point.x;
    }
    return k; //返回显示区域的点数
}

static int32_t CalcEnvelopePoints(GUI_RECT* rCanvas, GUI_POINT *pointBuf, uint32_t pointBufLen, int16_t *waveBuf, uint32_t waveLen, float yMaxVal)
{
	int32_t i = 0;
	int32_t k = 0;
	int16_t sign0;
	int16_t sign1;
	int32_t xPixels = rCanvas->x1 - rCanvas->x0;
	int32_t yPixels = rCanvas->y1 - rCanvas->y0;
	
	sign0 = 1;
	for(i = 0; i < waveLen - 1; i++) {
		sign1 = (abs(waveBuf[i+1]) > abs(waveBuf[i])) ? 1 : -1;
		
		if(sign0 - sign1 == 2) { //极大值
			pointBuf[k].x = i * xPixels / (waveLen - 0) + rCanvas->x0;
			pointBuf[k].y = rCanvas->y1 - (abs(waveBuf[i]) * yPixels / (yMaxVal - 0));
			k++;
			
			if(k >= pointBufLen) {
				return k;
			}

		}
		
		sign0 = sign1;
	}
	
	//最后一点
	pointBuf[k].x = rCanvas->x1;
	pointBuf[k].y = rCanvas->y1 - (abs(waveBuf[waveLen-1]) * yPixels / (yMaxVal - 0));
	k++;
	
	return k;
}

static void UiDrawWave_DrawWave(GUI_RECT* rCanvas, int16_t *pWaveBuf, uint32_t waveLen, uint8_t isShowEnvelope) {
	int32_t waveRangeMax = Fpga_GetAdcMaxVal();
	uint32_t ptsNum = 0;
	GUI_POINT ptsBuf[256*6];
	
	if(isShowEnvelope) //包络
		ptsNum = CalcEnvelopePoints(rCanvas, ptsBuf, sizeof(ptsBuf)/sizeof(ptsBuf[0]), pWaveBuf, waveLen, waveRangeMax);
	else //射频
		ptsNum = WavePointsConvert2Path(rCanvas, ptsBuf, sizeof(ptsBuf)/sizeof(ptsBuf[0]), pWaveBuf, waveLen, waveRangeMax);
	
	GUI_DrawPolyLine(ptsBuf, ptsNum, 0, 0);
}

void UiDrawWave_DisplayWaveAndThk(int16_t *pWaveBuf, uint32_t waveLen, double thkUm, uint8_t isUseBsUnit, uint8_t isDispRefBit,  uint8_t isShowEnvelope) {
	char str[32];
	GUI_RECT rCanvas = {0, 0, SCREEN_WIDTH - 1 - 64, SCREEN_HEIGHT - 1};
	
	GUI_ClearScreen();
	GUI_SetColor(12);
	UiDrawWave_DrawWave(&rCanvas, pWaveBuf, waveLen, isShowEnvelope);
	
	if(isUseBsUnit) { //英制(inch)
		double thkInch = UnitConvert_mm2inch(thkUm / 1000.0);
		snprintf(str, sizeof(str), (isDispRefBit ? "%.04f" : "%.03f"), thkInch);
	}
	else { //国际制(mm)
		snprintf(str, sizeof(str), (isDispRefBit ? "%.03f" : "%.02f"), thkUm/1000.0);
	}
	
	GUI_SetColor(15);
	GUI_SetTextAlign(GUI_TA_TOP | GUI_TA_RIGHT);
	
	GUI_DispStringAt(str, rCanvas.x1, rCanvas.y0);
	
	GUI_InvalidateRect(&rCanvas);
	GUI_Exec();
}

void UiDrawWave_DisplayInvalidWaveAndThk(void) {
	char str[32];
	GUI_RECT rCanvas = {0, 0, SCREEN_WIDTH - 1 - 64, SCREEN_HEIGHT - 1};
	
	GUI_ClearScreen();
	GUI_SetColor(15);
	GUI_SetTextAlign(GUI_TA_VCENTER | GUI_TA_HCENTER);
	GUI_DispStringAt("No Wave", (rCanvas.x0 + rCanvas.x1)/2, (rCanvas.y0 + rCanvas.y1)/2);
	
	GUI_InvalidateRect(&rCanvas);
	GUI_Exec();
}


//int16_t tstWaveBuf[4096];
void UiDrawWave_Init(void) {
//	int32_t j = 0;
//	
//	while(1) {
//		
//		j++;
//		for(int32_t i = 0; i < 4096; i++) {
//			tstWaveBuf[i] = 4096 * sin(2*3.14159*i/500);
//		}
//		
//		UiDrawWave_DisplayWave(tstWaveBuf, 4096);
//	}
}




/******************************* end of file **********************************/
