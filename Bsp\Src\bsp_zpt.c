/**
 ********************************************************************************
 * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
 * @file    : bsp_zpt.c
 * @project : HT300(V2023)
 * @brief   : 零点相关内容
 * <AUTHOR> PR Team
 * @since   : 2024/05/07
 * @version : V1.0
 * @history :
 *	2024/05/07:
 *	1) 创建;
 *
 ********************************************************************************
 * @note
 *
 ********************************************************************************
 * @attention
 *
 ********************************************************************************
 */
#include "bsp_zpt.h"
#include <stdlib.h>
#include <math.h>
#include "emat.h"
#include "data_persistence.h"
#include "app.h"
ZPT_T g_Zpt;
//MKS_T g_Mks = {0};

#define ZPT_MIN_NS	10
#define ZPT_MAX_NS	2000


#define MKS_PUL_NUM	3


double Zpt_GetFactoryZptTimeNs(void) {
	return DEFAULT_ZPT_TIME_NS_NO_MKS;
}

uint8_t Zpt_SetZptTimeNs(int32_t zero_ns)
{
	if((zero_ns >= ZPT_MIN_NS) && (zero_ns <= ZPT_MAX_NS)) {
		g_Zpt.zeroPoint_ns = zero_ns;
		return 0; //成功
	}
	else {
		return 1; //失败
	}
}

uint8_t Zpt_CheckZptIsValid(int32_t zptNs)
{
	if((zptNs >= ZPT_MIN_NS) && (zptNs <= ZPT_MAX_NS))
		return 1; //有效
	else
		return 0; //无效
}


int32_t Zpt_GetZptTimeNs(void)
{
	return g_Zpt.zeroPoint_ns;
}

//获取零点参数,单位点数
float Zpt_GetZptPtsNum(uint8_t fs) {
	return (g_Zpt.zeroPoint_ns / (1000.0 / fs));
}

////实际零点在波形起点后, 则为正, 在波形起点前, 则为负
////double Zpt_GetMeasWaveZptOffsetTimeNsByMks(void) {
//double Mks_GetZptOffsetTimeNsByMks(void) {
//	double mksOffsetTimeNs = 0;
////	double zptOffsetTimeNs = 0; //由零点带来的偏移, 算法中已做处理, 此处保持为0
//	
//	if(g_Mks.functionAvailabled && g_Mks.waveValid) {
//		mksOffsetTimeNs = g_Mks.measTimeNs - g_Mks.baseTimeNs;
//	}
//	return mksOffsetTimeNs;
////	return (mksOffsetTimeNs + zptOffsetTimeNs);
//}


//uint8_t Mks_Init(void)
//{
//	uint8_t res = 0;
//	float f32Tmp;
//	
//	g_Mks.functionAvailabled = 1;
//	g_Mks.waveValid = 0;
//	g_Mks.waveBufLen = sizeof(g_Mks.waveBuf)/sizeof(int16_t);
//	g_Mks.waveBgnPt = 0;
//	g_Mks.waveLenPt = MKS_WAVE_LEN;
//	g_Mks.pulNum = MKS_PUL_NUM;
//	g_Mks.fsMhz = 50; //此处 fsMhz 初始化无实际意义
//	g_Mks.feMhz = g_Emat.fe_MHz; //此处 feMhz 初始化无实际意义
//	g_Mks.baseTimeNs = MKS_DEFAULT_BASE_TIME_NS; //不作数
//	
//	res += DataPers_ReadMksBaseTime(&f32Tmp);
//	res += Mks_SetMksBaseTimeNs(f32Tmp);
//	return res;
//}

//void Mks_SetFunctionAvailability(uint8_t isAvailabled) {
//	g_Mks.functionAvailabled = isAvailabled;
//}

//uint8_t Mks_IsFunctionAvailabled(void) {
//	return g_Mks.functionAvailabled;
//}

//float Mks_GetBaseTimeNs(void) {
//	return g_Mks.baseTimeNs;
//}

//uint8_t Mks_GetPulNum(void) {
//	return g_Mks.pulNum;
//}

//uint8_t Mks_ResetToFactory(void)
//{
//	uint8_t res = 0;
//	float f32Tmp;
//	
//	res += DataPers_WriteMksBaseTime(MKS_DEFAULT_BASE_TIME_NS);
//	res += DataPers_ReadMksBaseTime(&f32Tmp);
//	res += Mks_SetMksBaseTimeNs(f32Tmp);
//	return res;
//}



//float Mks_CalcMksTimeNs(float baseTimeNs) {
//	//计算上升沿过零点
//	//计算范围: base-0.25*Te ~ base+0.75*Te
//	int32_t i;
//	uint8_t overRange = 0;
//	int32_t fs = g_Mks.fsMhz;
//	float fe = g_Mks.feMhz;
//	int32_t expectBgnPt = (baseTimeNs - 0.25*1000/fe) * fs / 1000;
//	int32_t expectEndPt = (baseTimeNs + 0.75*1000/fe) * fs / 1000;
//	int32_t realBgnPt = (expectBgnPt < 0) ? 0 : expectBgnPt;
//	int32_t realEndPt = (expectEndPt >= g_Mks.waveLenPt) ? g_Mks.waveLenPt : expectEndPt;

//	overRange = 1;
//	
//	for(i = realBgnPt + 1; i < realEndPt; i++) {
//		if(g_Mks.waveBuf[i] > 0 && g_Mks.waveBuf[i - 1] <= 0) {
//			overRange = 0;
//			break;
//		}
//	}
//	
//	if(overRange == 0) {
//		if((abs(g_Mks.waveBuf[i-1]) + abs(g_Mks.waveBuf[i])) == 0) {
//			return (i - 1) * 1000.0/fs;
//		}
//		else {
//			return (i - 1 + (double)abs(g_Mks.waveBuf[i-1]) / (abs(g_Mks.waveBuf[i-1]) + abs(g_Mks.waveBuf[i]))) * 1000.0/fs;
//		}
//	}
//	else {
//		return -1.0; //错误
//	}
//}

//uint8_t Mks_SetMksBaseTimeNs(float baseTimeNs) {
//	if(fabs(baseTimeNs - MKS_DEFAULT_BASE_TIME_NS) > 200) 
//		return 1; //error
//	
//	g_Mks.baseTimeNs = baseTimeNs;
//	return 0; //ok
//}


//uint8_t Mks_SetMksMeasTimeNs(float measTimeNs) {
//	if(fabs(measTimeNs - MKS_DEFAULT_BASE_TIME_NS) > 1000) 
//		return 1; //error
//	
//	g_Mks.measTimeNs = measTimeNs;
//	return 0; //ok
//}


//double Mks_GetMksMeasTimeNs(void) {
//	return (double)g_Mks.measTimeNs;
//}

/*********************** END OF FILE ********************************************/


