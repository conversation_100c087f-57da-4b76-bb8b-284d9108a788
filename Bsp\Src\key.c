#include "key.h"
#include "stm32f7xx_hal.h"
#include "delay.h"

uint8_t long_press_set = 0; // 识别长按M键出现长时间按的问题
static uint8_t mKeyPressed = 0;
static uint8_t upKeyPressed = 0;
static uint8_t downKeyPressed = 0;

//按键初始化函数
void KEY_Init(void) {
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	
	__HAL_RCC_GPIOC_CLK_ENABLE();
	
	GPIO_InitStruct.Pin = KEY_DOWN_PIN | KEY_UP_PIN | KEY_SET_PIN;
	GPIO_InitStruct.Mode = GPIO_MODE_INPUT;
	GPIO_InitStruct.Pull = GPIO_PULLUP;
	GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
	HAL_GPIO_Init(GPIOC, &GPIO_InitStruct);
}

void Key_SetMKeyPressed(void) {
	mKeyPressed = 1;
}

void Key_SetUpKeyPressed(void) {
	upKeyPressed = 1;
}

void Key_SetDownKeyPressed(void) {
	downKeyPressed = 1;
}

//按键处理函数
//返回按键值
// mode:0,不支持连续按;1,支持连续按(同时按);
// 0，没有任何按键按下
// 1，KEY0按下
// 2，KEY1按下
// 3，KEY2按下
// 4，WKUP按下 WK_UP
//注意此函数有响应优先级,KEY0>KEY1>KEY2>WK_UP!!
// mKeyPressed 的说明：当处于发射状态，为了避免查询按键时单击M键丢失，M键额外添加了外部中断触发标记，即 mKeyPressed=1。
uint8_t KEY_Scan(uint8_t mode) {
    uint16_t long_pres_cnt = 0;
    uint8_t key_val = 0;
    static uint8_t key_up = 1; //按键按松开标志
    if (mode)
        key_up = 1;//支持连按
	
    if ((mKeyPressed == 1) && (KEY_SET == 1)) { // 检查是否外部中断触发过M键，并且M键已经释放
        mKeyPressed = 0;
        return KEY_SET_PRES;
    }
	
	if ((upKeyPressed == 1) && (KEY_UP == 1)) { // 检查是否外部中断触发过M键，并且M键已经释放
        upKeyPressed = 0;
        return KEY_UP_PRES;
    }
	
	if ((downKeyPressed == 1) && (KEY_DOWN == 1)) { // 检查是否外部中断触发过M键，并且M键已经释放
        downKeyPressed = 0;
        return KEY_DOWN_PRES;
    }
	
    if (key_up && (KEY_SET == 0 || KEY_UP == 0 || KEY_DOWN == 0)) {
        delay_ms(10); //去抖动
        key_up = 0;
        if (KEY_SET == 0) {
            key_val = KEY_SET_PRES;
            mKeyPressed = 0; //当查询到M键时，清除掉外部中断的M键触发标志，避免双触发
        } 
		else if (KEY_UP == 0) {
            key_val = KEY_UP_PRES;
			upKeyPressed = 0;
		}
        else if (KEY_DOWN == 0) {
            key_val = KEY_DOWN_PRES;
			downKeyPressed = 0;
		}
		
        while (key_val != 0) {
            long_pres_cnt++;
            delay_ms(10);
            if (((KEY_SET == 1) && (KEY_UP == 1) && (KEY_DOWN == 1)) || (long_pres_cnt > 100)) {
                if (long_pres_cnt > 80) {
                    if (key_val == KEY_SET_PRES) {
                        if (long_press_set == 1) {
                            return 0;
                        } else {
                            long_press_set = 1; // 防止多次触发长按
                            return KEY_SET_LONG_PRES;
                        }
                    } else if (key_val == KEY_UP_PRES) {
                        if (long_press_set == 1) {
                            return 0;
                        } else {
                            long_press_set = 1; // 防止多次触发长按
                            return KEY_UP_LONG_PRES;
                        }
                    } else if (key_val == KEY_DOWN_PRES) {
                        if (long_press_set == 1) {
                            return 0;
                        } else {
                            long_press_set = 1; // 防止多次触发长按
                            return KEY_DOWN_LONG_PRES;
                        }
                    }

                } else {
                    return key_val;
                }
            }
        }

    } else if (KEY_SET == 1 && KEY_UP == 1 && KEY_DOWN == 1)
        key_up = 1;
    long_press_set = 0;

    return 0; // 无按键按下
}

////char g_Dev.deviceSN[15] = "PREMAT3#0001";
//// 奇下偶上 倒序两次 如：0001解码顺序 下上上上 下上上上
// uint8_t DismantlingUnlock(){
//	uint8_t i = 0;
//	uint8_t unlockOrder = 0; // 0up, 1down
//	unlockOrder = (((g_Dev.deviceSN[8] - '0') % 2)<<3) | (((g_Dev.deviceSN[9] - '0') % 2)<<2) | (((g_Dev.deviceSN[10] - '0') % 2)<<1) | (((g_Dev.deviceSN[11] - '0') % 2)<<0);
//	unlockOrder = (unlockOrder << 4) | unlockOrder;
//	for(i = 0; i < 8; ) {
//		if((unlockOrder & ( 1 << i)) != 0x00) {
//			while(1){
//				if(KEY_DOWN == 0) { // 对键
//					delay_ms(20);
//					if(KEY_DOWN == 0){
//						i++;
//						while(1) {
//							if(KEY_DOWN == 1) {
//								delay_ms(20);
//								if(KEY_DOWN == 1) {
//									break;
//								}
//							}
//						}
//						break;
//					}
//				}
//				else if(KEY_UP == 0){ //错键
//					delay_ms(20);
//					if(KEY_UP == 0){
//						i = 0;
//						while(1) {
//							if(KEY_UP == 1) {
//								delay_ms(20);
//								if(KEY_UP == 1) {
//									break;
//								}
//							}
//						}
//						break;
//					}
//				}
//			}
//		}
//		else {
//			while(1){
//				if(KEY_UP == 0) { // 对键
//					delay_ms(20);
//					if(KEY_UP == 0){
//						i++;
//						while(1) {
//							if(KEY_UP == 1) {
//								delay_ms(20);
//								if(KEY_UP == 1) {
//									break;
//								}
//							}
//						}
//						break;
//					}
//				}
//				else if(KEY_DOWN == 0){ //错键
//					delay_ms(20);
//					if(KEY_DOWN == 0){
//						i = 0;
//						while(1) {
//							if(KEY_DOWN == 1) {
//								delay_ms(20);
//								if(KEY_DOWN == 1) {
//									break;
//								}
//							}
//						}
//						break;
//					}
//				}
//			}
//		}
//	}
//}
