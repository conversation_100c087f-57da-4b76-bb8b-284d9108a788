#include "usart.h"
#include "delay.h"
#include "stm32f7xx_hal.h"
#include "bsp.h"

#if SYSTEM_SUPPORT_UCOS
#include "includes.h" //ucos 使用
#endif

#define RXBUFFERSIZE   1

static UART_HandleTypeDef huart2;
//串口1中断服务程序
//注意,读取USARTx->SR能避免莫名其妙的错误
uint8_t USART_RX_BUF[USART_REC_LEN]; //接收缓冲,最大USART_REC_LEN个字节.
//接收状态
// bit15，	接收完成标志
// bit14，	接收到0x0d
// bit13~0，	接收到的有效字节数目
uint16_t USART_RX_STA = 0; //接收状态标记
uint8_t aRxBuffer[RXBUFFERSIZE];//HAL库使用的串口接收缓冲

void Uart2_Init(uint32_t bound) {
	huart2.Instance = USART2;
	huart2.Init.BaudRate = bound;
	huart2.Init.WordLength = UART_WORDLENGTH_8B;
	huart2.Init.StopBits = UART_STOPBITS_1;
	huart2.Init.Parity = UART_PARITY_NONE;
	huart2.Init.Mode = UART_MODE_TX_RX;
	huart2.Init.HwFlowCtl = UART_HWCONTROL_NONE;
	huart2.Init.OverSampling = UART_OVERSAMPLING_16;
	huart2.Init.OneBitSampling = UART_ONE_BIT_SAMPLE_DISABLE;
	huart2.AdvancedInit.AdvFeatureInit = UART_ADVFEATURE_NO_INIT;
	if (HAL_UART_Init(&huart2) != HAL_OK)
	{
		Bsp_ErrorHandler();
	}
}

void HAL_UART_MspInit(UART_HandleTypeDef* uartHandle)
{
	GPIO_InitTypeDef GPIO_InitStruct = {0};
	RCC_PeriphCLKInitTypeDef PeriphClkInitStruct = {0};
  if(uartHandle->Instance==USART2)
  {
  /* USER CODE BEGIN USART2_MspInit 0 */

  /* USER CODE END USART2_MspInit 0 */

  /** Initializes the peripherals clock
  */
    PeriphClkInitStruct.PeriphClockSelection = RCC_PERIPHCLK_USART2;
    PeriphClkInitStruct.Usart2ClockSelection = RCC_USART2CLKSOURCE_PCLK1;
    if (HAL_RCCEx_PeriphCLKConfig(&PeriphClkInitStruct) != HAL_OK)
    {
//      Error_Handler();
    }

    /* USART2 clock enable */
    __HAL_RCC_USART2_CLK_ENABLE();

    __HAL_RCC_GPIOA_CLK_ENABLE();
    /**USART2 GPIO Configuration
    PA2     ------> USART2_TX
    PA3     ------> USART2_RX
    */
    GPIO_InitStruct.Pin = GPIO_PIN_2|GPIO_PIN_3;
    GPIO_InitStruct.Mode = GPIO_MODE_AF_PP;
    GPIO_InitStruct.Pull = GPIO_PULLUP;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_VERY_HIGH;
    GPIO_InitStruct.Alternate = GPIO_AF7_USART2;
    HAL_GPIO_Init(GPIOA, &GPIO_InitStruct);

  /* USER CODE BEGIN USART2_MspInit 1 */

  /* USER CODE END USART2_MspInit 1 */
  }
}


//串口2中断服务程序
void USART2_IRQHandler(void)                	
{ 
	uint32_t timeout=0;
	uint32_t maxDelay=0x1FFFF;
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		OSIntEnter();    
	#endif

	HAL_UART_IRQHandler(&huart2);	//调用HAL库中断处理公用函数

	timeout=0;
	while (HAL_UART_GetState(&huart2)!=HAL_UART_STATE_READY)//等待就绪
	{
		timeout++;////超时处理
		if(timeout>maxDelay) break;		
	}

	timeout=0;
	while(HAL_UART_Receive_IT(&huart2,(uint8_t *)aRxBuffer, RXBUFFERSIZE)!=HAL_OK)//一次处理完成之后，重新开启中断并设置RxXferCount为1
	{
		timeout++; //超时处理
		if(timeout>maxDelay) break;	
	}
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		OSIntExit();  											 
	#endif
} 

//接收完成处理回调函数
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	if(huart->Instance==USART1) {
		
	}
	else if(huart->Instance==USART2) {
		
	}
}

void Uart2_SendDatas(uint8_t *pTxdBuf, uint32_t len) {
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		CPU_SR_ALLOC();  	//必须要定义一个局部变量才能不报错
		OSIntEnter();    
	#endif
	for(uint32_t i = 0; i < len; i++) {
		HAL_UART_Transmit(&huart2, (uint8_t*)(pTxdBuf + i), 1, 1000);
		while(__HAL_UART_GET_FLAG(&huart2,UART_FLAG_TC)!=SET);		//等待发送结束
	}
	#if SYSTEM_SUPPORT_OS
		OSIntExit();  											 
	#endif
}

//void Uart2_SendOneByte(uint8_t val) {
//    USART_GetFlagStatus(USART2, USART_FLAG_TC);
//    USART_SendData(USART2, val);
//    while (USART_GetFlagStatus(USART2, USART_FLAG_TC) != SET)
//        ; //等待发送结束
//}

//void Uart2_SendDatas(uint8_t *buf, uint32_t len) {
//    for(uint32_t i = 0; i < len; i++) {
//		Uart2_SendOneByte(buf[i]);
//	}
//}
