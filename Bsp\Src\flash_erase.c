/**
  ********************************************************************************
  * Copyright (C), 2018-2024, 苏州博昇科技有限公司, www.phaserise.com
  * @file    : flash_erase.c
  * @project : 
  * @brief   : 
  * <AUTHOR> PR Team
  * @since   : 2024/08/04
  * @version : V1.0 
  * @history :
  *	2024/08/04: V0.1
  *	  1) 首次创建;
  *
  ********************************************************************************
  * @note
  * 
  ********************************************************************************
  * @attention
  * 
  ********************************************************************************
  */

#include "flash_erase.h"
#include "stmflash.h"


// #define ADDR_FLASH_SECTOR_0     ((uint32_t)0x08000000) //扇区0起始地址, 32 Kbytes  
// #define ADDR_FLASH_SECTOR_1     ((uint32_t)0x08008000) //扇区1起始地址, 32 Kbytes  
// #define ADDR_FLASH_SECTOR_2     ((uint32_t)0x08010000) //扇区2起始地址, 32 Kbytes  
// #define ADDR_FLASH_SECTOR_3     ((uint32_t)0x08018000) //扇区3起始地址, 32 Kbytes  
// #define ADDR_FLASH_SECTOR_4     ((uint32_t)0x08020000) //扇区4起始地址, 128 Kbytes  
// #define ADDR_FLASH_SECTOR_5     ((uint32_t)0x08040000) //扇区5起始地址, 256 Kbytes  
// #define ADDR_FLASH_SECTOR_6     ((uint32_t)0x08080000) //扇区6起始地址, 256 Kbytes  
// #define ADDR_FLASH_SECTOR_7     ((uint32_t)0x080C0000) //扇区7起始地址, 256 Kbytes 

void FlashErase_EraseProgram(void) {
	uint32_t t;
	uint32_t i = 0;
	uint32_t fwaddr = FLASH_APP_ADDR; //当前写入的地址
	uint32_t appSize = 0x080C0000 - 0x08010000 + 256*1024;
	uint32_t iapbuf[512];
	
	for(t = 0; t < appSize; t += 4)
	{
		iapbuf[i++] = 0;
		if(i == 512) {
			i = 0; 
			STMFLASH_Write(fwaddr, iapbuf, 512);
			fwaddr += 2048; //偏移2048  512*4=2048
		}
	} 
	
	if(i) STMFLASH_Write(fwaddr, iapbuf, i);//将最后的一些内容字节写进去.  
}


/******************************* end of file **********************************/
