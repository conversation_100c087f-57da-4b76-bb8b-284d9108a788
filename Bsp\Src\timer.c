#include "timer.h"
#include "stm32f7xx_hal.h"
#include "app.h"
#include "fpga.h"

TIM_HandleTypeDef TIM2_Handler; //定时器句柄 
TIM_HandleTypeDef TIM3_Handler; //定时器句柄 
TIM_HandleTypeDef TIM4_Handler; //定时器句柄 

extern uint8_t allowBatteryPercentCheckCnt;
uint8_t connectWDG_cnt = 0;

extern uint8_t forceUpdateThicknessVal_cnt;


void TIM2_Init(uint16_t arr,uint16_t psc)
{
	TIM2_Handler.Instance=TIM2;                          //通用定时器3
    TIM2_Handler.Init.Prescaler=psc;                     //分频
    TIM2_Handler.Init.CounterMode=TIM_COUNTERMODE_UP;    //向上计数器
    TIM2_Handler.Init.Period=arr;                        //自动装载值
    TIM2_Handler.Init.ClockDivision=TIM_CLOCKDIVISION_DIV1;//时钟分频因子
    HAL_TIM_Base_Init(&TIM2_Handler);
    HAL_TIM_Base_Stop(&TIM2_Handler);

	__HAL_TIM_CLEAR_IT(&TIM2_Handler, TIM_IT_UPDATE);
    HAL_TIM_Base_Start_IT(&TIM2_Handler);
	__HAL_TIM_CLEAR_IT(&TIM2_Handler, TIM_IT_UPDATE);
	HAL_TIM_Base_Stop(&TIM2_Handler);
	TIM2_Handler.Instance->CNT = 0;
}

//通用定时器3中断初始化
//arr：自动重装值。
//psc：时钟预分频数
//定时器溢出时间计算方法:Tout=((arr+1)*(psc+1))/Ft us.
//Ft=定时器工作频率,单位:Mhz
//这里使用的是定时器3!
void TIM3_Init(uint16_t arr,uint16_t psc)
{
	TIM3_Handler.Instance=TIM3;                          //通用定时器3
    TIM3_Handler.Init.Prescaler=psc;                     //分频
    TIM3_Handler.Init.CounterMode=TIM_COUNTERMODE_UP;    //向上计数器
    TIM3_Handler.Init.Period=arr;                        //自动装载值
    TIM3_Handler.Init.ClockDivision=TIM_CLOCKDIVISION_DIV1;//时钟分频因子
    HAL_TIM_Base_Init(&TIM3_Handler);
    HAL_TIM_Base_Stop(&TIM3_Handler);

	__HAL_TIM_CLEAR_IT(&TIM3_Handler, TIM_IT_UPDATE);
    HAL_TIM_Base_Start_IT(&TIM3_Handler); //使能定时器3和定时器3中断   
	__HAL_TIM_CLEAR_IT(&TIM3_Handler, TIM_IT_UPDATE);
	HAL_TIM_Base_Stop(&TIM3_Handler);
	TIM3_Handler.Instance->CNT = 0;
	
}

void TIM4_Init(uint16_t arr,uint16_t psc)
{  
    TIM4_Handler.Instance=TIM4;                          //通用定时器3
    TIM4_Handler.Init.Prescaler=psc;                     //分频
    TIM4_Handler.Init.CounterMode=TIM_COUNTERMODE_UP;    //向上计数器
    TIM4_Handler.Init.Period=arr;                        //自动装载值
    TIM4_Handler.Init.ClockDivision=TIM_CLOCKDIVISION_DIV1;//时钟分频因子
    HAL_TIM_Base_Init(&TIM4_Handler);
    HAL_TIM_Base_Stop(&TIM4_Handler);
    HAL_TIM_Base_Start_IT(&TIM4_Handler); //停止定时器3和定时器3更新中断：TIM_IT_UPDATE    
}


//此函数会被HAL_TIM_Base_Init()函数调用
void HAL_TIM_Base_MspInit(TIM_HandleTypeDef *htim)
{
	if(htim->Instance==TIM2)
	{
		__HAL_RCC_TIM2_CLK_ENABLE();            //使能TIM3时钟
		HAL_NVIC_SetPriority(TIM2_IRQn, 3, 3);    //设置中断优先级，抢占优先级1，子优先级3
		HAL_NVIC_EnableIRQ(TIM2_IRQn);          //开启ITM3中断   
	}
	else if(htim->Instance==TIM3)
	{
		__HAL_RCC_TIM3_CLK_ENABLE();            //使能TIM3时钟
		HAL_NVIC_SetPriority(TIM3_IRQn, 1, 3);    //设置中断优先级，抢占优先级1，子优先级3
		HAL_NVIC_EnableIRQ(TIM3_IRQn);          //开启ITM3中断   
	}
	else if(htim->Instance==TIM4)
	{ 
		__HAL_RCC_TIM4_CLK_ENABLE();            //使能TIM4时钟
		HAL_NVIC_SetPriority(TIM4_IRQn,2,3);    //设置中断优先级，抢占优先级2，子优先级0
		HAL_NVIC_EnableIRQ(TIM4_IRQn);          //开启ITM4中断
	}  
}

void TIM2_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&TIM2_Handler);
}

//定时器3中断服务函数
void TIM3_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&TIM3_Handler);
}

//定时器4中断服务函数
void TIM4_IRQHandler(void)
{
    HAL_TIM_IRQHandler(&TIM4_Handler);
}

//定时器3中断服务函数调用
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		OSIntEnter();    
	#endif
	if(htim==(&TIM2_Handler))
	{
		connectWDG_cnt++;

		g_Dev.keepConnecttedWDG_Action = 1;
		connectWDG_cnt = 0;
		__HAL_TIM_CLEAR_IT(&TIM2_Handler, TIM_IT_UPDATE);
	}
	else if(htim==(&TIM3_Handler))
	{
		g_Dev.allowOledShow = 1;
		allowBatteryPercentCheckCnt++;
		__HAL_TIM_CLEAR_IT(&TIM3_Handler, TIM_IT_UPDATE);
	}
	else if(htim==(&TIM4_Handler))
	{
		Fpga_EmitTaskForTimer();
		__HAL_TIM_CLEAR_IT(&TIM4_Handler, TIM_IT_UPDATE);
	}
	#if SYSTEM_SUPPORT_OS	 	//使用OS
		OSIntExit();  											 
	#endif
}

void Start_KeepConnect_WDG(void) {
	connectWDG_cnt = 0;

	TIM2_Handler.Instance = TIM2;
	TIM2_Handler.Instance->CNT = 0;
	HAL_TIM_Base_Start(&TIM2_Handler);
}

void Close_KeepConnect_WDG(void) {
	connectWDG_cnt = 0;
	g_Dev.keepConnecttedWDG_Action = 0;
	
	TIM2_Handler.Instance = TIM2;
	TIM2_Handler.Instance->CNT = 0;
	HAL_TIM_Base_Stop(&TIM2_Handler);
}

void Feed_KeepConnect_WDG(void) {
	connectWDG_cnt = 0;
	TIM2_Handler.Instance->CNT = 0;
}

// 设置重复次数所对应的定时器参数
void EmitTimer_SetIntervalTimeMsAndStart(uint32_t us) {
	if(0 < us && us < 100) return;
	
	if(us == 0) {
		HAL_TIM_Base_Stop(&TIM4_Handler);
		TIM4->CNT = 0;
	}
	else {
		HAL_TIM_Base_Stop(&TIM4_Handler);
		TIM4->ARR = us - 1;
		TIM4->CNT = 0;
		HAL_TIM_Base_Start(&TIM4_Handler);
	}
}

void EmitTimer_StopTimer(void) {
	HAL_TIM_Base_Stop(&TIM4_Handler);
	TIM4->CNT = 0;
}


