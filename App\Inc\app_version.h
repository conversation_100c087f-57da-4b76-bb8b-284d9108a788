/**
  ******************************************************************************
  * @file    app_version.h
  * @brief   This file contains all the function prototypes for
  *          the app_version.c file
  ******************************************************************************
  * @attention
  *
  ******************************************************************************
  */
#ifndef __APP_VERSION_H__
#define __APP_VERSION_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/


//u32 g_Dev.firmWareVersion = 18092814;
//#define FIRMWARE_VERSION 920210819//920200402;//20200401;//20030601;//20011401;//19090101;//19081201;//19041201;//19011801;//18122801;//18122801开始,wifi版本更新为第二版
//#define FIRMWARE_VERSION 920220413 //220413
//#define FIRMWARE_VERSION 0x02002201 //220413
//#define FIRMWARE_VERSION 0x02012202 //V2.1(2202)  221128
//#define FIRMWARE_VERSION 2012202 //V2.1(2202)  221129
//#define FIRMWARE_VERSION 3002401 //V3.0(2401)  20240802
//#define FIRMWARE_VERSION 3002402 //V3.0(2402)  20240816
//#define FIRMWARE_VERSION 3002403 //V3.0(2403)  20240914
//#define FIRMWARE_VERSION 3002404 //V3.0(2404)  20241119
//#define FIRMWARE_VERSION 3002405 //V3.0(2405)  20241204
//#define FIRMWARE_VERSION 3002406 //V3.0(2406)  20241218
#define FIRMWARE_VERSION 3002501 //V3.0(2501)  20250225

//发布版本使能
#define IS_RELEASE		1 //1
#define IS_DEBUG		(!IS_RELEASE)

//调试宏：自动计划测试. 正式版本此宏需要为0
#if IS_RELEASE
	#define DBG_MKS								0 //调试MKS(240506)
#else
	#define DBG_MKS								0 //调试MKS(240506)
#endif


void AppVersion_Init(void);

#ifdef __cplusplus
}
#endif
#endif

/******************************* end of file **********************************/