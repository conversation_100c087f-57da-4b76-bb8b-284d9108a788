/**
  ******************************************************************************
  * @file    flash_erase.h
  * @brief   This file contains all the function prototypes for
  *          the flash_erase.c file
  ******************************************************************************
  * @attention
  *
  ******************************************************************************
  */
#ifndef __FLASH_ERASE_H__
#define __FLASH_ERASE_H__

#ifdef __cplusplus
extern "C" {
#endif
#include <stdint.h>
#include <stdint.h>

void FlashErase_EraseProgram(void);
#ifdef __cplusplus
}
#endif
#endif

/******************************* end of file **********************************/