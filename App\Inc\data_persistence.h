/**
  ******************************************************************************
  * @file    data_persistence.h
  * @brief   This file contains all the function prototypes for
  *          the data_persistence.c file
  ******************************************************************************
  * @attention
  *
  ******************************************************************************
  */
  
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __DATA_PERSISTENCE_H__
#define __DATA_PERSISTENCE_H__

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>


uint8_t DataPersistence_Init(void);
uint32_t DataPersistence_ReadAllDatas(void);
uint32_t DataPersistence_SaveAllDatas(uint8_t isForced);
uint32_t DataPersistence_SaveAllDatasForced(void);

uint8_t DataPersistence_SaveSelfLockVal(uint32_t val);
uint8_t DataPersistence_ReadSelfLockVal(uint32_t *retVal);



uint8_t DataPers_WriteWifiEnable(uint32_t enable);
uint8_t DataPers_ReadWifiEnable(uint32_t *retVal);

uint8_t DataPers_WriteSnCode(char *strSnCode);
uint8_t DataPers_ReadSnCode(char *retSnCode, uint32_t size);

uint8_t DataPers_ReadUidEncode(uint32_t *retEncode);
uint8_t DataPers_WriteUidEncode(uint32_t encode);

#ifdef __cplusplus
}
#endif
#endif

/******************************* end of file **********************************/