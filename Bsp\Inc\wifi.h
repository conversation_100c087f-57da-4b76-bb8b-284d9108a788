#ifndef __WIFI_H
#define __WIFI_H
#include <stdint.h>

//#if WIFI_VERSION_2TH //第二版WIFI 
///*第二版WIFI引脚*/
//#define WIFI_EN PAout(2)	// DS0
//#define WIFI_RST PEout(7)
//#define WIFI_CS PAout(15)	// DS0

//#define WIFI_BUSY PEin(11)// ESP8266-IO4
//#define WIFI_NEED_READ_SPI PEin(9)// ESP8266-IO2
//#else
///*第三版WIFI引脚*/
////SCLK: PB3; MISO: PB4; MOSI: PB5; CS: PD0;
////EN: PD6; RST: PD3; BUSY: PA12; NEED_READ(DATA_VALID): PC10;

//#define WIFI_EN PDout(6)
//#define WIFI_RST PDout(3)
//#define WIFI_CS PDout(0)

//#define WIFI_BUSY PAin(12) // ESP8266-IO4
//#define WIFI_NEED_READ_SPI PCin(10) // ESP8266-IO2
//#endif

typedef struct {
//	uint8_t enable;
//	uint8_t connectedWithApp;
	uint8_t adcDataBitsToApp;
	uint8_t sendThicknessOnly; // 0: 发送完整数据包(默认); 1: 只发送厚度值
} Wifi_t;

extern Wifi_t g_Wifi;

//指令码
#define CMD_KEEPCONNECT		0x03
#define CMD_CALI			0x14
#define CMD_EMAT_FREQ		0x16
#define CMD_EMAT_PULSE_NUM	0x17
#define CMD_CALI_CANCEL		0x18
#define CMD_MOTION			0x20
#define CMD_PROBE 			0x21
#define CMD_CAMERA 			0x22
#define CMD_GAUGE_MODE 		0x23
#define CMD_SPEED 			0x24


void Wifi_Init(void);
uint8_t Wifi_ReceiveData(void);
void Wifi_SendData(uint16_t *buf, uint16_t length);
void CloseWifi(void);
void OpenWifi(void);
uint8_t getWifiState();
uint8_t WifiCmdPolling(uint8_t priority);
uint8_t WifiCmdExe(void);
void WifiSendCmd(uint8_t *buf);
//void TST_SendData(void);
void updatePackageParameter(uint32_t thickness, uint16_t sampRate, uint16_t velocity, uint16_t rep, uint16_t ultraSonicFreq);
void setSsidPassword(char *ssid, char *password);
void setAppIpAddress(int ipAddress);
void AppBreakConnectHandler();
void SendBatteryPercentCmd2App(uint8_t val);
void setESP8266_Ssid_Pwd();
void SendCaliProgCmd2App(uint8_t progress);
void setESP8266DebugMode(uint8_t isOpen);
void SendThicknessValueCmd2App(uint32_t thicknessUm, uint8_t isValid);



void SendThicknessValue2App(uint32_t thickness);
void SimulateWifiAutoExecution(void);
static void BuildSimulatedCommand(uint8_t cmdType, uint16_t param1, uint16_t param2, uint16_t param3, uint16_t param4);
uint8_t ExecuteSimulatedWifiCommand(uint8_t cmdType, uint16_t param1, uint16_t param2, uint16_t param3, uint16_t param4);


#endif
