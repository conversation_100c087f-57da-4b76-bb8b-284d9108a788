/**
  ******************************************************************************
  * @file    bsp_led.h
  * @brief   This file contains all the function prototypes for
  *          the bsp_led.c file
  ******************************************************************************
  * @attention
  *
  ******************************************************************************
  */
#ifndef __BSP_LED_H__
#define __BSP_LED_H__

#ifdef __cplusplus
extern "C" {
#endif

/* Includes ------------------------------------------------------------------*/
//#include "main.h"

#define BspLed(on) HAL_GPIO_WritePin(GPIOA, GPIO_PIN_11, on ? GPIO_PIN_SET : GPIO_PIN_RESET)

void BspLed_Init(void);


#ifdef __cplusplus
}
#endif
#endif

/******************************* end of file **********************************/